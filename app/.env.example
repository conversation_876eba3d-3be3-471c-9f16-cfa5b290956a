APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# SSE (Server-Sent Events) 설정
SSE_REDIS_PREFIX=sse:
SSE_REDIS_CONNECTION=default
SSE_REDIS_KEY_TTL=3600
SSE_MAX_TOTAL_CONNECTIONS=1000
SSE_MAX_CONNECTIONS_PER_USER=5
SSE_MAX_CONNECTIONS_PER_IP=10
SSE_CONNECTION_TIMEOUT=300
SSE_HEARTBEAT_INTERVAL=30
SSE_MESSAGE_BATCH_SIZE=100
SSE_MESSAGE_RETRY_ATTEMPTS=3
SSE_MESSAGE_RETRY_DELAY=1000
SSE_MAX_MESSAGE_SIZE=65536
SSE_COMPRESSION_ENABLED=true
SSE_OFFLINE_NOTIFICATIONS_ENABLED=true
SSE_MAX_OFFLINE_NOTIFICATIONS_PER_USER=100
SSE_OFFLINE_NOTIFICATIONS_RETENTION_DAYS=7
SSE_OFFLINE_NOTIFICATIONS_CLEANUP_INTERVAL=3600
SSE_RATE_LIMIT_PER_MINUTE=60
SSE_CSRF_PROTECTION=true
SSE_IP_WHITELIST=
SSE_REQUIRE_AUTHENTICATION=false
SSE_STATISTICS_ENABLED=true
SSE_LOG_LEVEL=info
SSE_PERFORMANCE_TRACKING=true
SSE_ERROR_REPORTING=true
SSE_PUSHER_FALLBACK_ENABLED=false
SSE_HYBRID_MODE=false
SSE_MIGRATION_PERCENTAGE=0

# 중복 QAID 리포트 메일 수신자 (쉼표로 구분)
DUPLICATE_QAID_MAIL_RECIPIENTS=<EMAIL>,<EMAIL>
