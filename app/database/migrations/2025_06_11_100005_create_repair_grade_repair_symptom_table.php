<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_grade_repair_symptom', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_grade_id')->constrained('repair_grades')->onDelete('cascade');
            $table->foreignId('repair_symptom_id')->constrained('repair_symptoms')->onDelete('cascade');
            $table->timestamps();

            // 중복 관계 방지를 위한 유니크 인덱스
            $table->unique(['repair_grade_id', 'repair_symptom_id'], 'grade_symptom_unique');
            
            // 성능 향상을 위한 인덱스
            $table->index('repair_grade_id');
            $table->index('repair_symptom_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_grade_repair_symptom');
    }
};
