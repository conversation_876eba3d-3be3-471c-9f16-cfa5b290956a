<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monitor_rules', function (Blueprint $table) {
            $table->id();
            $table->enum('rule_type', ['brand', 'exclude', 'size_pattern'])
                  ->comment('규칙 유형 (brand: 브랜드 키워드, exclude: 제외 키워드, size_pattern: 크기 추출 패턴)');
            $table->text('pattern')->comment('패턴 문자열 (키워드 또는 정규식)');
            $table->string('description')->comment('규칙 설명');
            $table->integer('priority')->default(0)->comment('우선순위 (낮은 숫자가 높은 우선순위)');
            $table->boolean('is_active')->default(true)->comment('활성화 상태');
            $table->timestamps();
            
            // 인덱스 추가
            $table->index(['rule_type', 'is_active']);
            $table->index(['priority', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monitor_rules');
    }
};
