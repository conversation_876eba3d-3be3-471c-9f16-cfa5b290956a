<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * SSE 오프라인 알림 테이블 생성
     * 사용자가 오프라인 상태일 때 전송된 알림을 저장하고
     * 로그인 시 미전달 알림을 전송하기 위한 테이블
     */
    public function up(): void
    {
        Schema::create('sse_offline_notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('알림 대상 사용자 ID');
            $table->string('type', 50)->comment('알림 타입 (notification, data_update 등)');
            $table->json('data')->comment('알림 데이터 (JSON 형태)');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('알림 생성 시간');
            $table->timestamp('delivered_at')->nullable()->comment('알림 전달 완료 시간');
            
            // 인덱스 설정
            $table->index('user_id', 'idx_user_id');
            $table->index('created_at', 'idx_created_at');
            $table->index(['user_id', 'delivered_at'], 'idx_user_delivered');
            
            // 외래키 제약조건
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sse_offline_notifications');
    }
};
