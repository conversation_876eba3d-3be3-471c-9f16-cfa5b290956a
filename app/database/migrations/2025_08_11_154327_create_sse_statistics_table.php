<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * SSE 통계 테이블 생성
     * SSE 연결 및 메시지 전송 통계를 일별로 수집하여
     * 모니터링 및 성능 분석에 활용하기 위한 테이블
     */
    public function up(): void
    {
        Schema::create('sse_statistics', function (Blueprint $table) {
            $table->id();
            $table->date('date')->unique()->comment('통계 수집 날짜');
            $table->unsignedInteger('total_connections')->default(0)->comment('총 연결 수');
            $table->unsignedInteger('authenticated_connections')->default(0)->comment('인증된 연결 수');
            $table->unsignedInteger('guest_connections')->default(0)->comment('게스트 연결 수');
            $table->unsignedInteger('messages_sent')->default(0)->comment('전송된 메시지 수');
            $table->unsignedInteger('failed_messages')->default(0)->comment('전송 실패 메시지 수');
            $table->unsignedInteger('peak_concurrent_connections')->default(0)->comment('최대 동시 연결 수');
            $table->decimal('average_connection_duration', 8, 2)->default(0)->comment('평균 연결 지속 시간(분)');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
            
            // 인덱스 설정
            $table->index('date', 'idx_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sse_statistics');
    }
};
