<?php

namespace Database\Factories;

use App\Models\RepairCost;
use App\Models\RepairCostRange;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairCost>
 */
class RepairCostFactory extends Factory
{
    protected $model = RepairCost::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $repairType = $this->faker->randomElement(array_keys(RepairCost::getRepairTypes()));
        
        // 수리 유형에 따른 기본 금액 범위 설정
        $baseAmount = match($repairType) {
            RepairCost::REPAIR_TYPE_PARTS => $this->faker->numberBetween(30000, 150000),
            RepairCost::REPAIR_TYPE_CLEANING => $this->faker->numberBetween(20000, 80000),
            RepairCost::REPAIR_TYPE_SOFTWARE => $this->faker->numberBetween(15000, 50000),
            RepairCost::REPAIR_TYPE_OTHER => $this->faker->numberBetween(25000, 100000),
            RepairCost::REPAIR_TYPE_INSPECTION => $this->faker->numberBetween(10000, 40000),
            default => $this->faker->numberBetween(20000, 80000)
        };

        // 1000원 단위로 반올림
        $amount = round($baseAmount / 1000) * 1000;

        return [
            'repair_cost_range_id' => RepairCostRange::factory(),
            'repair_type' => $repairType,
            'amount' => $amount
        ];
    }

    /**
     * 부품교체 수리비 상태
     */
    public function repairParts(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCost::REPAIR_TYPE_PARTS,
            'amount' => $this->faker->numberBetween(30, 150) * 1000 // 3만원~15만원
        ]);
    }

    /**
     * 세척 수리비 상태
     */
    public function repairCleaning(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCost::REPAIR_TYPE_CLEANING,
            'amount' => $this->faker->numberBetween(20, 80) * 1000 // 2만원~8만원
        ]);
    }

    /**
     * 소프트웨어 수리비 상태
     */
    public function repairSoftware(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCost::REPAIR_TYPE_SOFTWARE,
            'amount' => $this->faker->numberBetween(15, 50) * 1000 // 1.5만원~5만원
        ]);
    }

    /**
     * 기타 수리비 상태
     */
    public function repairOther(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCost::REPAIR_TYPE_OTHER,
            'amount' => $this->faker->numberBetween(25, 100) * 1000 // 2.5만원~10만원
        ]);
    }

    /**
     * 검수 수리비 상태
     */
    public function inspection(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCost::REPAIR_TYPE_INSPECTION,
            'amount' => $this->faker->numberBetween(10, 40) * 1000 // 1만원~4만원
        ]);
    }

    /**
     * 특정 범위에 속한 수리비 상태
     */
    public function forRange(RepairCostRange $range): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_cost_range_id' => $range->id
        ]);
    }

    /**
     * 특정 금액 상태
     */
    public function withAmount(int $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount
        ]);
    }

    /**
     * 저가 수리비 상태 (1-3만원)
     */
    public function lowCost(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $this->faker->numberBetween(10, 30) * 1000
        ]);
    }

    /**
     * 중가 수리비 상태 (3-8만원)
     */
    public function midCost(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $this->faker->numberBetween(30, 80) * 1000
        ]);
    }

    /**
     * 고가 수리비 상태 (8-20만원)
     */
    public function highCost(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $this->faker->numberBetween(80, 200) * 1000
        ]);
    }

    /**
     * 완전한 수리비 세트 생성 (모든 수리 유형)
     */
    public function completeSet(RepairCostRange $range): array
    {
        $costs = [];
        foreach (array_keys(RepairCost::getRepairTypes()) as $repairType) {
            $costs[] = $this->forRange($range)->state([
                'repair_type' => $repairType
            ])->make();
        }
        return $costs;
    }
}