<?php

namespace Database\Factories;

use App\Models\Cate4;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cate4>
 */
class Cate4Factory extends Factory
{
    protected $model = Cate4::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'TV',
            '모니터',
            '노트북',
            '데스크톱',
            '태블릿',
            '스마트폰',
            '애플제품',
            '게임기',
            '오디오',
            '카메라'
        ];

        return [
            'name' => $this->faker->randomElement($categories)
        ];
    }

    /**
     * TV 카테고리 상태
     */
    public function tv(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'TV'
        ]);
    }

    /**
     * 모니터 카테고리 상태
     */
    public function monitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '모니터'
        ]);
    }

    /**
     * 노트북 카테고리 상태
     */
    public function laptop(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '노트북'
        ]);
    }

    /**
     * 데스크톱 카테고리 상태
     */
    public function desktop(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '데스크톱'
        ]);
    }

    /**
     * 애플 제품 카테고리 상태
     */
    public function apple(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '애플제품'
        ]);
    }
}