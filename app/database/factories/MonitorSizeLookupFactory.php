<?php

namespace Database\Factories;

use App\Models\MonitorSizeLookup;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\MonitorSizeLookup>
 */
class MonitorSizeLookupFactory extends Factory
{
    protected $model = MonitorSizeLookup::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->words(3, true);
        $unit = $this->faker->randomElement(['INCH', 'CM']);
        $size = $unit === 'INCH'
            ? $this->faker->randomFloat(2, 13, 55)
            : $this->faker->randomFloat(2, 33, 140);

        return [
            'name' => $name,
            'brand' => $this->faker->randomElement(['brand', 'general']),
            'size' => $size,
            'unit' => $unit,
            'name_hash' => md5($name),
        ];
    }
}


