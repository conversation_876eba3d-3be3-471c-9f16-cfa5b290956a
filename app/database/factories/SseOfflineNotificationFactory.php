<?php

namespace Database\Factories;

use App\Models\SseOfflineNotification;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * SSE 오프라인 알림 팩토리
 */
class SseOfflineNotificationFactory extends Factory
{
    /**
     * 모델 클래스명
     */
    protected $model = SseOfflineNotification::class;

    /**
     * 모델의 기본 상태 정의
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(['notification', 'data_update', 'system_status']),
            'data' => [
                'title' => $this->faker->sentence(3),
                'message' => $this->faker->paragraph(2),
                'priority' => $this->faker->randomElement(['low', 'normal', 'high']),
                'category' => $this->faker->randomElement(['system', 'user', 'data']),
                'metadata' => [
                    'source' => $this->faker->randomElement(['admin', 'system', 'auto']),
                    'timestamp' => $this->faker->dateTimeThisMonth()->format('Y-m-d H:i:s')
                ]
            ],
            'created_at' => $this->faker->dateTimeThisMonth(),
            'delivered_at' => null,
        ];
    }

    /**
     * 전달 완료된 알림 상태
     */
    public function delivered(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'delivered_at' => $this->faker->dateTimeBetween($attributes['created_at'], 'now'),
            ];
        });
    }

    /**
     * 미전달 알림 상태 (기본값이지만 명시적으로 설정)
     */
    public function undelivered(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'delivered_at' => null,
            ];
        });
    }

    /**
     * 특정 타입의 알림
     */
    public function ofType(string $type): static
    {
        return $this->state(function (array $attributes) use ($type) {
            $data = match ($type) {
                'notification' => [
                    'title' => $this->faker->sentence(3),
                    'message' => $this->faker->paragraph(1),
                    'priority' => $this->faker->randomElement(['low', 'normal', 'high']),
                    'action_url' => $this->faker->optional()->url(),
                ],
                'data_update' => [
                    'model' => $this->faker->randomElement(['categories', 'products', 'users']),
                    'action' => $this->faker->randomElement(['created', 'updated', 'deleted']),
                    'payload' => [
                        'id' => $this->faker->numberBetween(1, 1000),
                        'changes' => $this->faker->words(3),
                    ],
                ],
                'system_status' => [
                    'status' => $this->faker->randomElement(['maintenance', 'online', 'degraded']),
                    'message' => $this->faker->sentence(),
                    'estimated_duration' => $this->faker->optional()->numberBetween(5, 120),
                ],
                default => $attributes['data'] ?? []
            };

            return [
                'type' => $type,
                'data' => $data,
            ];
        });
    }

    /**
     * 오래된 알림 (정리 테스트용)
     */
    public function old(int $daysOld = 30): static
    {
        return $this->state(function (array $attributes) use ($daysOld) {
            $createdAt = Carbon::now()->subDays($daysOld);
            
            return [
                'created_at' => $createdAt,
                'delivered_at' => $this->faker->boolean(70) 
                    ? $this->faker->dateTimeBetween($createdAt, $createdAt->copy()->addDays(7))
                    : null,
            ];
        });
    }

    /**
     * 높은 우선순위 알림
     */
    public function highPriority(): static
    {
        return $this->state(function (array $attributes) {
            $data = $attributes['data'] ?? [];
            $data['priority'] = 'high';
            
            return [
                'data' => $data,
            ];
        });
    }

    /**
     * 특정 사용자의 알림
     */
    public function forUser(User $user): static
    {
        return $this->state(function (array $attributes) use ($user) {
            return [
                'user_id' => $user->id,
            ];
        });
    }
}