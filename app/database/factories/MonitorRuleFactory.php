<?php

namespace Database\Factories;

use App\Models\MonitorRule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MonitorRule>
 */
class MonitorRuleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MonitorRule::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ruleTypes = [
            MonitorRule::RULE_TYPE_BRAND,
            MonitorRule::RULE_TYPE_EXCLUDE,
            MonitorRule::RULE_TYPE_SIZE_PATTERN
        ];

        $ruleType = $this->faker->randomElement($ruleTypes);

        // 규칙 유형에 따른 패턴 생성
        $pattern = $this->generatePatternByType($ruleType);

        return [
            'rule_type' => $ruleType,
            'pattern' => $pattern,
            'description' => $this->faker->sentence(),
            'priority' => $this->faker->numberBetween(1, 100),
            'is_active' => $this->faker->boolean(80), // 80% 확률로 활성화
        ];
    }

    /**
     * 브랜드 규칙 상태
     */
    public function brand(): static
    {
        return $this->state(fn (array $attributes) => [
            'rule_type' => MonitorRule::RULE_TYPE_BRAND,
            'pattern' => $this->faker->randomElement(['LG', '삼성', '엘지', 'samsung', 'lg전자']),
            'description' => '브랜드 키워드: ' . $this->faker->word(),
        ]);
    }

    /**
     * 제외 규칙 상태
     */
    public function exclude(): static
    {
        return $this->state(fn (array $attributes) => [
            'rule_type' => MonitorRule::RULE_TYPE_EXCLUDE,
            'pattern' => $this->faker->randomElement(['LG패널', '삼성패널', '정품패널', '대기업']),
            'description' => '제외 키워드: ' . $this->faker->word(),
        ]);
    }

    /**
     * 크기 패턴 규칙 상태
     */
    public function sizePattern(): static
    {
        return $this->state(fn (array $attributes) => [
            'rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN,
            'pattern' => $this->faker->randomElement([
                '/\b(\d{2,3})\s*인치\b/i',
                '/\b(\d{2,3})\s*inch\b/i',
                '/\b[A-Z]{2,3}(\d{2})[A-Z0-9]\d+[A-Z]\b/i'
            ]),
            'description' => '크기 추출 패턴: ' . $this->faker->sentence(),
        ]);
    }

    /**
     * 활성화된 규칙 상태
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * 비활성화된 규칙 상태
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * 규칙 유형에 따른 패턴 생성
     */
    private function generatePatternByType(string $ruleType): string
    {
        return match ($ruleType) {
            MonitorRule::RULE_TYPE_BRAND => $this->faker->randomElement([
                'LG', '삼성', '엘지', 'samsung', 'lg전자', '삼성전자'
            ]),
            MonitorRule::RULE_TYPE_EXCLUDE => $this->faker->randomElement([
                'LG패널', '삼성패널', '정품패널', '대기업', 'LG정품', '엘지정품'
            ]),
            MonitorRule::RULE_TYPE_SIZE_PATTERN => $this->faker->randomElement([
                '/\b(\d{2,3})\s*인치\b/i',
                '/\b(\d{2,3})\s*inch\b/i',
                '/\b[A-Z]{2,3}(\d{2})[A-Z0-9]\d+[A-Z]\b/i',
                '/\bULTRON\s?(\d{2})\d{2}\b/i'
            ]),
            default => $this->faker->word(),
        };
    }
} 