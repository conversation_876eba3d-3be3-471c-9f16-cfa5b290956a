<?php

namespace Database\Factories;

use App\Models\RepairCostPolicy;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairCostPolicy>
 */
class RepairCostPolicyFactory extends Factory
{
    protected $model = RepairCostPolicy::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $systems = [
            [
                'name' => 'monitor',
                'display_name' => '모니터',
                'pricing_type' => RepairCostPolicy::PRICING_TYPE_SIZE,
                'description' => '모니터 제품의 크기별 수리비 관리'
            ],
            [
                'name' => 'apple',
                'display_name' => '애플 제품',
                'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
                'description' => '애플 제품의 모델별 수리비 관리'
            ],
            [
                'name' => 'general',
                'display_name' => '일반 제품',
                'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
                'description' => '일반 제품의 판매가별 수리비 관리'
            ],
            [
                'name' => 'software',
                'display_name' => '소프트웨어 설치',
                'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
                'description' => '소프트웨어 설치 작업의 공통 수리비 관리'
            ]
        ];

        $system = $this->faker->randomElement($systems);

        return [
            'name' => $system['name'],
            'display_name' => $system['display_name'],
            'description' => $system['description'],
            'pricing_type' => $system['pricing_type'],
            'is_active' => $this->faker->boolean(90) // 90% 확률로 활성화
        ];
    }

    /**
     * 모니터 시스템 상태
     */
    public function monitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'monitor',
            'display_name' => '모니터',
            'pricing_type' => RepairCostPolicy::PRICING_TYPE_SIZE,
            'description' => '모니터 제품의 크기별 수리비 관리',
            'is_active' => true
        ]);
    }

    /**
     * 애플 제품 시스템 상태
     */
    public function apple(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'apple',
            'display_name' => '애플 제품',
            'pricing_type' => RepairCostPolicy::PRICING_TYPE_MODEL,
            'description' => '애플 제품의 모델별 수리비 관리',
            'is_active' => true
        ]);
    }

    /**
     * 일반 제품 시스템 상태
     */
    public function general(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'general',
            'display_name' => '일반 제품',
            'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
            'description' => '일반 제품의 판매가별 수리비 관리',
            'is_active' => true
        ]);
    }

    /**
     * 소프트웨어 설치 시스템 상태
     */
    public function software(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'software',
            'display_name' => '소프트웨어 설치',
            'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
            'description' => '소프트웨어 설치 작업의 공통 수리비 관리',
            'is_active' => true
        ]);
    }

    /**
     * 비활성화 상태
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false
        ]);
    }
}
