<?php

namespace App\Traits\Repair;

use App\Models\Product;
use App\Models\RepairFee;
use App\Services\TelegramService;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

trait MonitorTrait
{
    /**
     * 모니터 제품인지 확인($category ID가 328번, 329번 고정임)
     * @param Product $product
     * @return bool
     */
    public function isMonitorProduct(Product $product): bool
    {
        return ($product->cate4->name === 'TV' && $product->cate5->name === 'TV') ||
            ($product->cate4->name === '컴퓨터주변기기' && $product->cate5->name === '모니터');
    }

    /**
     * 모니터 수리비 모델명 결정<br>
     * - 삼성, LG: brand<br>
     * - 그 외: etc
     */
    public function getMonitorModel(Product $product): string
    {
        /*
         * 브랜드 배열과 제외 배열을 직접 입력할 수 있도록 해야 겠다.
         */
        // 브랜드 키워드 배열
        $brandArray = ['LG', '엘지', 'lg전자', '엘지전자', '삼성', '삼성전자', 'samsung', '룸앤', 'ROOM&TV'];

        // 그 중 제외할 키워드 배열
        $excludeArray = [
            'LG패널', 'LG 패널', '삼성패널', '삼성 패널', '정품패널', '정품 패널', '대기업', 'LG정품', '엘지정품', '삼성정품',
            'LG IPS', 'LGIPS', '삼성dex', '삼성 dex', 'lgc', 'dlg', '주연', '크로스오버', '한성', '디스플레이', '더함',
            '이노스', 'TCL', '프리즘', '인켈', '시티브', 'seetive', '로로테크', '이스트라', '홈플래닛', '아인츠', '샤오미',
            '클라인즈', '아남', '와이드뷰', '삼탠바이미', '와이드무빙뷰', 'Hisense', '아이리버', '라익미', '아이사',
            'artive', '유맥스', '이엔티비', 'hp', '모지', 'mozee', '레노버', 'lenovo', '델', 'dell', '벤큐', '카멜',
            '위드라이프', '제우스랩', 'zeuslab', '어드밴스원', '엠텍', 'thinkvision', '한솔시스템'
        ];

        $model = 'etc';

        // 제품 이름에 브랜드 이름이 포함되어 있는지 확인
        foreach ($brandArray as $brand) {
            if (stripos($product->name, $brand) !== false) {
                $model = 'brand';
                // 제외 키워드 확인
                foreach ($excludeArray as $exclude) {
                    if (stripos($product->name, $exclude) !== false) {
                        $model = 'etc';
                        break 2;
                    }
                }
            }
        }

        return $model;
    }

    /**
     * 상품명 문자열에서 크기(숫자)와 단위(cm 또는 inch)를 추출합니다.
     * 소수점 cm 값도 처리합니다.
     *
     * @param Product $product
     * @return array|null 크기 정보 ('size' => float|int, 'unit' => string) 또는 null (추출 실패 시)
     */
    public function extractSizeFromName(Product $product): ?array
    {
        // 1. 상품명에서 직접적인 크기 단위(cm) 찾기 (소수점 포함 가능, 유니코드)
        // 센치, 센티미터 같은 한글 단위 처리를 위해 'u' 추가
        if (preg_match('/(\d{2,3}(?:\.\d+)?)(?:cm|센치|센티미터)/iu', $product->name, $matches)) {
            return ['size' => floatval($matches[1]), 'unit' => 'CM'];
        }

        $feeUnit = 'INCH'; // 기본값은 inch
        // 2. 상품명에서 직접적인 크기 단위(인치/형) 찾기 (유니코드)
        // '형', '인치' 같은 한글 및 \b 처리를 위해 'u' 추가
        if (preg_match('/(\d{2,3}(?:\.\d+)?)\s?(?:inch|인치|형)\b/iu', $product->name, $matches)) {
            return ['size' => floatval($matches[1]), 'unit' => $feeUnit];
        }

        // 한성 "ULTRON" 뒤 네자리 숫자 중 앞 두자리를 크기로 인식 (대소문자 무시)
        if (preg_match('/\bULTRON\s?(\d{2})\d{2}\b/i', $product->name, $matches)) {
            $size = intval($matches[1]); // 정수로 처리
            // 추출된 크기가 합리적인지 확인 (예: 15 ~ 100)
            if ($size >= 8 && $size <= 100) {
                return ['size' => $size, 'unit' => $feeUnit];
            }
        }

        // '휴대용' 또는 '포터블' 체크를 위한 패턴을 추가(설마 포터블이 24인치 이상 되겠어???) 2025-05월...현재
        if (preg_match('/휴대용|포터블/i', $product->name)) {
            return ['size' => 23.8, 'unit' => $feeUnit];
        }

        // 3. 모델명으로 추정되는 부분에서 크기 찾기 (대소문자 구분 없음)
        // 모델명은 주로 영문/숫자이므로 'u'는 필수는 아닐 수 있으나, 일관성을 위해 추가해도 무방
        $modelPatterns = [
            '/\b[A-Z]{2,3}(\d{2})[A-Z0-9]\d+[A-Z]\b/i',  // 새로운 패턴: TFG34Q10W 형식
            '/\b(\d{2,3})(?!Hz\b)[A-Z]+[A-Z0-9]*\b/i', // 패턴 1: 숫자 시작, 바로 뒤 문자(주사율 Hz는 제외)
            '/\b(\d{2})\d+[A-Z][A-Z0-9]*\b/i', // 패턴 1.1: 숫자 시작, 뒤 숫자 더 오고 문자
            '/\b[A-Z]{1,4}(\d{2})[A-Z0-9]*\b/i', // 패턴 2: 알파벳 시작, 중간 숫자
            '/\b[A-Z]+(\d{2})\b/i',             // 패턴 3: 문자 뒤 숫자
            '/\b(\d{2})[A-Z]+\b/i',             // 패턴 3: 숫자 뒤 문자
        ];

        foreach ($modelPatterns as $pattern) {
            if (preg_match_all($pattern, $product->name, $matchesAll, PREG_PATTERN_ORDER)) {
                foreach ($matchesAll[1] as $potentialSize) {
                    $numSize = (int)$potentialSize;
                    // 합리적인 TV/모니터 크기 범위인지 확인 (예: 15인치 ~ 100인치)
                    if ($numSize >= 8 && $numSize <= 100) {
                        return ['size' => $numSize, 'unit' => $feeUnit];
                    }
                }
            }
        }

        // 4. 위의 모든 패턴에 해당하지 않을 경우, 상품명 마지막에 있는 숫자를 추출 (소수 포함)
        if (preg_match_all('/(\d+(?:\.\d+)?)/', $product->name, $matchesAll)) {
            // 찾은 모든 숫자 중 마지막 숫자를 사용
            $lastNumberStr = end($matchesAll[1]);
            $size = floatval($lastNumberStr);

            // 추출된 크기가 유효한지 간단히 확인 (예: 0보다 큰지)
            if ($size > 0) {
                return ['size' => $size, 'unit' => $feeUnit];
            }
        }

        // // 텔레그램으로 메시지 보내기
        $telegram = new TelegramService();
        $telegram->sendMessageToTeam("[{$product->qaid}]크기 정보 추출 실패: " . $product->name);

        // 어떤 크기 정보도 찾지 못한 경우 디폴트 값 지정
        return [
            'size' => 65,
            'unit' => $feeUnit
        ];
    }

    /**
     * 모니터 수리 요금 쿼리 빌더
     */
    public function monitorFeeListQuery(Product $product, string $model, string $feeUnit, string $repairType): Builder
    {
        return DB::table('repair_categories')
            ->join('repair_fee_ranges', 'repair_categories.id', '=', 'repair_fee_ranges.repair_category_id')
            ->join('repair_fees', 'repair_fee_ranges.id', '=', 'repair_fees.repair_fee_range_id')
            ->where('repair_categories.cate4_id', $product->cate4_id)
            ->where('repair_categories.cate5_id', $product->cate5_id)
            ->where('repair_fee_ranges.type', 'monitor')
            ->where('repair_fee_ranges.model', $model)
            ->where('repair_fee_ranges.fee_type', RepairFee::FEE_TYPE['SIZE'])
            ->where('repair_fee_ranges.fee_unit', RepairFee::FEE_UNIT[$feeUnit])
            ->where('repair_fees.repair_type', RepairFee::REPAIR_TYPE[$repairType])
            ->select(
                'repair_fee_ranges.id',
                'repair_fee_ranges.fee_type',
                'repair_fee_ranges.fee_unit',
                'repair_fee_ranges.min_value',
                'repair_fee_ranges.max_value',
                'repair_fees.amount'
            );
    }
}
