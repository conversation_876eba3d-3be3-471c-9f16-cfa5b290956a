<?php

namespace App\Traits\Repair;

use App\Models\RepairSymptom;
use App\Models\Req;
use Exception;
use Illuminate\Support\Collection;

trait SymptomTrait
{
    /**
     * @throws Exception
     */
    public function findSymptom(int $id): RepairSymptom
    {
        $symptom = RepairSymptom::find($id);

        if ($symptom === null) {
            throw new Exception("증상 [{$id}]을 찾을 수 없습니다.");
        }

        return $symptom;
    }

    public function findSymptomIdByCode(string $code): int
    {
        return RepairSymptom::where('code', $code)->first()->id;
    }

    /**
     * 타입에 따른 상품의 증상 리스트
     * @param int $reqType 요청 타입 (Req::TYPE_COUPANG 또는 Req::TYPE_APPLE)
     * @return array 증상 배열
     */
    public function getSymptoms(int $reqType): array
    {
        // 요청 타입에 따른 증상 타입 매핑
        $symptomType = $reqType === Req::TYPE_APPLE ? 'apple' : 'general';
        
        return RepairSymptom::where('type', $symptomType)->get()->toArray();
    }
}
