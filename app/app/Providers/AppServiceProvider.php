<?php

namespace App\Providers;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Observers\Cate4Observer;
use App\Observers\Cate5Observer;
use App\Repositories\Interfaces\WorkCategoryRepositoryInterface;
use App\Repositories\WorkCategoryRepository;
use App\Repositories\Interfaces\WorkActionRepositoryInterface;
use App\Repositories\WorkActionRepository;
use App\Repositories\Interfaces\WorkStatusTemplateRepositoryInterface;
use App\Repositories\WorkStatusTemplateRepository;
use App\Repositories\Interfaces\WorkStatusRepositoryInterface;
use App\Repositories\WorkStatusRepository;
use App\Repositories\Interfaces\MonitorSizeLookupRepositoryInterface;
use App\Repositories\MonitorSizeLookupRepository;
use App\Services\DynamicWorkStatusService;
use App\Services\EventManager;
use App\Services\NotificationManager;
use App\Services\DataSerializationService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Repository 인터페이스와 구현체 바인딩
        $this->app->bind(WorkCategoryRepositoryInterface::class, WorkCategoryRepository::class);
        $this->app->bind(WorkActionRepositoryInterface::class, WorkActionRepository::class);
        $this->app->bind(WorkStatusTemplateRepositoryInterface::class, WorkStatusTemplateRepository::class);
        $this->app->bind(WorkStatusRepositoryInterface::class, WorkStatusRepository::class);
        $this->app->bind(MonitorSizeLookupRepositoryInterface::class, MonitorSizeLookupRepository::class);

        // DynamicWorkStatusService를 싱글톤으로 등록 (Facade 지원)
        $this->app->singleton(DynamicWorkStatusService::class, function ($app) {
            return new DynamicWorkStatusService(
                $app->make(WorkCategoryRepositoryInterface::class),
                $app->make(WorkActionRepositoryInterface::class),
                $app->make(WorkStatusTemplateRepositoryInterface::class),
                $app->make(WorkStatusRepositoryInterface::class)
            );
        });

        // SSE 관련 서비스들을 싱글톤으로 등록
        $this->app->singleton(DataSerializationService::class);
        $this->app->singleton(EventManager::class);
        $this->app->singleton(NotificationManager::class);
        
        // 하이브리드 알림 시스템 서비스들을 싱글톤으로 등록
        $this->app->singleton(\App\Services\HybridNotificationService::class);
        $this->app->singleton(\App\Services\MessageSynchronizationService::class);
        
        // 마이그레이션 관련 서비스들을 싱글톤으로 등록
        $this->app->singleton(\App\Services\MigrationTrackingService::class);
        $this->app->singleton(\App\Services\PusherDependencyRemovalService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 카테고리 모델 Observer 등록
        Cate4::observe(Cate4Observer::class);
        Cate5::observe(Cate5Observer::class);
    }
}
