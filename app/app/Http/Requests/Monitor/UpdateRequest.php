<?php

namespace App\Http\Requests\Monitor;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class UpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // name과 name_hash는 불변 → 금지
            'name' => ['prohibited'],
            'name_hash' => ['prohibited'],

            'brand' => ['required', 'in:brand,general'],
            'size' => ['required', 'numeric', 'min:0'],
            'unit' => ['required', 'in:INCH,CM'],
        ];
    }

    /**
     * 유효성 검사 실패시 일관된 포맷으로 응답
     */
    protected function failedValidation(Validator $validator)
    {
        throw new ValidationException($validator, response()->json([
            'success' => false,
            'message' => '입력 데이터가 유효하지 않습니다.',
            'error_code' => 'VALIDATION_FAILED',
            'errors' => $validator->errors(),
        ], 422));
    }
}


