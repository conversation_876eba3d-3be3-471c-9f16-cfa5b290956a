<?php

namespace App\Http\Requests\Attendance;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'pageSize' => 'nullable|integer|min:1|max:100',
            'user_id' => 'nullable|integer|exists:users,id',
            'start_date' => 'nullable|date|before_or_equal:end_date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'pageSize.integer' => '페이지 크기는 숫자여야 합니다.',
            'pageSize.min' => '페이지 크기는 최소 :min 이상이어야 합니다.',
            'pageSize.max' => '페이지 크기는 최대 :max 이하여야 합니다.',
            'user_id.integer' => '사용자 ID는 숫자여야 합니다.',
            'user_id.exists' => '존재하지 않는 사용자입니다.',
            'start_date.date' => '시작일은 유효한 날짜여야 합니다.',
            'start_date.before_or_equal' => '시작일은 종료일보다 이전이거나 같아야 합니다.',
            'end_date.date' => '종료일은 유효한 날짜여야 합니다.',
            'end_date.after_or_equal' => '종료일은 시작일보다 이후이거나 같아야 합니다.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // pageSize가 제공되지 않은 경우 기본값 설정
        if (!$this->has('pageSize')) {
            $this->merge(['pageSize' => 16]);
        }

        // 날짜 필드가 빈 문자열인 경우 null로 변환
        if ($this->input('start_date') === '') {
            $this->merge(['start_date' => null]);
        }

        if ($this->input('end_date') === '') {
            $this->merge(['end_date' => null]);
        }

        // user_id가 빈 문자열인 경우 null로 변환
        if ($this->input('user_id') === '') {
            $this->merge(['user_id' => null]);
        }
    }
}
