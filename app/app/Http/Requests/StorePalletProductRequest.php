<?php

namespace App\Http\Requests;

use App\Models\RepairGrade;
use App\Models\RepairProduct;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class StorePalletProductRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'location_place' => 'required|string|max:255',
            'location_code' => 'required|string|max:255',
            'product_id' => 'required|integer',
            'qaid' => 'required|string',
            'repair_product_id' => 'required|integer',
            'pallet_grade_code' => 'nullable|string',
            'symptom_id' => 'required|integer',
            'process_id' => 'required|integer',
            'grade_id' => 'required|integer',
            'os_reinstall' => 'boolean', // true: 1, false: 0
            'add_parts' => 'nullable|array',
            'update_parts' => 'nullable|array',
            'remove_parts' => 'nullable|array',
            'invoice1' => 'required|integer',
            'invoice2' => 'required|integer',
            'invoice3' => 'required|integer',
            'repair_cost_id' => 'nullable|integer',
            'calculation_basis' => 'nullable|string',
            'calculation_details' => 'nullable|array',
            'memo' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'location_place.required' => '팔레트가 선택 되지 않았습니다.',
            'location_code.required' => '팔레트를 선택해 주세요.',
            'product_id.required' => '등록할 상품 정보가 없습니다.',
            'qaid.required' => '등록할 상품 정보가 없습니다. QAID를 입력해 주세요.',
            'repair_product_id.required' => '수리된 상품을 선택해 주세요.',
            'symptom_id.required' => '제품의 증상을 선택해 주세요.',
            'process_id.required' => '처리 내용을 선택해 주세요.',
            'grade_id.required' => '수리 등급을 선택해 주세요.',
            'invoice1.required' => '청구금액1(기본)을 입력해 주세요.',
            'invoice2.required' => '청구금액2(구성품 금액)을 입력해 주세요.',
            'invoice3.required' => '청구금액3(OS 재설치비)을 입력해 주세요.',
            'invoice1.integer' => '청구금액1(기본)은 숫자여야 합니다.',
            'invoice2.integer' => '청구금액2(구성품 금액)은 숫자여야 합니다.',
            'invoice3.integer' => '청구금액3(OS 재설치비)은 숫자여야 합니다.',
        ];
    }

    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $repairProductId = $this->input('repair_product_id');

            if (!$repairProductId) {
                return; // repair_product_id가 없으면 기본 validation에서 처리됨
            }

            try {
                $repairProduct = RepairProduct::with('repairGrade')
                    ->where('id', $repairProductId)
                    ->where('status', RepairProduct::STATUS_REPAIRED)
                    ->first();

                if ($repairProduct === null) {
                    $validator->errors()->add('repair_product_id', '저장하려는 상품이 존재 하지 않습니다.');
                    return;
                }

                // repairGrade가 로드되었는지 확인
                if ($repairProduct->repairGrade === null) {
                    $validator->errors()->add('repair_product_id', '수리 등급 정보를 찾을 수 없습니다.');
                    return;
                }

                $gradeCode = $repairProduct->repairGrade->code;
                $gradeName = RepairGrade::$GRADE_NAME[RepairGrade::GRADE_XL];
                // XL을 포함한 등급의 invoice1 값이 0보다 크면 에러
                if (str_starts_with($gradeCode, 'ST_XL') && $repairProduct->invoice1 > 0) {
                    $validator->errors()->add('repair_product_id', "[$gradeName]은 청구 비용이 발생할 수 없습니다.");
                }
            } catch (\Exception $e) {
                $validator->errors()->add('repair_product_id', '상품 검증 중 오류가 발생했습니다.');
            }
        });
    }
}
