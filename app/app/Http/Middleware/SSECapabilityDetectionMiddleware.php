<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Services\HybridNotificationService;

/**
 * SSE 지원 능력 감지 미들웨어
 * 
 * 클라이언트의 SSE 지원 여부를 감지하고 기록합니다.
 */
class SSECapabilityDetectionMiddleware
{
    private HybridNotificationService $hybridService;

    public function __construct(HybridNotificationService $hybridService)
    {
        $this->hybridService = $hybridService;
    }

    /**
     * 요청 처리
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        
        // SSE 관련 요청인지 확인
        if ($this->isSSERelatedRequest($request)) {
            $this->detectSSECapability($request);
        }
        
        return $response;
    }

    /**
     * SSE 관련 요청인지 확인
     */
    private function isSSERelatedRequest(Request $request): bool
    {
        $path = $request->path();
        
        // SSE 엔드포인트 또는 관련 API 요청
        return str_contains($path, 'sse') || 
               str_contains($path, 'notifications') ||
               $request->hasHeader('Accept') && str_contains($request->header('Accept'), 'text/event-stream');
    }

    /**
     * SSE 지원 능력 감지
     */
    private function detectSSECapability(Request $request): void
    {
        $userAgent = $request->userAgent();
        $acceptHeader = $request->header('Accept', '');
        $userId = auth()->id();
        
        // EventSource API 지원 여부 감지
        $supportsSSE = $this->checkEventSourceSupport($userAgent, $acceptHeader);
        
        // 명시적 SSE 지원 헤더 확인
        if ($request->hasHeader('X-SSE-Support')) {
            $supportsSSE = $request->header('X-SSE-Support') === 'true';
        }
        
        // Tauri 앱 감지
        if ($this->isTauriApp($userAgent)) {
            $supportsSSE = true; // Tauri는 EventSource를 지원
        }
        
        // 사용자별 SSE 지원 정보 저장
        if ($userId) {
            $this->hybridService->updateUserSSESupport($userId, $supportsSSE);
        }
        
        // 전체 통계를 위한 캐시 업데이트
        $this->updateCapabilityStats($supportsSSE, $userAgent);
        
        Log::debug('SSE 지원 능력 감지', [
            'user_id' => $userId,
            'supports_sse' => $supportsSSE,
            'user_agent' => $userAgent,
            'accept_header' => $acceptHeader,
            'is_tauri' => $this->isTauriApp($userAgent)
        ]);
    }

    /**
     * EventSource API 지원 여부 확인
     */
    private function checkEventSourceSupport(string $userAgent, string $acceptHeader): bool
    {
        // Accept 헤더에 text/event-stream이 있으면 SSE 지원
        if (str_contains($acceptHeader, 'text/event-stream')) {
            return true;
        }
        
        // 브라우저별 EventSource 지원 확인
        $supportedBrowsers = [
            'Chrome' => 6,
            'Firefox' => 6,
            'Safari' => 5,
            'Edge' => 79,
            'Opera' => 11
        ];
        
        foreach ($supportedBrowsers as $browser => $minVersion) {
            if (str_contains($userAgent, $browser)) {
                $version = $this->extractBrowserVersion($userAgent, $browser);
                return $version >= $minVersion;
            }
        }
        
        // Internet Explorer는 SSE를 지원하지 않음
        if (str_contains($userAgent, 'MSIE') || str_contains($userAgent, 'Trident')) {
            return false;
        }
        
        // 기본적으로 최신 브라우저는 SSE를 지원한다고 가정
        return true;
    }

    /**
     * Tauri 앱인지 확인
     */
    private function isTauriApp(string $userAgent): bool
    {
        return str_contains($userAgent, 'Tauri') || 
               str_contains($userAgent, 'tauri');
    }

    /**
     * 브라우저 버전 추출
     */
    private function extractBrowserVersion(string $userAgent, string $browser): int
    {
        $pattern = "/{$browser}\/(\d+)/i";
        if (preg_match($pattern, $userAgent, $matches)) {
            return (int) $matches[1];
        }
        
        return 0;
    }

    /**
     * 지원 능력 통계 업데이트
     */
    private function updateCapabilityStats(bool $supportsSSE, string $userAgent): void
    {
        $date = now()->format('Y-m-d');
        $hour = now()->format('H');
        
        // 일별 통계
        $dailyKey = "sse_capability_daily:{$date}";
        $dailyStats = Cache::get($dailyKey, ['total' => 0, 'sse_supported' => 0]);
        $dailyStats['total']++;
        if ($supportsSSE) {
            $dailyStats['sse_supported']++;
        }
        Cache::put($dailyKey, $dailyStats, 86400 * 7); // 7일 보관
        
        // 시간별 통계
        $hourlyKey = "sse_capability_hourly:{$date}:{$hour}";
        $hourlyStats = Cache::get($hourlyKey, ['total' => 0, 'sse_supported' => 0]);
        $hourlyStats['total']++;
        if ($supportsSSE) {
            $hourlyStats['sse_supported']++;
        }
        Cache::put($hourlyKey, $hourlyStats, 86400); // 24시간 보관
        
        // 브라우저별 통계
        $browser = $this->detectBrowser($userAgent);
        $browserKey = "sse_capability_browser:{$browser}:{$date}";
        $browserStats = Cache::get($browserKey, ['total' => 0, 'sse_supported' => 0]);
        $browserStats['total']++;
        if ($supportsSSE) {
            $browserStats['sse_supported']++;
        }
        Cache::put($browserKey, $browserStats, 86400 * 7);
    }

    /**
     * 브라우저 감지
     */
    private function detectBrowser(string $userAgent): string
    {
        $browsers = [
            'Chrome' => 'Chrome',
            'Firefox' => 'Firefox', 
            'Safari' => 'Safari',
            'Edge' => 'Edge',
            'Opera' => 'Opera',
            'Tauri' => 'Tauri'
        ];
        
        foreach ($browsers as $pattern => $name) {
            if (str_contains($userAgent, $pattern)) {
                return $name;
            }
        }
        
        return 'Unknown';
    }

    /**
     * SSE 지원 통계 조회
     */
    public static function getCapabilityStats(int $days = 7): array
    {
        $stats = [
            'daily' => [],
            'browsers' => [],
            'summary' => ['total' => 0, 'sse_supported' => 0, 'support_rate' => 0]
        ];
        
        // 일별 통계 수집
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_capability_daily:{$date}";
            $dailyData = Cache::get($dailyKey, ['total' => 0, 'sse_supported' => 0]);
            
            $stats['daily'][$date] = $dailyData;
            $stats['summary']['total'] += $dailyData['total'];
            $stats['summary']['sse_supported'] += $dailyData['sse_supported'];
        }
        
        // 브라우저별 통계 수집
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Tauri', 'Unknown'];
        foreach ($browsers as $browser) {
            $browserStats = ['total' => 0, 'sse_supported' => 0];
            
            for ($i = 0; $i < $days; $i++) {
                $date = now()->subDays($i)->format('Y-m-d');
                $browserKey = "sse_capability_browser:{$browser}:{$date}";
                $browserData = Cache::get($browserKey, ['total' => 0, 'sse_supported' => 0]);
                
                $browserStats['total'] += $browserData['total'];
                $browserStats['sse_supported'] += $browserData['sse_supported'];
            }
            
            if ($browserStats['total'] > 0) {
                $browserStats['support_rate'] = round(($browserStats['sse_supported'] / $browserStats['total']) * 100, 2);
                $stats['browsers'][$browser] = $browserStats;
            }
        }
        
        // 전체 지원률 계산
        if ($stats['summary']['total'] > 0) {
            $stats['summary']['support_rate'] = round(($stats['summary']['sse_supported'] / $stats['summary']['total']) * 100, 2);
        }
        
        return $stats;
    }
}