<?php

namespace App\Http\Middleware;

use App\Services\SseLogger;
use App\Services\SseSecurityService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

/**
 * SSE 보안 미들웨어
 * 
 * SSE 연결에 대한 보안 검증을 수행합니다.
 * - 세션 기반 인증 검증
 * - Rate Limiting 및 IP 기반 연결 제한
 * - CSRF 토큰 검증 및 메시지 sanitization
 */
class SseSecurityMiddleware
{
    private const MAX_CONNECTIONS_PER_IP = 10;
    private const MAX_CONNECTIONS_PER_USER = 5;
    private const RATE_LIMIT_ATTEMPTS = 60; // 분당 요청 수
    private const RATE_LIMIT_DECAY_MINUTES = 1;
    
    private SseSecurityService $securityService;
    private SseLogger $logger;
    
    public function __construct(SseSecurityService $securityService, SseLogger $logger)
    {
        $this->securityService = $securityService;
        $this->logger = $logger;
    }

    /**
     * 요청 처리
     *
     * @param Request $request HTTP 요청
     * @param Closure $next 다음 미들웨어
     * @return SymfonyResponse 응답
     */
    public function handle(Request $request, Closure $next): SymfonyResponse
    {
        try {
            $ip = $request->ip();
            $user = $this->authenticateViaSession($request);
            
            // 1. IP 차단 확인
            if ($this->securityService->isIpBlocked($ip)) {
                $this->logger->logSecurityEvent('blocked_ip_access', '차단된 IP 접근 시도', ['ip' => $ip]);
                
                return response()->json([
                    'success' => false,
                    'message' => '접근이 차단된 IP입니다.'
                ], 403);
            }
            
            // 2. 보안 헤더 검증
            if (!$this->securityService->validateSecurityHeaders($request)) {
                return response()->json([
                    'success' => false,
                    'message' => '유효하지 않은 요청 헤더입니다.'
                ], 400);
            }

            // 3. Rate Limiting 검사
            if (!$this->checkRateLimit($request)) {
                $this->logger->logRateLimitExceeded($ip, 0);
                
                return response()->json([
                    'success' => false,
                    'message' => '요청이 너무 많습니다. 잠시 후 다시 시도해주세요.'
                ], 429);
            }

            // 4. 연결 유효성 검사
            if (!$this->validateConnection($request)) {
                $this->logger->logSecurityEvent('invalid_connection', '유효하지 않은 연결 요청', [
                    'ip' => $ip,
                    'user_agent' => $request->userAgent()
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => '유효하지 않은 연결 요청입니다.'
                ], 400);
            }

            // 5. IP 기반 연결 제한 검사
            if (!$this->checkIpConnectionLimit($request)) {
                $this->logger->logSecurityEvent('ip_connection_limit', 'IP 연결 제한 초과', ['ip' => $ip]);
                
                return response()->json([
                    'success' => false,
                    'message' => '동일 IP에서 너무 많은 연결이 시도되었습니다.'
                ], 429);
            }

            // 6. 사용자별 연결 제한 검사 (인증된 사용자만)
            if ($user && !$this->checkUserConnectionLimit($user->id)) {
                $this->logger->logSecurityEvent('user_connection_limit', '사용자 연결 제한 초과', [
                    'user_id' => $user->id,
                    'ip' => $ip
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => '사용자당 최대 연결 수를 초과했습니다.'
                ], 429);
            }

            // 7. CSRF 토큰 검증 (POST 요청만)
            if ($request->isMethod('POST') && !$this->verifyCsrfToken($request)) {
                $this->logger->logSecurityEvent('csrf_validation_failed', 'CSRF 토큰 검증 실패', [
                    'ip' => $ip,
                    'user_id' => $user?->id
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => 'CSRF 토큰이 유효하지 않습니다.'
                ], 403);
            }

            // 8. 요청 데이터 sanitization 및 보안 검증
            $this->sanitizeRequestData($request);
            
            // 9. 의심스러운 활동 탐지
            $suspiciousActivities = $this->securityService->detectSuspiciousActivity($request, $user);
            if (!empty($suspiciousActivities)) {
                // 의심스러운 활동이 많은 경우 차단
                if (count($suspiciousActivities) >= 3) {
                    $this->securityService->blockIp($ip, 60); // 1시간 차단
                    
                    return response()->json([
                        'success' => false,
                        'message' => '의심스러운 활동이 감지되어 접근이 차단되었습니다.'
                    ], 403);
                }
            }

            // 보안 검사 통과 로그
            $this->logger->debug('SSE 보안 검사 통과', [
                'ip' => $ip,
                'user_id' => $user?->id,
                'method' => $request->method(),
                'path' => $request->path(),
                'suspicious_activities' => $suspiciousActivities
            ]);

            return $next($request);

        } catch (\Exception $e) {
            $this->logger->logError('SSE 보안 미들웨어 오류', $e, [
                'ip' => $request->ip(),
                'path' => $request->path()
            ]);

            return response()->json([
                'success' => false,
                'message' => '보안 검증 중 오류가 발생했습니다.'
            ], 500);
        }
    }

    /**
     * 연결 유효성을 검사합니다.
     *
     * @param Request $request HTTP 요청
     * @return bool 유효성 여부
     */
    private function validateConnection(Request $request): bool
    {
        // User-Agent 검사
        $userAgent = $request->userAgent();
        if (empty($userAgent) || strlen($userAgent) < 10) {
            return false;
        }

        // 의심스러운 User-Agent 패턴 검사
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return false;
            }
        }

        // Accept 헤더 검사 (SSE 스트림 요청인 경우)
        if ($request->is('sse/stream')) {
            $accept = $request->header('Accept', '');
            if (!str_contains($accept, 'text/event-stream') && !str_contains($accept, '*/*')) {
                return false;
            }
        }

        return true;
    }

    /**
     * 세션을 통한 사용자 인증을 확인합니다.
     *
     * @param Request $request HTTP 요청
     * @return \App\Models\User|null 인증된 사용자 또는 null
     */
    private function authenticateViaSession(Request $request): ?\App\Models\User
    {
        try {
            // Laravel Sanctum 세션 인증 확인
            if (Auth::check()) {
                return Auth::user();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('세션 인증 확인 중 오류', [
                'error' => $e->getMessage(),
                'ip_address' => $request->ip()
            ]);
            
            return null;
        }
    }

    /**
     * Rate Limiting을 검사합니다.
     *
     * @param Request $request HTTP 요청
     * @return bool Rate Limit 통과 여부
     */
    private function checkRateLimit(Request $request): bool
    {
        $key = 'sse_rate_limit:' . $request->ip();
        
        return RateLimiter::attempt(
            $key,
            self::RATE_LIMIT_ATTEMPTS,
            function () {
                // Rate limit 통과 시 실행할 콜백 (비어있음)
            },
            self::RATE_LIMIT_DECAY_MINUTES * 60
        );
    }

    /**
     * IP별 연결 제한을 검사합니다.
     *
     * @param Request $request HTTP 요청
     * @return bool 연결 제한 통과 여부
     */
    private function checkIpConnectionLimit(Request $request): bool
    {
        $ip = $request->ip();
        $cacheKey = "sse_ip_connections:{$ip}";
        
        $currentConnections = Cache::get($cacheKey, 0);
        
        return $currentConnections < self::MAX_CONNECTIONS_PER_IP;
    }

    /**
     * 사용자별 연결 제한을 검사합니다.
     *
     * @param int $userId 사용자 ID
     * @return bool 연결 제한 통과 여부
     */
    private function checkUserConnectionLimit(int $userId): bool
    {
        $cacheKey = "sse_user_connections:{$userId}";
        
        $currentConnections = Cache::get($cacheKey, 0);
        
        return $currentConnections < self::MAX_CONNECTIONS_PER_USER;
    }

    /**
     * CSRF 토큰을 검증합니다.
     *
     * @param Request $request HTTP 요청
     * @return bool CSRF 토큰 유효성
     */
    private function verifyCsrfToken(Request $request): bool
    {
        try {
            // Laravel의 기본 CSRF 검증 사용
            $token = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');
            
            if (!$token) {
                return false;
            }

            return hash_equals(
                session()->token(),
                $token
            );
        } catch (\Exception $e) {
            Log::error('CSRF 토큰 검증 중 오류', [
                'error' => $e->getMessage(),
                'ip_address' => $request->ip()
            ]);
            
            return false;
        }
    }

    /**
     * 요청 데이터를 sanitize합니다.
     *
     * @param Request $request HTTP 요청
     * @return void
     */
    private function sanitizeRequestData(Request $request): void
    {
        $input = $request->all();
        
        // 보안 서비스를 통한 고급 검증
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                // XSS 및 SQL Injection 검증
                if (!$this->securityService->validateAgainstXss($value) ||
                    !$this->securityService->validateAgainstSqlInjection($value)) {
                    
                    $this->logger->logSecurityEvent('malicious_input_detected', '악성 입력 감지', [
                        'field' => $key,
                        'ip' => $request->ip(),
                        'sample' => substr($value, 0, 100)
                    ]);
                    
                    // 악성 입력 제거
                    $request->request->remove($key);
                    continue;
                }
            }
        }
        
        // 기본 sanitization
        $sanitized = [];
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                // XSS 방지를 위한 HTML 태그 제거
                $sanitized[$key] = strip_tags($value);
                
                // SQL Injection 방지를 위한 특수 문자 이스케이프
                $sanitized[$key] = htmlspecialchars($sanitized[$key], ENT_QUOTES, 'UTF-8');
                
                // 길이 제한 (최대 1000자)
                $sanitized[$key] = substr($sanitized[$key], 0, 1000);
            } elseif (is_array($value)) {
                // 배열인 경우 재귀적으로 처리
                $sanitized[$key] = $this->sanitizeArray($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        // sanitize된 데이터로 요청 교체
        $request->replace($sanitized);
    }

    /**
     * 배열 데이터를 재귀적으로 sanitize합니다.
     *
     * @param array $array 배열 데이터
     * @return array sanitize된 배열
     */
    private function sanitizeArray(array $array): array
    {
        $sanitized = [];

        foreach ($array as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = htmlspecialchars(strip_tags($value), ENT_QUOTES, 'UTF-8');
                $sanitized[$key] = substr($sanitized[$key], 0, 1000);
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * IP 연결 수를 증가시킵니다.
     *
     * @param string $ip IP 주소
     * @return void
     */
    public function incrementIpConnections(string $ip): void
    {
        $cacheKey = "sse_ip_connections:{$ip}";
        $ttl = 3600; // 1시간
        
        Cache::increment($cacheKey, 1);
        Cache::put($cacheKey, Cache::get($cacheKey, 1), $ttl);
    }

    /**
     * IP 연결 수를 감소시킵니다.
     *
     * @param string $ip IP 주소
     * @return void
     */
    public function decrementIpConnections(string $ip): void
    {
        $cacheKey = "sse_ip_connections:{$ip}";
        
        $current = Cache::get($cacheKey, 0);
        if ($current > 0) {
            Cache::put($cacheKey, $current - 1, 3600);
        }
    }

    /**
     * 사용자 연결 수를 증가시킵니다.
     *
     * @param int $userId 사용자 ID
     * @return void
     */
    public function incrementUserConnections(int $userId): void
    {
        $cacheKey = "sse_user_connections:{$userId}";
        $ttl = 3600; // 1시간
        
        Cache::increment($cacheKey, 1);
        Cache::put($cacheKey, Cache::get($cacheKey, 1), $ttl);
    }

    /**
     * 사용자 연결 수를 감소시킵니다.
     *
     * @param int $userId 사용자 ID
     * @return void
     */
    public function decrementUserConnections(int $userId): void
    {
        $cacheKey = "sse_user_connections:{$userId}";
        
        $current = Cache::get($cacheKey, 0);
        if ($current > 0) {
            Cache::put($cacheKey, $current - 1, 3600);
        }
    }
}