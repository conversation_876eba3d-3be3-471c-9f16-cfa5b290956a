<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        'sse/stream', // SSE 스트림은 CSRF 검증 제외
    ];

    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        // StreamedResponse는 header() 메서드가 없으므로 체크
        if (method_exists($response, 'header')) {
            $response->header('P3P', 'CP="IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT"');
        }

        return $response;
    }
}
