<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserWorkLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'created_at' => $this->created_at,
            'memo' => $this->memo,
            'product' => [
                'qaid' => $this->product->qaid,
                'name' => $this->product->name,
                'repair_product' => $this->product->repairProduct ? [
                    'repair_grade' => $this->product->repairProduct->repairGrade ? [
                        'name' => $this->product->repairProduct->repairGrade->name,
                    ] : null,
                ] : null,
            ],
        ];
    }
}
