<?php

namespace App\Http\Controllers;

use App\Helpers\PaginationHelper;
use App\Models\ProductLog;
use App\Models\User;
use App\Models\WarehousePallet;
use App\Models\WorkStatus;
use App\Services\Warehouse\PalletService;
use App\Services\WorkStatusService;
use Illuminate\Http\Request;

class WarehousePalletController extends Controller
{
    protected WorkStatusService $workStatusService;

    public function __construct(WorkStatusService $workStatusService)
    {
        $this->workStatusService = $workStatusService;
    }

    /**
     * 입고 적재시 사용할 적재 가능한 팔레트 리스트
     */
    public function loading(Request $request)
    {
        $service = new PalletService();
        $pallets = $service->getLoadingPallets();

        return $this->successResponse([
            'pallets' => $pallets,
        ]);
    }

    /**
     * 팔레트 번호 생성
     */
    public function generatePalletNumber()
    {
        $service = new PalletService();
        $palletNumber = $service->generatePalletNumber();

        return $this->successResponse([
            'pallet_number' => $palletNumber,
        ]);
    }

    /**
     * 팔레트 번호 저장::클라이언트에서 팔레트 번호 생성을 누르면 저장됨
     */
    public function store(Request $request)
    {
        $palletNumber = $request->input('pallet_number');

        $pallet = WarehousePallet::where('pallet_number', $palletNumber)->first();
        if ($pallet) {
            return $this->errorResponse([
                'message' => "이미 사용중인 팔레트 번호입니다.",
            ]);
        }

        $user = auth()->user();
        $pallet = WarehousePallet::create([
            'pallet_number' => $palletNumber,
            'status' => WarehousePallet::STATUS_AVAILABLE,
            'created_by' => $user->id,
        ]);

        $statusIds = $this->workStatusService->getIds([
            WorkStatus::LINK_WAREHOUSE_PALLET_CREATE,
        ]);

        $now = now();
        ProductLog::insert([
            'product_id' => null,
            'model_type' => 'App\Models\WarehousePallet',
            'model_id' => $pallet->id,
            'work_status_id' => $statusIds[WorkStatus::LINK_WAREHOUSE_PALLET_CREATE],
            'user_id' => $user->id,
            'memo' => "입고 팔레트 [$palletNumber] 생성(상태: 사용 가능, 등록한 직원: $user->name)",
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        return $this->successResponse([
            'pallet' => $pallet,
        ]);
    }

    /**
     * 창고에 보관된 팔레트 리스트
     */
    public function list(Request $request)
    {
        $data = [
            'keyword' => $request->input('keyword'),
            'status' => $request->input('status'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        $query = WarehousePallet::where('type', WarehousePallet::TYPE_WAREHOUSING);

        if ($data['status']) {
            $query->where('status', $data['status']);
        }

        if ($data['keyword']) {
            // 팔레트 번호 또는 상품의 QAID로 검색
            $query->where(function($q) use ($data) {
                $q->where('pallet_number', $data['keyword'])
                    ->orWhereHas('palletItems.product', function($subQuery) use ($data) {
                        $subQuery->where('qaid', $data['keyword']);
                    });
            });
        }

        $pallets = $query->orderBy('created_at', 'desc')
            ->paginate($data['pageSize']);

        $pallets->getCollection()->transform(function($pallet) {
            $totalAmount = 0;
            foreach ($pallet->palletItems as $item) {
                if ($item->product) {
                    $totalAmount += $item->product->amount;
                }
            }

            // 작업자 이름 가져오기 - 쿼리 최적화
            $userIds = array_filter([$pallet->created_by, $pallet->shipped_in_by, $pallet->shipped_out_by]);
            $usersMap = [];

            if (!empty($userIds)) {
                $users = User::withTrashed()->whereIn('id', $userIds)->get();
                foreach ($users as $user) {
                    $usersMap[$user->id] = $user->name;
                }
            }

            $createdByName = $usersMap[$pallet->created_by] ?? null;
            $shippedInByName = $usersMap[$pallet->shipped_in_by] ?? null;
            $shippedOutByName = $usersMap[$pallet->shipped_out_by] ?? null;
            return [
                'id' => $pallet->id,
                'pallet_number' => $pallet->pallet_number,
                'status' => $pallet->status,
                'status_name' => WarehousePallet::$STATUS_NAME[$pallet->status],
                'items_count' => $pallet->palletItems()->count(),
                'amount' => $totalAmount,
                'created_by' => $createdByName,
                'shipped_in_by' => $shippedInByName,
                'shipped_in_at' => $pallet->shipped_in_at,
                'shipped_out_by' => $shippedOutByName,
                'shipped_out_at' => $pallet->shipped_out_at,
            ];
        });

        $paginatedResult = $pallets->withQueryString();

        return $this->successResponse([
            'pallets' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }
}
