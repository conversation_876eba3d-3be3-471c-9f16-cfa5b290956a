<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Services\UserWorkHistoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class UserWorkHistoryController extends Controller
{
    private UserWorkHistoryService $workHistoryService;

    public function __construct(UserWorkHistoryService $workHistoryService)
    {
        $this->workHistoryService = $workHistoryService;
    }

    /**
     * 오늘 작업한 내역을 조회합니다.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTodayWorkHistory(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'date' => 'nullable|date_format:Y-m-d',
                'pageSize' => 'integer|min:1|max:100',
            ]);

            $user = Auth::user();
            $date = $request->input('date'); // 선택적으로 특정 날짜 조회 가능
            $pageSize = $request->input('pageSize', 5);

            $result = $this->workHistoryService->getTodayWorkHistory($user, $date, $pageSize);

            return $this->successResponse($result, '오늘 작업 내역을 성공적으로 조회했습니다.');
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '작업 내역 조회에 실패했습니다.',
                'log_message' => '오늘 작업 내역 조회 실패(UserWorkHistoryController::getTodayWorkHistory)',
            ], $e, 'work_history', 500);
        }
    }

    /**
     * 특정 기간의 작업 내역을 조회합니다.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getWorkHistoryByDateRange(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'start_date' => 'required|date_format:Y-m-d',
                'end_date' => 'required|date_format:Y-m-d|after_or_equal:start_date',
                'pageSize' => 'integer|min:1|max:100',
            ]);

            $user = Auth::user();
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $pageSize = $request->input('pageSize', 16);

            $result = $this->workHistoryService->getWorkHistoryByDateRange($user, $startDate, $endDate, $pageSize);

            return $this->successResponse($result, '작업 내역을 성공적으로 조회했습니다.');
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '작업 내역 조회에 실패했습니다.',
                'log_message' => '기간별 작업 내역 조회 실패(UserWorkHistoryController::getWorkHistoryByDateRange)',
            ], $e, 'work_history', 500);
        }
    }

    /**
     * 작업 통계를 조회합니다.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getWorkStatistics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'days' => 'integer|min:1|max:30',
            ]);

            $user = Auth::user();
            $days = $request->input('days', 7); // 기본값 7일

            $result = $this->workHistoryService->getWorkStatistics($user, $days);

            return $this->successResponse($result, '작업 통계를 성공적으로 조회했습니다.');
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '작업 통계 조회에 실패했습니다.',
                'log_message' => '작업 통계 조회 실패(UserWorkHistoryController::getWorkStatistics)',
            ], $e, 'work_history', 500);
        }
    }
}
