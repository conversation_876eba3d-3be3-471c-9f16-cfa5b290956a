<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Services\RepairCostService;
use App\Services\MonitorSizeExtractionService;
use App\Services\RepairCostTypeProcessMappingService;
use App\Services\TelegramService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Throwable;

/**
 * 수리비 계산 API 컨트롤러
 *
 * 작업자 앱에서 사용하는 수리비 조회 및 계산 API를 제공합니다.
 */
class RepairCostController extends Controller
{
    /**
     * 수리비 계산 서비스
     */
    protected RepairCostService $repairCostService;

    /**
     * 모니터 크기 추출 서비스
     */
    protected MonitorSizeExtractionService $monitorSizeService;

    /**
     * 수리 프로세스 매핑 서비스
     */
    protected RepairCostTypeProcessMappingService $mappingService;

    /**
     * 텔레그램 알림 서비스
     */
    protected TelegramService $telegramService;

    /**
     * 생성자
     */
    public function __construct(
        RepairCostService                   $repairCostService,
        MonitorSizeExtractionService        $monitorSizeService,
        RepairCostTypeProcessMappingService $mappingService,
        TelegramService                     $telegramService
    ) {
        $this->repairCostService = $repairCostService;
        $this->monitorSizeService = $monitorSizeService;
        $this->mappingService = $mappingService;
        $this->telegramService = $telegramService;
    }

    /**
     * 수리비 계산 API
     *
     * 제품 정보와 프로세스 코드를 받아 수리비를 계산합니다.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateCost(Request $request): JsonResponse
    {
        try {
            // 입력값 검증
            $validator = Validator::make($request->all(), [
                'qaid' => 'required|string|max:50',
                'process_codes' => 'required|array|min:1',
                'process_codes.*' => 'required|string|max:50'
            ], [
                'qaid.required' => 'QAID는 필수입니다.',
                'qaid.string' => 'QAID는 문자열이어야 합니다.',
                'qaid.max' => 'QAID는 50자를 초과할 수 없습니다.',
                'process_codes.required' => '프로세스 코드는 필수입니다.',
                'process_codes.array' => '프로세스 코드는 배열이어야 합니다.',
                'process_codes.min' => '최소 1개의 프로세스 코드가 필요합니다.',
                'process_codes.*.required' => '프로세스 코드는 필수입니다.',
                'process_codes.*.string' => '프로세스 코드는 문자열이어야 합니다.',
                'process_codes.*.max' => '프로세스 코드는 50자를 초과할 수 없습니다.'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse([
                    'message' => '입력값 검증에 실패했습니다.',
                    'errors' => $validator->errors()->toArray()
                ], null, 'repair', 422);
            }

            $qaid = trim($request->input('qaid'));
            $processCodes = $request->input('process_codes');

            // 제품 조회
            $product = Product::where('qaid', $qaid)->first();
            if (!$product) {
                return $this->errorResponse([
                    'message' => '해당 QAID의 제품을 찾을 수 없습니다.',
                    'log_message' => "제품 조회 실패 - QAID: {$qaid}"
                ], null, 'repair', 404);
            }

            // 각 프로세스 코드별 수리비 계산
            $totalCost = 0;
            $breakdown = [];
            $hasError = false;
            $errorMessages = [];

            foreach ($processCodes as $processCode) {
                try {
                    $result = $this->repairCostService->calculateRepairCost($product, $processCode);

                    if ($this->repairCostService->validateCalculationResult($result)) {
                        $totalCost += $result['amount'];
                        $breakdown[] = [
                            'process_code' => $processCode,
                            'amount' => $result['amount'],
                            'basis' => $result['basis'],
                            'details' => $result['details']
                        ];

                        // 계산 결과 로그 기록
                        $this->repairCostService->logCalculationResult($product, $processCode, $result);
                    } else {
                        throw new \Exception('수리비 계산 결과가 유효하지 않습니다.');
                    }

                } catch (Throwable $e) {
                    $hasError = true;
                    $errorMessage = "프로세스 코드 {$processCode}의 수리비 계산 실패: " . $e->getMessage();
                    $errorMessages[] = $errorMessage;

                    // 기본값 적용
                    $defaultAmount = RepairCostService::DEFAULT_REPAIR_COST;
                    $totalCost += $defaultAmount;
                    $breakdown[] = [
                        'process_code' => $processCode,
                        'amount' => $defaultAmount,
                        'basis' => '기본값 적용 (계산 오류)',
                        'details' => [
                            'policy_name' => 'default',
                            'error' => $e->getMessage()
                        ]
                    ];

                    // 에러 로그 기록
                    Log::error('수리비 계산 오류', [
                        'qaid' => $qaid,
                        'product_id' => $product->id,
                        'process_code' => $processCode,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            // 오류가 발생한 경우 텔레그램 알림 발송
            if ($hasError) {
                $this->sendErrorNotification($qaid, $product, $errorMessages);
            }

            return $this->successResponse([
                'total_cost' => $totalCost,
                'breakdown' => $breakdown,
                'has_error' => $hasError,
                'error_messages' => $hasError ? $errorMessages : null
            ], '수리비 계산이 완료되었습니다.');

        } catch (Throwable $e) {
            // 전체적인 오류 처리
            Log::error('수리비 계산 API 오류', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse([
                'message' => '수리비 계산 중 오류가 발생했습니다.',
                'log_message' => '수리비 계산 API 오류: ' . $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 제품 정보 조회 API
     *
     * QAID로 제품 정보를 조회하고 모니터 크기 정보를 포함합니다.
     *
     * @param string $qaid
     * @return JsonResponse
     */
    public function getProductInfo(string $qaid): JsonResponse
    {
        try {
            $qaid = trim($qaid);

            // 제품 조회
            $product = Product::with(['cate4', 'cate5', 'req'])
                ->where('qaid', $qaid)
                ->first();

            if (!$product) {
                return $this->errorResponse([
                    'message' => '해당 QAID의 제품을 찾을 수 없습니다.',
                    'log_message' => "제품 조회 실패 - QAID: {$qaid}"
                ], null, 'repair', 404);
            }

            // 모니터 크기 추출 (모니터 제품인 경우)
            $extractedSize = null;
            if ($this->monitorSizeService->isMonitorProduct($product)) {
                try {
                    $sizeInfo = $this->monitorSizeService->extractSizeFromName($product);
                    if ($sizeInfo) {
                        $extractedSize = [
                            'size' => $sizeInfo['size'],
                            'unit' => $sizeInfo['unit'],
                            'brand_type' => $sizeInfo['brand_type'] ?? 'other'
                        ];
                    }
                } catch (Throwable $e) {
                    Log::warning('모니터 크기 추출 실패', [
                        'qaid' => $qaid,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // 적용 가능한 수리 프로세스 목록 조회
            $availableProcesses = $this->repairCostService->getAvailableProcessesForProduct($product);

            return $this->successResponse([
                'product' => [
                    'id' => $product->id,
                    'qaid' => $product->qaid,
                    'name' => $product->name,
                    'amount' => $product->amount,
                    'cate4' => $product->cate4 ? [
                        'id' => $product->cate4->id,
                        'name' => $product->cate4->name
                    ] : null,
                    'cate5' => $product->cate5 ? [
                        'id' => $product->cate5->id,
                        'name' => $product->cate5->name
                    ] : null,
                    'req_type' => $product->req->req_type ?? null
                ],
                'extracted_size' => $extractedSize,
                'available_processes' => $availableProcesses
            ], '제품 정보 조회가 완료되었습니다.');

        } catch (Throwable $e) {
            Log::error('제품 정보 조회 API 오류', [
                'qaid' => $qaid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse([
                'message' => '제품 정보 조회 중 오류가 발생했습니다.',
                'log_message' => '제품 정보 조회 API 오류: ' . $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 적용 가능한 수리 프로세스 목록 조회 API
     *
     * 제품 카테고리별로 필터링된 수리 프로세스 목록을 제공합니다.
     *
     * @param string $qaid
     * @return JsonResponse
     */
    public function getAvailableProcesses(string $qaid): JsonResponse
    {
        try {
            $qaid = trim($qaid);

            // 제품 조회
            $product = Product::where('qaid', $qaid)->first();
            if (!$product) {
                return $this->errorResponse([
                    'message' => '해당 QAID의 제품을 찾을 수 없습니다.',
                    'log_message' => "제품 조회 실패 - QAID: {$qaid}"
                ], null, 'repair', 404);
            }

            // 적용 가능한 프로세스 목록 조회
            $processes = $this->repairCostService->getAvailableProcessesForProduct($product);

            return $this->successResponse([
                'processes' => $processes,
                'total_count' => count($processes)
            ], '적용 가능한 수리 프로세스 목록 조회가 완료되었습니다.');

        } catch (Throwable $e) {
            Log::error('수리 프로세스 목록 조회 API 오류', [
                'qaid' => $qaid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse([
                'message' => '수리 프로세스 목록 조회 중 오류가 발생했습니다.',
                'log_message' => '수리 프로세스 목록 조회 API 오류: ' . $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 계산 오류 텔레그램 알림 발송
     *
     * @param string $qaid
     * @param Product $product
     * @param array $errorMessages
     * @return void
     */
    protected function sendErrorNotification(string $qaid, Product $product, array $errorMessages): void
    {
        try {
            $message = "🚨 수리비 계산 오류 발생\n\n";
            $message .= "📋 제품 정보:\n";
            $message .= "- QAID: {$qaid}\n";
            $message .= "- 제품명: {$product->name}\n";
            $message .= "- 제품 ID: {$product->id}\n\n";
            $message .= "❌ 오류 내용:\n";

            foreach ($errorMessages as $index => $error) {
                $message .= ($index + 1) . ". {$error}\n";
            }

            $message .= "\n⏰ 발생 시간: " . now()->format('Y-m-d H:i:s');

            $this->telegramService->sendMessageToTeam($message);

        } catch (Throwable $e) {
            Log::error('텔레그램 알림 발송 실패', [
                'qaid' => $qaid,
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
