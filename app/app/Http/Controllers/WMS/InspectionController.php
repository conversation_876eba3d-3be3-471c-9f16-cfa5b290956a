<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Models\Req;
use App\Services\CategoryService;
use App\Services\InspectionService;
use App\Services\ProductQueryBuilderService;
use App\Services\ProductService;
use App\Services\ReqService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class InspectionController extends Controller
{
    protected ReqService $reqService;
    protected ProductService $productService;
    protected InspectionService $inspectionService;

    public function __construct(
        ReqService $reqService,
        ProductService $productService,
        InspectionService $inspectionService
    )
    {
        $this->reqService = $reqService;
        $this->productService = $productService;
        $this->inspectionService = $inspectionService;
    }

    /**
     * 점검요청 상품 리스트
     * @param Request $request
     * @return JsonResponse
     */
    public function productList(Request $request): JsonResponse
    {
        $data = [
            'reqId' => $request->input('reqId'),
            'cate4' => $request->input('cate4'),
            'cate5' => $request->input('cate5'),
            'isRg' => $request->input('isRg'),
            'isAg' => $request->input('isAg'),
            'searchType' => $request->input('searchType'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        // 모든 카테고리 가져오기
        $categoryService = new CategoryService();
        $category = $categoryService->getAllCategories();

        // 요청 리스트 중 아직 완료되지 않은 리스트만 뽑아온다.
        $columns = ['id', 'req_at'];
        $uncheckedReqs = $this->reqService->getUncheckedList($columns, 'req_at');

        $queryBuilderService = new ProductQueryBuilderService();
        // 선택된 reqId가 없다면 처리가 완료되지 않은 목록들 중 가장 예전의 reqId를 가져온다.
        $data['reqId'] = $data['reqId'] ?: ($uncheckedReqs->first()->id ?? null);
        $products = $queryBuilderService->inspectPage($data);

        $paginatedResult = $products->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'inspects' => $uncheckedReqs,
            'cate4' => $category,
            'products' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 입고검수대기에서 개별적으로 검수통과(완료) 상태 변환(QAID)
     * 기존 프로그램에서 설명란에 로트번호로도 된다고 되어 있었지만 실제 프로그램에선 처리가 안 되어 있음
     * 일단 기존프로그램대로 처리함
     * method: patch
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function inspectionPass(Request $request): JsonResponse
    {
        $user = Auth::user();

        $data = [
            'reqId' => $request->input('reqId'),
            'qaid' => mb_strtoupper($request->input('qaid')),
        ];

        try {
            $errorFlag = false;
            $errorMessage = '';

            if (empty($data['reqId'])) {
                $errorFlag = true;
                $errorMessage = "입고날짜가 선택되지 않았습니다.";
            }

            if (empty($data['qaid'])) {
                $errorFlag = true;
                $errorMessage = "QAID를 찾을 수 없습니다.";
            }

            if ($errorFlag) {
                throw new Exception($errorMessage);
            }

            $product = $this->productService->checkedStatusChecked($data, $user);

            return $this->successResponse([
                'reqId' => $data['reqId'],
                'qaid' => $data['qaid'],
                'product_name' => $product->name,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'inspection', $e->getCode());
        }
    }

    /**
     * 일괄 검수 통과(미입고 처리): 입고검수(대기) -> (남아있는 상품은)미입고 처리<br>
     *
     * @throws Throwable
     */
    public function checkUndelivered(int $id): JsonResponse
    {
        try {
            $req = Req::find($id);
            $user = Auth::user();

            $this->inspectionService->processInspectionPass($req, $user);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'inspection', 500);
        }
    }
}
