<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Models\RepairCostPolicy;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Throwable;

class RepairCostPolicyController extends Controller
{
    /**
     * 수리비 정책 목록 조회
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $systems = RepairCostPolicy::with(['categories' => function ($query) {
                $query->where('is_active', true)->count();
            }])
            ->withCount(['categories', 'categories as active_categories_count' => function ($query) {
                $query->where('is_active', true);
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(16)
            ->withQueryString();

            return $this->successResponse([
                'systems' => $systems->items(),
                'pagination' => PaginationHelper::optimize($systems),
                'pricing_types' => RepairCostPolicy::getPricingTypes(),
                'policy_names' => RepairCostPolicy::getPolicyNames()
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 가격 정책 목록 조회 중 오류가 발생했습니다.',
                'log_message' => '수리비 가격 정책 목록 조회 실패: ' . $e->getMessage(),
                'error_code' => 'REPAIR_COST_POLICY_001',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 새로운 수리비 정책 생성
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => [
                    'required',
                    'string',
                    'max:50',
                    'unique:repair_cost_policies,name',
                    Rule::in(array_keys(RepairCostPolicy::getPolicyNames()))
                ],
                'display_name' => 'required|string|max:100',
                'description' => 'nullable|string|max:500',
                'pricing_type' => [
                    'required',
                    'string',
                    Rule::in(array_keys(RepairCostPolicy::getPricingTypes()))
                ],
                'is_active' => 'boolean'
            ], [
                'name.required' => '시스템명은 필수입니다.',
                'name.unique' => '이미 존재하는 시스템명입니다.',
                'name.in' => '유효하지 않은 시스템명입니다.',
                'display_name.required' => '표시명은 필수입니다.',
                'pricing_type.required' => '가격 결정 방식은 필수입니다.',
                'pricing_type.in' => '유효하지 않은 가격 결정 방식입니다.'
            ]);

            // 가격 결정 방식 검증 로직
            $this->validatePricingTypeForSystem($validated['name'], $validated['pricing_type']);

            DB::beginTransaction();

            $system = RepairCostPolicy::create([
                'name' => $validated['name'],
                'display_name' => $validated['display_name'],
                'description' => $validated['description'] ?? null,
                'pricing_type' => $validated['pricing_type'],
                'is_active' => $validated['is_active'] ?? true
            ]);

            DB::commit();

            return $this->successResponse([
                'system' => $system
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'REPAIR_COST_POLICY_002',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 정책 생성 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_003',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 특정 수리비 정책 조회
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $system = RepairCostPolicy::with([
                'categories' => function ($query) {
                    $query->with(['cate4', 'cate5'])
                          ->withCount(['ranges', 'ranges as active_ranges_count' => function ($q) {
                              $q->where('is_active', true);
                          }]);
                }
            ])
            ->withCount(['categories', 'categories as active_categories_count' => function ($query) {
                $query->where('is_active', true);
            }])
            ->findOrFail($id);

            return $this->successResponse([
                'system' => $system
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '수리비 가격 정책을 찾을 수 없습니다.',
                'log_message' => '수리비 가격 정책 조회 실패: ' . $e->getMessage(),
                'error_code' => 'REPAIR_COST_POLICY_004'
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 정책 조회 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_005',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 정책 수정
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $system = RepairCostPolicy::findOrFail($id);

            $validated = $request->validate([
                'name' => [
                    'sometimes',
                    'string',
                    'max:50',
                    Rule::unique('repair_cost_policies', 'name')->ignore($id),
                    Rule::in(array_keys(RepairCostPolicy::getPolicyNames()))
                ],
                'display_name' => 'sometimes|string|max:100',
                'description' => 'nullable|string|max:500',
                'pricing_type' => [
                    'sometimes',
                    'string',
                    Rule::in(array_keys(RepairCostPolicy::getPricingTypes()))
                ],
                'is_active' => 'sometimes|boolean'
            ], [
                'name.unique' => '이미 존재하는 시스템명입니다.',
                'name.in' => '유효하지 않은 시스템명입니다.',
                'pricing_type.in' => '유효하지 않은 가격 결정 방식입니다.'
            ]);

            // 가격 결정 방식이 변경되는 경우 검증
            if (isset($validated['name']) || isset($validated['pricing_type'])) {
                $newName = $validated['name'] ?? $system->name;
                $newPricingType = $validated['pricing_type'] ?? $system->pricing_type;
                $this->validatePricingTypeForSystem($newName, $newPricingType);
            }

            DB::beginTransaction();

            $system->update($validated);

            DB::commit();

            return $this->successResponse([
                'system' => $system->fresh()
            ]);

        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            SimpleLogService::warning('repair', '수리비 가격 정책 수정 실패', [
                'message' => '해당 수리비 정책을 찾을 수 없습니다.',
                'error' => $e->getMessage(),
                'error_code' => 'REPAIR_COST_POLICY_006'
            ], $e);

            return $this->errorResponse([
                'message' => '해당 수리비 정책을 찾을 수 없습니다.',
                'error_code' => 'REPAIR_COST_POLICY_006'
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'REPAIR_COST_POLICY_007',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 가격 정책 수정 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_008',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 정책 삭제
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $system = RepairCostPolicy::findOrFail($id);

            // 연관된 카테고리가 있는지 확인
            $categoriesCount = $system->categories()->count();
            if ($categoriesCount > 0) {
                return $this->errorResponse([
                    'message' => '연관된 카테고리가 있어 삭제할 수 없습니다. 먼저 카테고리를 삭제해주세요.',
                    'error_code' => 'REPAIR_COST_POLICY_009',
                    'data' => [
                        'categories_count' => $categoriesCount
                    ]
                ], null, 'repair', 400);
            }

            DB::beginTransaction();

            $system->delete();

            DB::commit();

            return $this->successResponse([], '수리비 가격 정책이 성공적으로 삭제되었습니다.');
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '해당 수리비 정책을 찾을 수 없습니다.',
                'log_message' => '수리비 정책 삭제 실패: ' . $e->getMessage(),
                'error_code' => 'REPAIR_COST_POLICY_010'
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 정책 삭제 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_011',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 시스템별 통계 정보 조회
     *
     * @return JsonResponse
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $systems = RepairCostPolicy::with(['categories.ranges.costs'])
                ->withCount([
                    'categories',
                    'categories as active_categories_count' => function ($query) {
                        $query->where('is_active', true);
                    }
                ])
                ->get();

            $statistics = $systems->map(function ($system) {
                $totalRanges = 0;
                $activeRanges = 0;
                $totalCosts = 0;
                $activeCosts = 0;

                foreach ($system->categories as $category) {
                    $totalRanges += $category->ranges->count();
                    $activeRanges += $category->ranges->where('is_active', true)->count();

                    foreach ($category->ranges as $range) {
                        $totalCosts += $range->costs->count();
                        if ($range->is_active) {
                            $activeCosts += $range->costs->count();
                        }
                    }
                }

                return [
                    'id' => $system->id,
                    'name' => $system->name,
                    'display_name' => $system->display_name,
                    'pricing_type' => $system->pricing_type,
                    'is_active' => $system->is_active,
                    'statistics' => [
                        'categories_count' => $system->categories_count,
                        'active_categories_count' => $system->active_categories_count,
                        'ranges_count' => $totalRanges,
                        'active_ranges_count' => $activeRanges,
                        'costs_count' => $totalCosts,
                        'active_costs_count' => $activeCosts
                    ]
                ];
            });

            $overallStats = [
                'total_systems' => $systems->count(),
                'active_systems' => $systems->where('is_active', true)->count(),
                'total_categories' => $systems->sum('categories_count'),
                'active_categories' => $systems->sum('active_categories_count'),
                'total_ranges' => $statistics->sum('statistics.ranges_count'),
                'active_ranges' => $statistics->sum('statistics.active_ranges_count'),
                'total_costs' => $statistics->sum('statistics.costs_count'),
                'active_costs' => $statistics->sum('statistics.active_costs_count')
            ];

            return $this->successResponse([
                'systems' => $statistics,
                'overall' => $overallStats
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 정책 통계 조회 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_012',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 시스템 활성화/비활성화 토글
     *
     * @param int $id
     * @return JsonResponse
     */
    public function toggleActive(int $id): JsonResponse
    {
        try {
            $system = RepairCostPolicy::findOrFail($id);

            DB::beginTransaction();

            $system->is_active = !$system->is_active;
            $system->save();

            DB::commit();

            $status = $system->is_active ? '활성화' : '비활성화';

            return $this->successResponse([
                'id' => $system->id,
                'is_active' => $system->is_active
            ], "수리비 정책이 성공적으로 {$status}되었습니다.");
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 정책 활성화/비활성화 실패: 해당 수리비 정책을 찾을 수 없습니다.',
                'error_code' => 'REPAIR_COST_POLICY_013'
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '시스템 상태 변경 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_014',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 시스템별 데이터 일괄 관리 - 카테고리 활성화/비활성화
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function bulkManageCategories(Request $request, int $id): JsonResponse
    {
        try {
            $system = RepairCostPolicy::findOrFail($id);

            $validated = $request->validate([
                'action' => 'required|string|in:activate,deactivate',
                'category_ids' => 'sometimes|array',
                'category_ids.*' => 'integer|exists:repair_cost_categories,id'
            ], [
                'action.required' => '작업 유형은 필수입니다.',
                'action.in' => '유효하지 않은 작업 유형입니다.',
                'category_ids.array' => '카테고리 ID는 배열이어야 합니다.',
                'category_ids.*.exists' => '존재하지 않는 카테고리 ID입니다.'
            ]);

            DB::beginTransaction();

            $query = $system->categories();

            // 특정 카테고리들만 선택된 경우
            if (isset($validated['category_ids'])) {
                $query->whereIn('id', $validated['category_ids']);
            }

            $isActive = $validated['action'] === 'activate';
            $affectedRows = $query->update(['is_active' => $isActive]);

            DB::commit();

            $actionText = $isActive ? '활성화' : '비활성화';

            return $this->successResponse([
                'affected_rows' => $affectedRows,
                'action' => $validated['action']
            ], "{$affectedRows}개의 카테고리가 성공적으로 {$actionText}되었습니다.");
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '해당 수리비 정책을 찾을 수 없습니다.',
                'error_code' => 'REPAIR_COST_POLICY_015',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'REPAIR_COST_POLICY_016',
                'data' => null,
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '일괄 관리 작업 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_017',
                'error_message' => $e->getMessage()
            ], $e, 'repair', 500);
        }
    }

    /**
     * 시스템별 데이터 일괄 관리 - 범위 활성화/비활성화
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function bulkManageRanges(Request $request, int $id): JsonResponse
    {
        try {
            $system = RepairCostPolicy::findOrFail($id);

            $validated = $request->validate([
                'action' => 'required|string|in:activate,deactivate',
                'category_id' => 'sometimes|integer|exists:repair_cost_categories,id',
                'range_ids' => 'sometimes|array',
                'range_ids.*' => 'integer|exists:repair_cost_ranges,id'
            ], [
                'action.required' => '작업 유형은 필수입니다.',
                'action.in' => '유효하지 않은 작업 유형입니다.',
                'category_id.exists' => '존재하지 않는 카테고리 ID입니다.',
                'range_ids.array' => '범위 ID는 배열이어야 합니다.',
                'range_ids.*.exists' => '존재하지 않는 범위 ID입니다.'
            ]);

            DB::beginTransaction();

            // 시스템의 카테고리들을 통해 범위에 접근
            $rangesQuery = DB::table('repair_cost_ranges')
                ->whereIn('repair_cost_category_id', function ($query) use ($system) {
                    $query->select('id')
                          ->from('repair_cost_categories')
                          ->where('repair_cost_policy_id', $system->id);
                });

            // 특정 카테고리가 지정된 경우
            if (isset($validated['category_id'])) {
                $rangesQuery->where('repair_cost_category_id', $validated['category_id']);
            }

            // 특정 범위들만 선택된 경우
            if (isset($validated['range_ids'])) {
                $rangesQuery->whereIn('id', $validated['range_ids']);
            }

            $isActive = $validated['action'] === 'activate';
            $affectedRows = $rangesQuery->update(['is_active' => $isActive]);

            DB::commit();

            $actionText = $isActive ? '활성화' : '비활성화';

            return $this->successResponse([
                'affected_rows' => $affectedRows,
                'action' => $validated['action']
            ], "{$affectedRows}개의 범위가 성공적으로 {$actionText}되었습니다.");
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '해당 수리비 정책을 찾을 수 없습니다.',
                'error_code' => 'REPAIR_COST_POLICY_018'
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'REPAIR_COST_POLICY_019',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '일괄 관리 작업 중 오류가 발생했습니다.',
                'error_code' => 'REPAIR_COST_POLICY_020'
            ], $e, 'repair', 500);
        }
    }

    /**
     * 시스템별 가격 결정 방식 검증
     *
     * @param string $policyName
     * @param string $pricingType
     * @throws Exception
     */
    private function validatePricingTypeForSystem(string $policyName, string $pricingType): void
    {
        $validCombinations = [
            RepairCostPolicy::POLICY_MONITOR_GENERAL => [RepairCostPolicy::PRICING_TYPE_SIZE],
            RepairCostPolicy::POLICY_APPLE => [RepairCostPolicy::PRICING_TYPE_PRICE],
            RepairCostPolicy::POLICY_GENERAL_PRICE => [RepairCostPolicy::PRICING_TYPE_PRICE],
            RepairCostPolicy::POLICY_OS_INSTALL_PRICE => [RepairCostPolicy::PRICING_TYPE_PRICE],
            RepairCostPolicy::POLICY_DEFAULT => [RepairCostPolicy::PRICING_TYPE_PRICE]
        ];

        if (!isset($validCombinations[$policyName]) ||
            !in_array($pricingType, $validCombinations[$policyName])) {
            throw new Exception("시스템 '{$policyName}'에는 가격 결정 방식 '{$pricingType}'을(를) 사용할 수 없습니다.");
        }
    }
}
