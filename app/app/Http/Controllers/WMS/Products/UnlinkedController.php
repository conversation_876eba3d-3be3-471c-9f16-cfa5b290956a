<?php

namespace App\Http\Controllers\WMS\Products;

use App\Exceptions\ProductException;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Models\Req;
use App\Models\User;
use App\Services\CategoryService;
use App\Services\ProductQueryBuilderService;
use App\Services\ProductService;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class UnlinkedController extends Controller
{
    use ProductSearchTrait;

    private array|User|Collection|Model $user;
    protected ProductService $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $data = [
            'reqId' => Req::UNLINKED_ID, # 미등록 상품은 req_id 가 2로 고정
            'productStatus' => $request->input('productStatus'),
            'processCd' => $request->input('processCd'),
            'cate4' => $request->input('cate4'),
            'cate5' => $request->input('cate5'),
            'searchType' => $request->input('searchType'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        $queryBuilderService = new ProductQueryBuilderService();
        $query = $queryBuilderService->unlinkedPage($data);

        $categoryService = new CategoryService();
        $category = $categoryService->getAllCategories();

        $paginatedResult = $query->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'cate4' => $category,
            'products' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult)
        ]);
    }

    /**
     * 미등록 상품 등록
     * method: post
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();

        $data = [
            'reqId' => Req::UNLINKED_ID, # 미등록 상품은 2번으로 고정
            'qaid' => $request->input('productQaid'),
            'barcode' => $request->input('productBarcode'),
            'name' => $request->input('productName'), # description
            'amount' => $request->input('productAmount'),
            'cate4' => $request->input('productCate4'),
            'cate5' => $request->input('productCate5'),
            'vendor' => $request->input('productVendor'),
            'memo' => $request->input('productMemo')
        ];

        try {
            if ($this->existsProductByQaid($data['qaid'])) {
                throw ProductException::alreadyExists($data['qaid']);
            }

            $this->productService->storeUnlinkedProduct($data, $user);

            return $this->successResponse([
                'request' => $data,
            ]);
        } catch(Exception $e) {
            return $this->errorResponse([
                'message' => '[미등록 상품]등록 실패: ' . $e->getMessage(),
            ], $e, 'product', 500);
        }
    }

    /**
     * 미등록 상품 수정
     * method: put
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function update(Request $request): JsonResponse
    {
        $user = Auth::user();

        $data = [
            'reqId' => Req::UNLINKED_ID,
            'id' => $request->input('productId'),
            'qaid' => $request->input('productQaid'),
            'barcode' => $request->input('productBarcode'),
            'name' => $request->input('productName'),
            'amount' => $request->input('productAmount'),
            'cate4' => $request->input('productCate4'),
            'cate5' => $request->input('productCate5'),
            'vendor' => $request->input('productVendor'),
            'status' => $request->input('productStatus'),
            'memo' => $request->input('productMemo')
        ];

        try {
            if ($this->existsProductById($data['id'])) {
                throw ProductException::alreadyExists($data['qaid']);
            }

            $this->productService->updateUnlinkedProduct($data, $user);

            return $this->successResponse([
                'request' => $data,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '[미등록 상품]수정 실패: ' . $e->getMessage(),
            ], $e, 'product', 500);
        }
    }
}
