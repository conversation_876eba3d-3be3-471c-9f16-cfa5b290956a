<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Services\ProductLogService;
use App\Services\QaidService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class QaidController extends Controller
{
    protected QaidService $qaidService;

    public function __construct(QaidService $qaidService)
    {
        $this->qaidService = $qaidService;
    }

    /**
     * QAID 리스트
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $data = [
            'beginAt' => $request->input('beginAt'),
            'endAt' => $request->input('endAt'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        $query = $this->qaidService->getList($data);
        $paginatedResult = $query->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * QAID 재발행 히스토리
     * @param  string  $id
     * @return JsonResponse
     */
    public function getHistory(string $id): JsonResponse
    {
        $logService = new ProductLogService();
        $logs = $logService->getQaidHistory($id);

        return $this->successResponse([
            'items' => $logs,
        ]);
    }

    /**
     * QAID 중복 검사 및 저장
     * @param  string  $qaid
     * @param  int  $id
     * @return JsonResponse
     */
    public function existsQaid(string $qaid, int $id): JsonResponse
    {
        try {
            $result = $this->qaidService->isDuplicate($qaid);

            if ($result['is_duplicate'] === true) {
                // $message = "입력된 [QAID: $qaid]는 [{$result['date']}]에 프린트 되었습니다.\n재발행된 횟수: {$result['print_count']}\n\n다시 프린트 하시겠습니까?\n\n잘못되었다 싶은 경우 팀장님께 문의해 주세요.";
                $message = "입력된 [QAID: $qaid]는 [{$result['date']}]에 프린트 되었습니다.\nQAID를 재발행하시려면 팀장님께 문의해 주세요.";
                throw new Exception($message);
            }

            $this->qaidService->storeQaid($qaid, $id);

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => "QAID 재발행 오류: " . $e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * QAID 재발행::관리자만 재발행 가능
     * @param  string  $qaid
     * @param  int  $id
     * @return JsonResponse
     */
    public function rePrintQaid(string $qaid, int $id): JsonResponse
    {
        try {
            // 사용자 인증 확인
            $user = auth()->user();
            if (!$user) {
                throw new Exception('로그인이 필요합니다.');
            }

            $admin = ['Super-Admin', 'Admin', 'Pallet-Manager', 'Carryout-Manager', 'Receiving-Manager'];
            if (!in_array($user->role, $admin)) {
                throw new Exception('QAID 재발행은 관리자만 가능합니다.');
            }

            $this->qaidService->updateQaid($qaid, $id);

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
                'log_message' => "QAID 재발행 오류[$qaid]: " . $e->getMessage(),
            ], $e, 'repair', 500);
        }
    }
}
