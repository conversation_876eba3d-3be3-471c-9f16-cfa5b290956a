<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Models\RepairCost;
use App\Models\RepairCostCategory;
use App\Models\RepairCostRange;
use App\Services\SimpleLogService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

/**
 * 수리비 범위 관리 컨트롤러
 *
 * 관리자 앱에서 사용할 수리비 범위 CRUD API를 제공합니다.
 */
class RepairCostRangeController extends Controller
{
    /**
     * 카테고리별 수리비 범위 목록 조회
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'category_id' => 'required|integer|exists:repair_cost_categories,id',
                'is_active' => 'sometimes|boolean',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ]);

            $categoryId = $request->input('category_id');
            $isActive = $request->input('is_active');
            $perPage = $request->input('per_page', 15);

            // 카테고리 존재 확인
            $category = RepairCostCategory::findOrFail($categoryId);

            $query = RepairCostRange::where('repair_cost_category_id', $categoryId)
                ->with(['category.policy', 'costs']);

            // 활성화 상태 필터링
            if ($isActive !== null) {
                $query->where('is_active', $isActive);
            }

            // 정렬: min_value 기준 오름차순
            $query->orderBy('min_value', 'asc')
                  ->orderBy('max_value', 'asc');

            $ranges = $query->paginate($perPage);

            // 각 범위에 대한 수리비 정보 추가
            $ranges->getCollection()->transform(function ($range) {
                $range->costs_summary = $range->costs->mapWithKeys(function ($cost) {
                    return [$cost->repair_type => $cost->amount];
                });

                $range->display_name = $range->display_name;

                return $range;
            });

            return $this->successResponse([
                'category' => [
                    'id' => $category->id,
                    'policy_name' => $category->policy->display_name ?? '',
                    'pricing_criteria' => $category->pricing_criteria
                ],
                'ranges' => $ranges
            ]);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '해당 카테고리를 찾을 수 없습니다.',
                'error_code' => 'CATEGORY_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 범위 목록 조회 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 새로운 수리비 범위 생성
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'repair_cost_category_id' => 'required|integer|exists:repair_cost_categories,id',
                'range_name' => 'required|string|max:100',
                'min_value' => 'nullable|numeric|min:0',
                'max_value' => 'nullable|numeric|min:0',
                'unit' => ['required', Rule::in([
                    RepairCostRange::UNIT_INCH,
                    RepairCostRange::UNIT_WON,
                    RepairCostRange::UNIT_COMMON
                ])],
                'is_active' => 'sometimes|boolean',
                'repair_costs' => 'sometimes|array',
                'repair_costs.*.repair_type' => ['required_with:repair_costs', Rule::in(array_keys(RepairCost::getRepairTypes()))],
                'repair_costs.*.amount' => 'required_with:repair_costs|integer|min:0|max:9999999'
            ]);

            // 범위 값 유효성 검증
            $minValue = $request->input('min_value');
            $maxValue = $request->input('max_value');

            if ($minValue !== null && $maxValue !== null && $minValue >= $maxValue) {
                return $this->errorResponse([
                    'message' => '최소값은 최대값보다 작아야 합니다.',
                    'error_code' => 'INVALID_RANGE_VALUES'
                ], null, 'repair', 422);
            }

            // 범위 중복 검증
            $categoryId = $request->input('repair_cost_category_id');
            $overlappingRange = $this->checkRangeOverlap($categoryId, $minValue, $maxValue);

            if ($overlappingRange) {
                return $this->errorResponse([
                    'message' => '입력한 범위가 기존 범위와 중복됩니다.',
                    'error_code' => 'RANGE_OVERLAP',
                    'data' => [
                        'overlapping_range' => $overlappingRange
                    ]
                ], null, 'repair', 422);
            }

            DB::beginTransaction();

            // 수리비 범위 생성
            $range = RepairCostRange::create([
                'repair_cost_category_id' => $categoryId,
                'range_name' => $request->input('range_name'),
                'min_value' => $minValue,
                'max_value' => $maxValue,
                'unit' => $request->input('unit'),
                'is_active' => $request->input('is_active', true)
            ]);

            // 수리비 금액 생성 (제공된 경우)
            $repairCosts = $request->input('repair_costs', []);
            foreach ($repairCosts as $costData) {
                RepairCost::create([
                    'repair_cost_range_id' => $range->id,
                    'repair_type' => $costData['repair_type'],
                    'amount' => $costData['amount']
                ]);
            }

            DB::commit();

            // 생성된 범위 정보 로드
            $range->load(['category.policy', 'costs']);
            $range->costs_summary = $range->costs->mapWithKeys(function ($cost) {
                return [$cost->repair_type => $cost->amount];
            });
            $range->display_name = $range->display_name;

            return $this->successResponse([
                'range' => $range
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 범위 생성 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 특정 수리비 범위 조회
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $range = RepairCostRange::with(['category.policy', 'costs'])
                ->findOrFail($id);

            $range->costs_summary = $range->costs->mapWithKeys(function ($cost) {
                return [$cost->repair_type => $cost->amount];
            });
            $range->display_name = $range->display_name;

            return $this->successResponse([
                'range' => $range
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '해당 수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 범위 조회 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 범위 수정
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $range = RepairCostRange::findOrFail($id);

            $request->validate([
                'range_name' => 'sometimes|string|max:100',
                'min_value' => 'sometimes|nullable|numeric|min:0',
                'max_value' => 'sometimes|nullable|numeric|min:0',
                'unit' => ['sometimes', Rule::in([
                    RepairCostRange::UNIT_INCH,
                    RepairCostRange::UNIT_WON,
                    RepairCostRange::UNIT_COMMON
                ])],
                'is_active' => 'sometimes|boolean'
            ]);

            // 범위 값 유효성 검증
            $minValue = $request->input('min_value', $range->min_value);
            $maxValue = $request->input('max_value', $range->max_value);

            if ($minValue !== null && $maxValue !== null && $minValue >= $maxValue) {
                return $this->errorResponse([
                    'message' => '최소값은 최대값보다 작아야 합니다.',
                    'error_code' => 'INVALID_RANGE_VALUES'
                ], null, 'repair', 422);
            }

            // 범위 중복 검증 (자기 자신 제외)
            $overlappingRange = $this->checkRangeOverlap(
                $range->repair_cost_category_id,
                $minValue,
                $maxValue,
                $id
            );

            if ($overlappingRange) {
                return $this->errorResponse([
                    'message' => '수정하려는 범위가 기존 범위와 중복됩니다.',
                    'error_code' => 'RANGE_OVERLAP',
                    'data' => [
                        'overlapping_range' => $overlappingRange
                    ]
                ], null, 'repair', 422);
            }

            // 수리비 범위 수정
            $range->update($request->only([
                'range_name', 'min_value', 'max_value', 'unit', 'is_active'
            ]));

            // 수정된 범위 정보 로드
            $range->load(['category.policy', 'costs']);
            $range->costs_summary = $range->costs->mapWithKeys(function ($cost) {
                return [$cost->repair_type => $cost->amount];
            });
            $range->display_name = $range->display_name;

            return $this->successResponse([
                'range' => $range
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '수리비 범위 수정 실패: 해당 수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '수리비 범위 수정 실패: 입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 범위 수정 실패: 내부 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 범위 삭제
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $range = RepairCostRange::findOrFail($id);

            // 연관된 수리비 금액이 있는지 확인
            $costsCount = $range->costs()->count();

            if ($costsCount > 0) {
                return $this->errorResponse([
                    'message' => '연관된 수리비 금액이 있어 삭제할 수 없습니다.',
                    'error_code' => 'RANGE_HAS_COSTS',
                    'data' => [
                        'costs_count' => $costsCount
                    ]
                ], null, 'repair', 422);
            }

            DB::beginTransaction();

            // 수리비 범위 삭제
            $range->delete();

            DB::commit();

            return $this->successResponse([], '수리비 범위가 성공적으로 삭제되었습니다.');
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 범위 삭제 실패: 해당 수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 범위 삭제 실패: 내부 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 범위 중복 검증
     *
     * @param int $categoryId
     * @param float|null $minValue
     * @param float|null $maxValue
     * @param int|null $excludeId 제외할 범위 ID (수정 시 사용)
     * @return array|null
     */
    private function checkRangeOverlap(int $categoryId, ?float $minValue, ?float $maxValue, ?int $excludeId = null): ?array
    {
        $query = RepairCostRange::where('repair_cost_category_id', $categoryId)
            ->where('is_active', true);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        $existingRanges = $query->get();

        foreach ($existingRanges as $existingRange) {
            $existingMin = $existingRange->min_value ?? 0;
            $existingMax = $existingRange->max_value ?? PHP_FLOAT_MAX;
            $newMin = $minValue ?? 0;
            $newMax = $maxValue ?? PHP_FLOAT_MAX;

            // 범위 중복 검사
            if ($this->rangesOverlap($newMin, $newMax, $existingMin, $existingMax)) {
                return [
                    'id' => $existingRange->id,
                    'range_name' => $existingRange->range_name,
                    'min_value' => $existingRange->min_value,
                    'max_value' => $existingRange->max_value,
                    'display_name' => $existingRange->display_name
                ];
            }
        }

        return null;
    }

    /**
     * 두 범위가 중복되는지 확인
     *
     * @param float $min1
     * @param float $max1
     * @param float $min2
     * @param float $max2
     * @return bool
     */
    private function rangesOverlap(float $min1, float $max1, float $min2, float $max2): bool
    {
        return $min1 < $max2 && $min2 < $max1;
    }

    /**
     * 특정 범위의 수리비 금액 목록 조회
     *
     * @param int $rangeId
     * @return JsonResponse
     */
    public function getCosts(int $rangeId): JsonResponse
    {
        try {
            $range = RepairCostRange::with(['category.policy', 'costs'])
                ->findOrFail($rangeId);

            // 모든 수리 유형에 대한 금액 정보 구성
            $repairTypes = RepairCost::getRepairTypes();
            $costsData = [];

            foreach ($repairTypes as $type => $displayName) {
                $cost = $range->costs->firstWhere('repair_type', $type);
                $costsData[] = [
                    'repair_type' => $type,
                    'repair_type_display' => $displayName,
                    'amount' => $cost ? $cost->amount : 0,
                    'cost_id' => $cost ? $cost->id : null,
                    'formatted_amount' => $cost ? $cost->formatted_amount : '0원'
                ];
            }

            return $this->successResponse([
                'range' => [
                    'id' => $range->id,
                    'range_name' => $range->range_name,
                    'display_name' => $range->display_name,
                    'min_value' => $range->min_value,
                    'max_value' => $range->max_value,
                    'unit' => $range->unit,
                    'category' => [
                        'id' => $range->category->id,
                        'policy_name' => $range->category->system->display_name ?? '',
                        'pricing_criteria' => $range->category->pricing_criteria
                    ]
                ],
                'costs' => $costsData
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 금액 목록 조회 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 특정 범위의 수리비 금액 일괄 수정
     *
     * @param Request $request
     * @param int $rangeId
     * @return JsonResponse
     */
    public function updateCosts(Request $request, int $rangeId): JsonResponse
    {
        try {
            $range = RepairCostRange::with(['category.policy', 'costs'])
                ->findOrFail($rangeId);

            $request->validate([
                'costs' => 'required|array|min:1',
                'costs.*.repair_type' => ['required', Rule::in(array_keys(RepairCost::getRepairTypes()))],
                'costs.*.amount' => 'required|integer|min:0|max:9999999',
                'track_changes' => 'sometimes|boolean'
            ]);

            $costs = $request->input('costs');
            $trackChanges = $request->input('track_changes', true);
            $changes = [];

            DB::beginTransaction();

            foreach ($costs as $costData) {
                $repairType = $costData['repair_type'];
                $newAmount = $costData['amount'];

                // 기존 수리비 조회
                $existingCost = $range->costs->firstWhere('repair_type', $repairType);

                if ($existingCost) {
                    // 기존 수리비 수정
                    $oldAmount = $existingCost->amount;

                    if ($oldAmount != $newAmount) {
                        $existingCost->update(['amount' => $newAmount]);

                        if ($trackChanges) {
                            $changes[] = [
                                'repair_type' => $repairType,
                                'repair_type_display' => RepairCost::getRepairTypes()[$repairType],
                                'old_amount' => $oldAmount,
                                'new_amount' => $newAmount,
                                'change_type' => 'updated'
                            ];
                        }
                    }
                } else {
                    // 새로운 수리비 생성
                    RepairCost::create([
                        'repair_cost_range_id' => $rangeId,
                        'repair_type' => $repairType,
                        'amount' => $newAmount
                    ]);

                    if ($trackChanges) {
                        $changes[] = [
                            'repair_type' => $repairType,
                            'repair_type_display' => RepairCost::getRepairTypes()[$repairType],
                            'old_amount' => 0,
                            'new_amount' => $newAmount,
                            'change_type' => 'created'
                        ];
                    }
                }
            }

            // 변경 이력 로깅
            if ($trackChanges && !empty($changes)) {
                SimpleLogService::info('repair', '수리비 금액 변경', [
                    'range_id' => $rangeId,
                    'range_name' => $range->range_name,
                    'category_id' => $range->repair_cost_category_id,
                    'changes' => $changes,
                    'user_id' => auth()->id() ?? null,
                    'timestamp' => now()
                ]);
            }

            DB::commit();

            // 업데이트된 수리비 정보 조회
            $range->load('costs');
            $repairTypes = RepairCost::getRepairTypes();
            $updatedCosts = [];

            foreach ($repairTypes as $type => $displayName) {
                $cost = $range->costs->firstWhere('repair_type', $type);
                $updatedCosts[] = [
                    'repair_type' => $type,
                    'repair_type_display' => $displayName,
                    'amount' => $cost ? $cost->amount : 0,
                    'cost_id' => $cost ? $cost->id : null,
                    'formatted_amount' => $cost ? $cost->formatted_amount : '0원'
                ];
            }

            return $this->successResponse([
                'range' => [
                    'id' => $range->id,
                    'range_name' => $range->range_name,
                    'display_name' => $range->display_name
                ],
                'costs' => $updatedCosts,
                'changes' => $trackChanges ? $changes : null,
                'changes_count' => count($changes)
            ]);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 올바르지 않습니다.',
                'error_code' => 'VALIDATION_ERROR',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 금액 수정 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 금액 변경 이력 조회
     *
     * @param Request $request
     * @param int $rangeId
     * @return JsonResponse
     */
    public function getCostHistory(Request $request, int $rangeId): JsonResponse
    {
        try {
            $range = RepairCostRange::findOrFail($rangeId);

            $request->validate([
                'start_date' => 'sometimes|date',
                'end_date' => 'sometimes|date|after_or_equal:start_date',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ]);

            $perPage = $request->input('per_page', 20);
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // 로그 파일에서 변경 이력 조회 (실제 구현에서는 별도 테이블 사용 권장)
            $logPath = storage_path('logs/laravel.log');
            $history = [];

            if (file_exists($logPath)) {
                $logContent = file_get_contents($logPath);
                $pattern = '/\[.*?\] local\.INFO: 수리비 금액 변경.*?"range_id":' . $rangeId . '.*?}/';

                if (preg_match_all($pattern, $logContent, $matches)) {
                    foreach ($matches[0] as $match) {
                        // JSON 부분 추출 및 파싱
                        if (preg_match('/\{.*\}/', $match, $jsonMatch)) {
                            $logData = json_decode($jsonMatch[0], true);
                            if ($logData && isset($logData['changes'])) {
                                $history[] = [
                                    'timestamp' => $logData['timestamp'] ?? null,
                                    'changes' => $logData['changes'],
                                    'user_id' => $logData['user_id'] ?? null
                                ];
                            }
                        }
                    }
                }
            }

            // 날짜 필터링
            if ($startDate || $endDate) {
                $history = array_filter($history, function ($item) use ($startDate, $endDate) {
                    if (!$item['timestamp']) return false;

                    $itemDate = Carbon::parse($item['timestamp']);

                    if ($startDate && $itemDate->lt(Carbon::parse($startDate))) {
                        return false;
                    }

                    if ($endDate && $itemDate->gt(Carbon::parse($endDate))) {
                        return false;
                    }

                    return true;
                });
            }

            // 최신순 정렬
            usort($history, function ($a, $b) {
                return strcmp($b['timestamp'] ?? '', $a['timestamp'] ?? '');
            });

            // 페이지네이션 시뮬레이션
            $total = count($history);
            $offset = ($request->input('page', 1) - 1) * $perPage;
            $paginatedHistory = array_slice($history, $offset, $perPage);

            return $this->successResponse([
                'range' => [
                    'id' => $range->id,
                    'range_name' => $range->range_name,
                    'display_name' => $range->display_name
                ],
                'history' => $paginatedHistory,
                'pagination' => [
                    'total' => $total,
                    'per_page' => $perPage,
                    'current_page' => $request->input('page', 1),
                    'last_page' => ceil($total / $perPage)
                ]
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 올바르지 않습니다.',
                'error_code' => 'VALIDATION_ERROR',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리비 변경 이력 조회 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리비 금액 일괄 복사
     *
     * @param Request $request
     * @param int $rangeId
     * @return JsonResponse
     */
    public function copyCosts(Request $request, int $rangeId): JsonResponse
    {
        try {
            $sourceRange = RepairCostRange::with('costs')->findOrFail($rangeId);

            $request->validate([
                'target_range_ids' => 'required|array|min:1',
                'target_range_ids.*' => 'integer|exists:repair_cost_ranges,id',
                'overwrite_existing' => 'sometimes|boolean'
            ]);

            $targetRangeIds = $request->input('target_range_ids');
            $overwriteExisting = $request->input('overwrite_existing', false);
            $results = [];

            // 자기 자신을 대상에서 제외
            $targetRangeIds = array_filter($targetRangeIds, function ($id) use ($rangeId) {
                return $id != $rangeId;
            });

            if (empty($targetRangeIds)) {
                return $this->errorResponse([
                    'message' => '복사할 대상 범위가 없습니다.',
                    'error_code' => 'NO_TARGET_RANGES'
                ], null, 'repair', 422);
            }

            DB::beginTransaction();

            foreach ($targetRangeIds as $targetRangeId) {
                try {
                    $targetRange = RepairCostRange::with('costs')->findOrFail($targetRangeId);
                    $copiedCount = 0;
                    $skippedCount = 0;

                    foreach ($sourceRange->costs as $sourceCost) {
                        $existingCost = $targetRange->costs->firstWhere('repair_type', $sourceCost->repair_type);

                        if ($existingCost) {
                            if ($overwriteExisting) {
                                $existingCost->update(['amount' => $sourceCost->amount]);
                                $copiedCount++;
                            } else {
                                $skippedCount++;
                            }
                        } else {
                            RepairCost::create([
                                'repair_cost_range_id' => $targetRangeId,
                                'repair_type' => $sourceCost->repair_type,
                                'amount' => $sourceCost->amount
                            ]);
                            $copiedCount++;
                        }
                    }

                    $results[] = [
                        'target_range_id' => $targetRangeId,
                        'target_range_name' => $targetRange->range_name,
                        'copied_count' => $copiedCount,
                        'skipped_count' => $skippedCount,
                        'success' => true
                    ];

                } catch (Exception $e) {
                    $results[] = [
                        'target_range_id' => $targetRangeId,
                        'target_range_name' => null,
                        'copied_count' => 0,
                        'skipped_count' => 0,
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

            $successCount = count(array_filter($results, function ($result) {
                return $result['success'];
            }));

            return $this->successResponse([
                'source_range' => [
                    'id' => $sourceRange->id,
                    'range_name' => $sourceRange->range_name,
                    'costs_count' => $sourceRange->costs->count()
                ],
                'results' => $results,
                'summary' => [
                    'total_targets' => count($targetRangeIds),
                    'successful_copies' => $successCount,
                    'failed_copies' => count($targetRangeIds) - $successCount
                ]
            ]);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '해당 수리비 범위를 찾을 수 없습니다.',
                'error_code' => 'RANGE_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 올바르지 않습니다.',
                'error_code' => 'VALIDATION_ERROR',
                'errors' => $e->errors()
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '수리비 금액 복사 중 오류가 발생했습니다.',
                'error_code' => 'INTERNAL_ERROR',
            ], $e, 'repair', 500);
        }
    }
}
