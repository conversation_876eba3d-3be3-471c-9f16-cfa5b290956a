<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Models\MonitorRule;
use App\Services\MonitorSizeExtractionService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Throwable;

class MonitorRuleController extends Controller
{
    /**
     * 모니터 크기 추출 서비스
     */
    protected MonitorSizeExtractionService $extractionService;

    public function __construct(MonitorSizeExtractionService $extractionService)
    {
        $this->extractionService = $extractionService;
    }
    /**
     * 모니터 규칙 목록 조회
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = MonitorRule::query();

            // 규칙 유형별 필터링
            if ($request->has('rule_type')) {
                $query->byType($request->rule_type);
            }

            // 활성화 상태별 필터링
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // 우선순위 순으로 정렬
            $rules = $query->orderByPriority()->get();

            // 규칙 유형별로 그룹화
            $groupedRules = $rules->groupBy('rule_type')->map(function ($rules, $type) {
                return [
                    'type' => $type,
                    'type_name' => MonitorRule::getRuleTypes()[$type] ?? $type,
                    'rules' => $rules->values()
                ];
            })->values();

            return $this->successResponse([
                'rules' => $rules,
                'grouped_rules' => $groupedRules,
                'rule_types' => MonitorRule::getRuleTypes()
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '모니터 규칙 목록 조회에 실패했습니다.',
                'log_message' => '모니터 규칙 목록 조회 실패: ' . $e->getMessage(),
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 새로운 모니터 규칙 생성
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'rule_type' => [
                    'required',
                    'string',
                    Rule::in([
                        MonitorRule::RULE_TYPE_BRAND,
                        MonitorRule::RULE_TYPE_EXCLUDE,
                        MonitorRule::RULE_TYPE_SIZE_PATTERN
                    ])
                ],
                'pattern' => 'required|string|max:500',
                'description' => 'nullable|string|max:1000',
                'priority' => 'nullable|integer|min:1',
                'is_active' => 'boolean'
            ]);

            // 우선순위가 지정되지 않은 경우 마지막 순서로 설정
            if (!isset($validated['priority'])) {
                $maxPriority = MonitorRule::byType($validated['rule_type'])->max('priority') ?? 0;
                $validated['priority'] = $maxPriority + 1;
            }

            // 정규식 패턴인 경우 유효성 검증
            if ($validated['rule_type'] === MonitorRule::RULE_TYPE_SIZE_PATTERN) {
                if (@preg_match($validated['pattern'], '') === false) {
                    return $this->errorResponse([
                        'message' => '유효하지 않은 정규식 패턴입니다.',
                        'error_code' => 'INVALID_REGEX_PATTERN',
                        'errors' => [
                            'pattern' => ['정규식 패턴이 올바르지 않습니다.']
                        ]
                    ], null, 'monitor', 422);
                }
            }

            $rule = MonitorRule::create($validated);

            return $this->successResponse([
                'rule' => $rule
            ], "모니터 규칙이 생성되었습니다.", 201);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'monitor', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '모니터 규칙 생성에 실패했습니다.',
                'log_message' => '모니터 규칙 생성 실패: ' . $e->getMessage(),
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 특정 모니터 규칙 조회
     */
    public function show(int $id): JsonResponse
    {
        try {
            $rule = MonitorRule::findOrFail($id);

            return $this->successResponse([
                'rule' => $rule
            ], "모니터 규칙 조회가 완료되었습니다.");
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '해당 모니터 규칙을 찾을 수 없습니다.',
                'error_code' => 'MONITOR_RULE_NOT_FOUND',
            ], $e, 'monitor', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '모니터 규칙 조회에 실패했습니다.',
                'log_message' => '모니터 규칙 조회 실패: ' . $e->getMessage(),
                'error_code' => 'MONITOR_RULE_SHOW_FAILED'
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 모니터 규칙 수정
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $rule = MonitorRule::findOrFail($id);

            $validated = $request->validate([
                'rule_type' => [
                    'sometimes',
                    'string',
                    Rule::in([
                        MonitorRule::RULE_TYPE_BRAND,
                        MonitorRule::RULE_TYPE_EXCLUDE,
                        MonitorRule::RULE_TYPE_SIZE_PATTERN
                    ])
                ],
                'pattern' => 'sometimes|string|max:500',
                'description' => 'nullable|string|max:1000',
                'priority' => 'sometimes|integer|min:1',
                'is_active' => 'sometimes|boolean'
            ]);

            // 정규식 패턴인 경우 유효성 검증
            $ruleType = $validated['rule_type'] ?? $rule->rule_type;
            if ($ruleType === MonitorRule::RULE_TYPE_SIZE_PATTERN && isset($validated['pattern'])) {
                if (@preg_match($validated['pattern'], '') === false) {
                    return $this->errorResponse([
                        'message' => '유효하지 않은 정규식 패턴입니다.',
                        'error_code' => 'INVALID_REGEX_PATTERN',
                        'errors' => [
                            'pattern' => ['정규식 패턴이 올바르지 않습니다.']
                        ]
                    ], null, 'monitor', 422);
                }
            }

            $rule->update($validated);

            return $this->successResponse([
                'rule' => $rule->fresh()
            ], "모니터 규칙이 수정되었습니다.");
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '해당 모니터 규칙을 찾을 수 없습니다.',
                'error_code' => 'MONITOR_RULE_NOT_FOUND',
            ], $e, 'monitor', 404);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'monitor', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '모니터 규칙 수정에 실패했습니다.',
                'log_message' => '모니터 규칙 수정 실패: ' . $e->getMessage(),
                'error_code' => 'MONITOR_RULE_UPDATE_FAILED'
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 모니터 규칙 삭제
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $rule = MonitorRule::findOrFail($id);
            $rule->delete();

            return $this->successResponse([], "모니터 규칙이 삭제되었습니다.");
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '해당 모니터 규칙을 찾을 수 없습니다.',
                'error_code' => 'MONITOR_RULE_NOT_FOUND',
            ], $e, 'monitor', 404);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '모니터 규칙 삭제에 실패했습니다.',
                'log_message' => '모니터 규칙 삭제 실패: ' . $e->getMessage(),
                'error_code' => 'MONITOR_RULE_DELETE_FAILED'
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 규칙 우선순위 재정렬
     *
     * @throws Exception|Throwable
     */
    public function reorderRules(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'rule_ids' => 'required|array',
                'rule_ids.*' => 'required|integer|exists:monitor_rules,id'
            ]);

            DB::beginTransaction();

            $success = MonitorRule::reorderPriorities($validated['rule_ids']);

            if (!$success) {
                DB::rollBack();
                return $this->errorResponse([
                    'message' => '규칙 우선순위 재정렬에 실패했습니다.',
                    'error_code' => 'REORDER_FAILED'
                ], null, 'monitor', 500);
            }

            DB::commit();

            // 재정렬된 규칙 목록 반환
            $reorderedRules = MonitorRule::whereIn('id', $validated['rule_ids'])
                ->orderByPriority()
                ->get();

            return $this->successResponse([
                'rules' => $reorderedRules
            ], "규칙 우선순위가 성공적으로 재정렬되었습니다.");
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'monitor', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '규칙 우선순위 재정렬에 실패했습니다.',
                'log_message' => '규칙 우선순위 재정렬 실패: ' . $e->getMessage(),
                'error_code' => 'REORDER_RULES_FAILED'
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 모니터 크기 추출 테스트
     */
    public function testExtraction(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'product_name' => 'required|string|max:500'
            ]);

            $productName = $validated['product_name'];

            // 테스트 결과 수집
            $testResult = $this->extractionService->testExtraction($productName);

            // 추가 분석 정보
            $analysis = [
                'is_monitor_product' => false, // 기본값으로 설정
                'applied_brand_rules' => [],
                'applied_exclude_rules' => [],
                'applied_size_patterns' => [],
                'extraction_steps' => []
            ];

            // 브랜드 규칙 적용 확인
            foreach ($this->extractionService->getBrandRules() as $rule) {
                if (stripos($productName, $rule->pattern) !== false) {
                    $analysis['applied_brand_rules'][] = [
                        'id' => $rule->id,
                        'pattern' => $rule->pattern,
                        'description' => $rule->description,
                        'priority' => $rule->priority
                    ];
                }
            }

            // 제외 규칙 적용 확인
            foreach ($this->extractionService->getExcludeRules() as $rule) {
                if (stripos($productName, $rule->pattern) !== false) {
                    $analysis['applied_exclude_rules'][] = [
                        'id' => $rule->id,
                        'pattern' => $rule->pattern,
                        'description' => $rule->description,
                        'priority' => $rule->priority
                    ];
                }
            }

            // 크기 패턴 적용 확인
            foreach ($this->extractionService->getSizePatternRules() as $rule) {
                if (preg_match($rule->pattern, $productName, $matches)) {
                    $analysis['applied_size_patterns'][] = [
                        'id' => $rule->id,
                        'pattern' => $rule->pattern,
                        'description' => $rule->description,
                        'priority' => $rule->priority,
                        'matches' => $matches
                    ];
                }
            }

            // 추출 단계 설명
            if (!empty($analysis['applied_exclude_rules'])) {
                $analysis['extraction_steps'][] = '제외 키워드가 발견되어 모니터로 분류되지 않음';
            } elseif (!empty($analysis['applied_brand_rules'])) {
                $analysis['is_monitor_product'] = true;
                $analysis['extraction_steps'][] = '브랜드 키워드가 발견되어 모니터로 분류됨';
                if (!empty($analysis['applied_size_patterns'])) {
                    $analysis['extraction_steps'][] = '크기 패턴이 적용되어 크기 정보 추출됨';
                } else {
                    $analysis['extraction_steps'][] = '크기 패턴이 적용되지 않아 기본값 사용';
                }
            } else {
                $analysis['extraction_steps'][] = '브랜드 키워드가 발견되지 않아 모니터로 분류되지 않음';
            }

            return $this->successResponse([
                'test_result' => $testResult,
                'analysis' => $analysis
            ]);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors()
            ], $e, 'monitor', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '모니터 크기 추출 테스트에 실패했습니다.',
                'log_message' => '모니터 크기 추출 테스트 실패: ' . $e->getMessage(),
                'error_code' => 'EXTRACTION_TEST_FAILED'
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 규칙 캐시 초기화
     */
    public function clearCache(): JsonResponse
    {
        try {
            $this->extractionService->clearRulesCache();

            return $this->successResponse([], '규칙 캐시가 초기화되었습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '규칙 캐시 초기화에 실패했습니다.',
                'log_message' => '규칙 캐시 초기화 실패: ' . $e->getMessage(),
                'error_code' => 'CACHE_CLEAR_FAILED'
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 규칙 통계 조회
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $statistics = [
                'total_rules' => MonitorRule::count(),
                'active_rules' => MonitorRule::where('is_active', true)->count(),
                'inactive_rules' => MonitorRule::where('is_active', false)->count(),
                'by_type' => [
                    'brand' => MonitorRule::byType(MonitorRule::RULE_TYPE_BRAND)->count(),
                    'exclude' => MonitorRule::byType(MonitorRule::RULE_TYPE_EXCLUDE)->count(),
                    'size_pattern' => MonitorRule::byType(MonitorRule::RULE_TYPE_SIZE_PATTERN)->count()
                ],
                'active_by_type' => [
                    'brand' => MonitorRule::active()->byType(MonitorRule::RULE_TYPE_BRAND)->count(),
                    'exclude' => MonitorRule::active()->byType(MonitorRule::RULE_TYPE_EXCLUDE)->count(),
                    'size_pattern' => MonitorRule::active()->byType(MonitorRule::RULE_TYPE_SIZE_PATTERN)->count()
                ]
            ];

            return $this->successResponse($statistics, '규칙 통계를 성공적으로 조회했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '규칙 통계 조회에 실패했습니다.',
                'log_message' => '규칙 통계 조회 실패: ' . $e->getMessage(),
                'error_code' => 'STATISTICS_FAILED'
            ], $e, 'monitor', 500);
        }
    }
}
