<?php

namespace App\Http\Controllers\WMS\Settings;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Services\RepairFeeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class RepairFee<PERSON>ontroller extends Controller
{
    public function __construct(
        private readonly RepairFeeService $repairFeeService
    ) {
    }

    /**
     * 수리비 설정 검색 필드
     */
    public function repairOptions(): JsonResponse
    {
        $repairFeeService = new RepairFeeService();
        $options = $repairFeeService->getRepairOptions();

        return $this->successResponse([
            'options' => $options,
        ]);
    }

    /**
     * 수리비 리스트
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $data = [
            'cate4' => $request->input('cate4'),
            'cate5' => $request->input('cate5'),
            'repair_range_type' => $request->input('repair_range_type'),
            'repair_range_model' => $request->input('repair_range_model'),
            'repair_range_fee_type' => $request->input('repair_range_fee_type'),
            'repair_range_fee_unit' => $request->input('repair_range_fee_unit'),
            'repair_type' => $request->input('repair_type'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        $items = $this->repairFeeService->getRepairFeeList($data);

        // 페이지네이션 및 코드값 변환
        $paginatedResult = $items->paginate($data['pageSize'])->withQueryString();
        $translatedItems = $this->repairFeeService->translateCodeToName($paginatedResult);

        return $this->successResponse([
            'items' => $translatedItems->items(),
            'pagination' => PaginationHelper::optimize($translatedItems),
        ]);
    }

    /**
     * @param  Request  $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        $data = $request->all();

        try {
            $result = $this->repairFeeService->storeRepairFee($data);

            return $this->successResponse($result);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }

    /**
     * @param  Request  $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function update(Request $request): JsonResponse
    {
        $data = $request->all();

        try {
             $this->repairFeeService->updateRepairFee($data);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }

    /**
     * @param int $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->repairFeeService->destroyRepairFee($id);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }
}
