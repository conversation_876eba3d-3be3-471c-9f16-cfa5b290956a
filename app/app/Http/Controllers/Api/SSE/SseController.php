<?php

namespace App\Http\Controllers\Api\SSE;

use App\Http\Controllers\Controller;
use App\Http\Middleware\SseSecurityMiddleware;
use App\Models\User;
use App\Services\ConnectionManager;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * SSE(Server-Sent Events) 컨트롤러
 *
 * 실시간 알림 및 데이터 전송을 위한 SSE 연결을 관리합니다.
 * Laravel Sanctum 세션 인증을 활용하여 보안을 유지합니다.
 */
class SseController extends Controller
{
    private ConnectionManager $connectionManager;
    private SseSecurityMiddleware $securityMiddleware;
    private const HEARTBEAT_INTERVAL = 30; // 30초
    private const CONNECTION_TIMEOUT = 300; // 5분

    public function __construct(
        ConnectionManager $connectionManager,
        SseSecurityMiddleware $securityMiddleware
    ) {
        $this->connectionManager = $connectionManager;
        $this->securityMiddleware = $securityMiddleware;
    }

    /**
     * SSE 스트림 엔드포인트
     *
     * 클라이언트와 SSE 연결을 설정하고 실시간 데이터를 전송합니다.
     *
     * @param Request $request HTTP 요청
     * @return StreamedResponse SSE 스트림 응답
     */
    public function stream(Request $request): StreamedResponse
    {
        // 고유 연결 ID 생성
        $connectionId = Str::uuid()->toString();

        // 사용자 인증 확인
        $user = $this->authenticateUser($request);

        Log::info('SSE 스트림 연결 시작', [
            'connection_id' => $connectionId,
            'user_id' => $user?->id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return new StreamedResponse(function () use ($connectionId, $user, $request) {
            $this->handleConnection($connectionId, $user, $request);
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no', // Nginx 버퍼링 비활성화
        ]);
    }

    /**
     * 연결 정보 조회 엔드포인트
     *
     * @param Request $request HTTP 요청
     * @return JsonResponse 연결 정보 응답
     */
    public function connect(Request $request): JsonResponse
    {
        try {
            $user = $this->authenticateUser($request);
            $stats = $this->connectionManager->getConnectionStats();

            return response()->json([
                'success' => true,
                'data' => [
                    'user_id' => $user?->id,
                    'is_authenticated' => $user !== null,
                    'connection_stats' => $stats,
                    'heartbeat_interval' => self::HEARTBEAT_INTERVAL
                ]
            ]);
        } catch (Exception $e) {
            Log::error('SSE 연결 정보 조회 실패', [
                'error' => $e->getMessage(),
                'ip_address' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'SSE 연결 정보를 가져올 수 없습니다.'
            ], 500);
        }
    }

    /**
     * 연결 해제 엔드포인트
     *
     * @param Request $request HTTP 요청
     * @return JsonResponse 연결 해제 응답
     */
    public function disconnect(Request $request): JsonResponse
    {
        try {
            $connectionId = $request->input('connection_id');

            if (!$connectionId) {
                return response()->json([
                    'success' => false,
                    'message' => '연결 ID가 필요합니다.'
                ], 400);
            }

            $this->connectionManager->removeConnection($connectionId);

            Log::info('SSE 연결 해제 요청 처리', [
                'connection_id' => $connectionId,
                'ip_address' => $request->ip()
            ]);

            return response()->json([
                'success' => true,
                'message' => '연결이 성공적으로 해제되었습니다.'
            ]);
        } catch (Exception $e) {
            Log::error('SSE 연결 해제 실패', [
                'error' => $e->getMessage(),
                'connection_id' => $request->input('connection_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '연결 해제 중 오류가 발생했습니다.'
            ], 500);
        }
    }

    /**
     * SSE 연결을 처리합니다.
     *
     * @param string $connectionId 연결 ID
     * @param User|null $user 인증된 사용자 (게스트인 경우 null)
     * @param Request $request HTTP 요청
     * @return void
     */
    private function handleConnection(string $connectionId, ?User $user, Request $request): void
    {
        try {
            // 연결 메타데이터 준비
            $metadata = [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'subscriptions' => $this->getSubscriptions($request)
            ];

            // 연결 등록
            $this->connectionManager->addConnection(
                $connectionId,
                $user?->id,
                $metadata
            );

            // 보안 미들웨어 연결 카운터 증가
            $this->securityMiddleware->incrementIpConnections($request->ip());
            if ($user) {
                $this->securityMiddleware->incrementUserConnections($user->id);
            }

            // 초기 연결 확인 메시지 전송
            $this->sendConnectionEstablished($connectionId, $user);

            // 연결 유지 루프
            $this->maintainConnection($connectionId);

        } catch (Exception $e) {
            Log::error('SSE 연결 처리 중 오류 발생', [
                'connection_id' => $connectionId,
                'user_id' => $user?->id,
                'error' => $e->getMessage()
            ]);
        } finally {
            // 연결 정리
            $this->connectionManager->removeConnection($connectionId);

            // 보안 미들웨어 연결 카운터 감소
            $this->securityMiddleware->decrementIpConnections($request->ip());
            if ($user) {
                $this->securityMiddleware->decrementUserConnections($user->id);
            }

            Log::info('SSE 연결 종료', [
                'connection_id' => $connectionId,
                'user_id' => $user?->id
            ]);
        }
    }

    /**
     * 연결을 유지하고 하트비트를 전송합니다.
     *
     * @param string $connectionId 연결 ID
     * @return void
     */
    private function maintainConnection(string $connectionId): void
    {
        $startTime = time();
        $lastHeartbeat = time();

        while (true) {
            // 연결 타임아웃 확인
            if (time() - $startTime > self::CONNECTION_TIMEOUT) {
                Log::info('SSE 연결 타임아웃', ['connection_id' => $connectionId]);
                break;
            }

            // 클라이언트 연결 상태 확인
            if (connection_aborted()) {
                Log::info('클라이언트가 SSE 연결을 종료했습니다', [
                    'connection_id' => $connectionId
                ]);
                break;
            }

            // 하트비트 전송
            if (time() - $lastHeartbeat >= self::HEARTBEAT_INTERVAL) {
                if (!$this->sendHeartbeat($connectionId)) {
                    Log::warning('하트비트 전송 실패, 연결 종료', [
                        'connection_id' => $connectionId
                    ]);
                    break;
                }
                $lastHeartbeat = time();
            }

            // CPU 사용량 최적화를 위한 짧은 대기
            usleep(100000); // 0.1초
        }
    }

    /**
     * 하트비트 메시지를 전송합니다.
     *
     * @param string $connectionId 연결 ID
     * @return bool 전송 성공 여부
     */
    private function sendHeartbeat(string $connectionId): bool
    {
        try {
            // 하트비트 시간 업데이트
            if (!$this->connectionManager->updateHeartbeat($connectionId)) {
                return false;
            }

            // 하트비트 메시지 전송
            $heartbeatData = [
                'type' => 'heartbeat',
                'timestamp' => Carbon::now()->toISOString(),
                'connection_id' => $connectionId
            ];

            echo "data: " . json_encode($heartbeatData) . "\n\n";

            if (ob_get_level()) {
                ob_flush();
            }
            flush();

            return true;
        } catch (Exception $e) {
            Log::error('하트비트 전송 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 연결 설정 완료 메시지를 전송합니다.
     *
     * @param string $connectionId 연결 ID
     * @param User|null $user 사용자 정보
     * @return void
     */
    private function sendConnectionEstablished(string $connectionId, ?User $user): void
    {
        try {
            $connectionData = [
                'type' => 'connection_established',
                'timestamp' => Carbon::now()->toISOString(),
                'data' => [
                    'connection_id' => $connectionId,
                    'user_id' => $user?->id,
                    'is_authenticated' => $user !== null,
                    'heartbeat_interval' => self::HEARTBEAT_INTERVAL,
                    'message' => $user
                        ? "안녕하세요, {$user->name}님! SSE 연결이 설정되었습니다."
                        : 'SSE 연결이 설정되었습니다. (게스트 모드)'
                ]
            ];

            echo "data: " . json_encode($connectionData) . "\n\n";

            if (ob_get_level()) {
                ob_flush();
            }
            flush();

            Log::info('SSE 연결 설정 완료 메시지 전송', [
                'connection_id' => $connectionId,
                'user_id' => $user?->id
            ]);
        } catch (Exception $e) {
            Log::error('연결 설정 메시지 전송 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 요청에서 사용자 인증을 확인합니다.
     *
     * @param Request $request HTTP 요청
     * @return User|null 인증된 사용자 또는 null (게스트)
     */
    private function authenticateUser(Request $request): ?User
    {
        try {
            // Laravel Sanctum 세션 인증 확인
            if (Auth::check()) {
                $user = Auth::user();

                Log::debug('SSE 사용자 인증 성공', [
                    'user_id' => $user->id,
                    'user_name' => $user->name
                ]);

                return $user;
            }

            Log::debug('SSE 게스트 연결', [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('SSE 사용자 인증 중 오류 발생', [
                'error' => $e->getMessage(),
                'ip_address' => $request->ip()
            ]);

            return null;
        }
    }

    /**
     * 요청에서 구독 정보를 추출합니다.
     *
     * @param Request $request HTTP 요청
     * @return array 구독 목록
     */
    private function getSubscriptions(Request $request): array
    {
        $subscriptions = $request->input('subscriptions', ['notifications']);

        // 유효한 구독 타입만 허용
        $validSubscriptions = ['notifications', 'categories', 'system'];

        return array_intersect($subscriptions, $validSubscriptions);
    }
}
