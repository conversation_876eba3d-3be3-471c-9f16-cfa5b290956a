<?php

namespace App\Http\Controllers\Api\SSE;

use App\Http\Controllers\Controller;
use App\Services\HybridNotificationService;
use App\Services\MigrationTrackingService;
use App\Services\PusherDependencyRemovalService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 마이그레이션 대시보드 컨트롤러
 *
 * SSE 마이그레이션 진행 상황 모니터링 대시보드 API를 제공합니다.
 */
class MigrationDashboardController extends Controller
{
    private MigrationTrackingService $trackingService;
    private PusherDependencyRemovalService $removalService;
    private HybridNotificationService $hybridService;

    public function __construct(
        MigrationTrackingService $trackingService,
        PusherDependencyRemovalService $removalService,
        HybridNotificationService $hybridService
    ) {
        $this->trackingService = $trackingService;
        $this->removalService = $removalService;
        $this->hybridService = $hybridService;
    }

    /**
     * 대시보드 메인 데이터 조회
     */
    public function dashboard(Request $request): JsonResponse
    {
        try {
            $days = $request->input('days', 7);

            if ($days < 1 || $days > 30) {
                return response()->json([
                    'success' => false,
                    'message' => '조회 기간은 1일에서 30일 사이여야 합니다.'
                ], 400);
            }

            $dashboardData = [
                'overview' => $this->getDashboardOverview(),
                'migration_progress' => $this->trackingService->calculateMigrationProgress($days),
                'system_health' => $this->getSystemHealthSummary(),
                'recent_activities' => $this->getRecentActivities(),
                'alerts' => $this->getActiveAlerts(),
                'quick_stats' => $this->getQuickStats()
            ];

            return response()->json([
                'success' => true,
                'data' => $dashboardData,
                'generated_at' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('대시보드 데이터 조회 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '대시보드 데이터 조회 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 상세 마이그레이션 리포트 조회
     */
    public function detailedReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'days' => 'integer|min:1|max:30',
            'include_details' => 'boolean',
            'include_recommendations' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '입력 데이터가 올바르지 않습니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $options = [
                'days' => $request->input('days', 7),
                'include_details' => $request->input('include_details', true),
                'include_recommendations' => $request->input('include_recommendations', true)
            ];

            $report = $this->trackingService->generateMigrationReport($options);

            return response()->json([
                'success' => true,
                'data' => $report
            ]);

        } catch (\Exception $e) {
            Log::error('상세 리포트 생성 실패', [
                'error' => $e->getMessage(),
                'options' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '리포트 생성 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Pusher 제거 체크리스트 조회
     */
    public function removalChecklist(): JsonResponse
    {
        try {
            $checklist = $this->removalService->generateRemovalChecklist();

            return response()->json([
                'success' => true,
                'data' => $checklist,
                'generated_at' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('제거 체크리스트 생성 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '체크리스트 생성 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * SSE 전환율 기록
     */
    public function recordConversion(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'successful' => 'required|boolean',
            'metadata' => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '입력 데이터가 올바르지 않습니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $userId = $request->input('user_id');
            $successful = $request->input('successful');
            $metadata = $request->input('metadata', []);

            $this->trackingService->recordSSEConversion($userId, $successful);

            Log::info('SSE 전환율 수동 기록', [
                'user_id' => $userId,
                'successful' => $successful,
                'metadata' => $metadata,
                'recorded_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'SSE 전환율이 기록되었습니다.',
                'data' => [
                    'user_id' => $userId,
                    'successful' => $successful,
                    'recorded_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('SSE 전환율 기록 실패', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '전환율 기록 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 실시간 통계 조회
     */
    public function realtimeStats(): JsonResponse
    {
        try {
            $stats = [
                'current_connections' => $this->getCurrentConnections(),
                'message_throughput' => $this->getMessageThroughput(),
                'error_rate' => $this->getCurrentErrorRate(),
                'sse_usage_percentage' => $this->getSSEUsagePercentage(),
                'system_status' => $this->getSystemStatus(),
                'last_updated' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('실시간 통계 조회 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '실시간 통계 조회 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 알림 설정 업데이트
     */
    public function updateAlertSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'error_rate_threshold' => 'numeric|min:0|max:100',
            'conversion_rate_threshold' => 'numeric|min:0|max:100',
            'connection_threshold' => 'integer|min:0',
            'notification_channels' => 'array',
            'notification_channels.*' => 'string|in:email,slack,webhook'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '입력 데이터가 올바르지 않습니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $settings = $request->only([
                'error_rate_threshold',
                'conversion_rate_threshold',
                'connection_threshold',
                'notification_channels'
            ]);

            // 설정 저장 (실제 구현에서는 데이터베이스나 설정 파일에 저장)
            \Illuminate\Support\Facades\Cache::put('migration_alert_settings', $settings, 86400);

            Log::info('마이그레이션 알림 설정 업데이트', [
                'settings' => $settings,
                'updated_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => '알림 설정이 업데이트되었습니다.',
                'data' => $settings
            ]);

        } catch (\Exception $e) {
            Log::error('알림 설정 업데이트 실패', [
                'error' => $e->getMessage(),
                'settings' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '알림 설정 업데이트 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 대시보드 개요 정보
     */
    private function getDashboardOverview(): array
    {
        $migrationProgress = $this->hybridService->getMigrationProgress();

        return [
            'migration_status' => $this->getMigrationStatus($migrationProgress),
            'current_mode' => $this->getCurrentMode(),
            'total_users' => $this->getTotalUsers(),
            'sse_adoption_rate' => $migrationProgress['actual_sse_usage'],
            'days_since_migration_start' => $this->getDaysSinceMigrationStart()
        ];
    }

    /**
     * 마이그레이션 상태 결정
     */
    private function getMigrationStatus(array $progress): string
    {
        $sseUsage = $progress['actual_sse_usage'];

        if ($sseUsage >= 95) {
            return 'ready_for_completion';
        } elseif ($sseUsage >= 80) {
            return 'nearing_completion';
        } elseif ($sseUsage >= 50) {
            return 'in_progress';
        } elseif ($sseUsage >= 10) {
            return 'early_stage';
        } else {
            return 'not_started';
        }
    }

    /**
     * 현재 모드 조회
     */
    private function getCurrentMode(): string
    {
        $hybridMode = config('sse.migration.hybrid_mode', false);
        $pusherFallback = config('sse.migration.pusher_fallback_enabled', false);

        if ($hybridMode) {
            return 'hybrid';
        } elseif ($pusherFallback) {
            return 'sse_with_fallback';
        } else {
            return 'sse_only';
        }
    }

    /**
     * 전체 사용자 수 조회
     */
    private function getTotalUsers(): int
    {
        // 실제 구현에서는 User 모델에서 조회
        return \Illuminate\Support\Facades\Cache::get('total_users_count', 0);
    }

    /**
     * 마이그레이션 시작 후 경과 일수
     */
    private function getDaysSinceMigrationStart(): int
    {
        $startDate = \Illuminate\Support\Facades\Cache::get('migration_start_date');

        if ($startDate) {
            return now()->diffInDays(\Carbon\Carbon::parse($startDate));
        }

        return 0;
    }

    /**
     * 시스템 건강 상태 요약
     */
    private function getSystemHealthSummary(): array
    {
        return [
            'overall_status' => 'healthy', // healthy, warning, critical
            'sse_service' => 'healthy',
            'pusher_service' => 'healthy',
            'redis_service' => 'healthy',
            'last_check' => now()->toISOString()
        ];
    }

    /**
     * 최근 활동 조회
     */
    private function getRecentActivities(): array
    {
        // 실제 구현에서는 활동 로그에서 조회
        return [
            [
                'type' => 'conversion_milestone',
                'message' => 'SSE 전환율이 80%에 도달했습니다.',
                'timestamp' => now()->subHours(2)->toISOString(),
                'severity' => 'info'
            ],
            [
                'type' => 'system_alert',
                'message' => '오류율이 임계값을 초과했습니다.',
                'timestamp' => now()->subHours(6)->toISOString(),
                'severity' => 'warning'
            ]
        ];
    }

    /**
     * 활성 알림 조회
     */
    private function getActiveAlerts(): array
    {
        return [
            [
                'id' => 'alert_001',
                'type' => 'error_rate',
                'title' => '높은 오류율 감지',
                'message' => '지난 1시간 동안 오류율이 5%를 초과했습니다.',
                'severity' => 'warning',
                'created_at' => now()->subMinutes(30)->toISOString(),
                'acknowledged' => false
            ]
        ];
    }

    /**
     * 빠른 통계 조회
     */
    private function getQuickStats(): array
    {
        return [
            'messages_sent_today' => 1250,
            'active_connections' => 45,
            'conversion_rate_today' => 78.5,
            'uptime_percentage' => 99.9
        ];
    }

    /**
     * 현재 연결 수 조회
     */
    private function getCurrentConnections(): array
    {
        return [
            'sse_connections' => \Illuminate\Support\Facades\Cache::get('sse:active_connections', 0),
            'pusher_connections' => \Illuminate\Support\Facades\Cache::get('pusher_active_connections', 0),
            'total_connections' => \Illuminate\Support\Facades\Cache::get('total_active_connections', 0)
        ];
    }

    /**
     * 메시지 처리량 조회
     */
    private function getMessageThroughput(): array
    {
        $hour = now()->format('Y-m-d-H');
        $stats = \Illuminate\Support\Facades\Cache::get("delivery_stats:sse:{$hour}", 0) +
                \Illuminate\Support\Facades\Cache::get("delivery_stats:pusher:{$hour}", 0);

        return [
            'messages_per_hour' => $stats,
            'messages_per_minute' => round($stats / 60, 2),
            'peak_hour_today' => $this->getPeakHourToday()
        ];
    }

    /**
     * 오늘의 피크 시간 조회
     */
    private function getPeakHourToday(): array
    {
        $today = now()->format('Y-m-d');
        $peakHour = 0;
        $peakCount = 0;

        for ($hour = 0; $hour < 24; $hour++) {
            $hourKey = "{$today}-" . str_pad($hour, 2, '0', STR_PAD_LEFT);
            $hourCount = \Illuminate\Support\Facades\Cache::get("delivery_stats:sse:{$hourKey}", 0) +
                        \Illuminate\Support\Facades\Cache::get("delivery_stats:pusher:{$hourKey}", 0);

            if ($hourCount > $peakCount) {
                $peakCount = $hourCount;
                $peakHour = $hour;
            }
        }

        return [
            'hour' => $peakHour,
            'message_count' => $peakCount
        ];
    }

    /**
     * 현재 오류율 조회
     */
    private function getCurrentErrorRate(): float
    {
        $hour = now()->format('Y-m-d-H');
        $errorStats = \Illuminate\Support\Facades\Cache::get("error_rates:{$hour}", [
            'total_requests' => 0,
            'error_count' => 0
        ]);

        if ($errorStats['total_requests'] > 0) {
            return round(($errorStats['error_count'] / $errorStats['total_requests']) * 100, 2);
        }

        return 0;
    }

    /**
     * SSE 사용 비율 조회
     */
    private function getSSEUsagePercentage(): float
    {
        $progress = $this->hybridService->getMigrationProgress();
        return $progress['actual_sse_usage'];
    }

    /**
     * 시스템 상태 조회
     */
    private function getSystemStatus(): string
    {
        $errorRate = $this->getCurrentErrorRate();
        $sseUsage = $this->getSSEUsagePercentage();

        if ($errorRate > 5) {
            return 'critical';
        } elseif ($errorRate > 2 || $sseUsage < 50) {
            return 'warning';
        } else {
            return 'healthy';
        }
    }
}
