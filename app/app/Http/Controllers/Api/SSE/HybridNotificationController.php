<?php

namespace App\Http\Controllers\Api\SSE;

use App\Http\Controllers\Controller;
use App\Http\Middleware\SSECapabilityDetectionMiddleware;
use App\Services\HybridNotificationService;
use App\Services\MessageSynchronizationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 하이브리드 알림 컨트롤러
 *
 * SSE와 Pusher 하이브리드 시스템의 관리 API를 제공합니다.
 */
class HybridNotificationController extends Controller
{
    private HybridNotificationService $hybridService;
    private MessageSynchronizationService $syncService;

    public function __construct(
        HybridNotificationService $hybridService,
        MessageSynchronizationService $syncService
    ) {
        $this->hybridService = $hybridService;
        $this->syncService = $syncService;
    }

    /**
     * 하이브리드 알림 전송
     */
    public function sendNotification(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:broadcast,user',
            'user_id' => 'required_if:type,user|integer|exists:users,id',
            'data' => 'required|array',
            'notification_type' => 'string|in:notification,data_update,system',
            'priority' => 'string|in:low,normal,high,urgent'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '입력 데이터가 올바르지 않습니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $data = $request->input('data');
            $notificationType = $request->input('notification_type', 'notification');
            $type = $request->input('type');

            // 메시지 ID 생성
            $messageId = uniqid('hybrid_', true);
            $data['message_id'] = $messageId;
            $data['priority'] = $request->input('priority', 'normal');

            if ($type === 'broadcast') {
                $this->hybridService->sendToAll($data, $notificationType);

                Log::info('하이브리드 전체 알림 전송', [
                    'message_id' => $messageId,
                    'type' => $notificationType,
                    'data_size' => strlen(json_encode($data))
                ]);

            } else {
                $userId = $request->input('user_id');
                $this->hybridService->sendToUser($userId, $data, $notificationType);

                Log::info('하이브리드 개별 알림 전송', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'type' => $notificationType,
                    'data_size' => strlen(json_encode($data))
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => '알림이 성공적으로 전송되었습니다.',
                'message_id' => $messageId
            ]);

        } catch (\Exception $e) {
            Log::error('하이브리드 알림 전송 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '알림 전송 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 클라이언트 SSE 지원 상태 업데이트
     */
    public function updateSSESupport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'supports_sse' => 'required|boolean',
            'user_agent' => 'string',
            'client_info' => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '입력 데이터가 올바르지 않습니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        $userId = auth()->id();
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => '인증이 필요합니다.'
            ], 401);
        }

        try {
            $supportsSSE = $request->input('supports_sse');
            $this->hybridService->updateUserSSESupport($userId, $supportsSSE);

            Log::info('사용자 SSE 지원 상태 업데이트', [
                'user_id' => $userId,
                'supports_sse' => $supportsSSE,
                'user_agent' => $request->input('user_agent'),
                'client_info' => $request->input('client_info', [])
            ]);

            return response()->json([
                'success' => true,
                'message' => 'SSE 지원 상태가 업데이트되었습니다.',
                'supports_sse' => $supportsSSE
            ]);

        } catch (\Exception $e) {
            Log::error('SSE 지원 상태 업데이트 실패', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'SSE 지원 상태 업데이트 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 하이브리드 설정 업데이트
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'hybrid_mode' => 'boolean',
            'pusher_fallback_enabled' => 'boolean',
            'migration_percentage' => 'integer|min:0|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '입력 데이터가 올바르지 않습니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $settings = $request->only(['hybrid_mode', 'pusher_fallback_enabled', 'migration_percentage']);
            $this->hybridService->updateHybridSettings($settings);

            Log::info('하이브리드 설정 업데이트', [
                'settings' => $settings,
                'updated_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => '하이브리드 설정이 업데이트되었습니다.',
                'settings' => $settings
            ]);

        } catch (\Exception $e) {
            Log::error('하이브리드 설정 업데이트 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'settings' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '설정 업데이트 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 마이그레이션 진행 상황 조회
     */
    public function getMigrationProgress(): JsonResponse
    {
        try {
            $progress = $this->hybridService->getMigrationProgress();
            $deliveryStats = $this->hybridService->getDeliveryStats();
            $syncStats = $this->syncService->getSynchronizationStats();
            $capabilityStats = SSECapabilityDetectionMiddleware::getCapabilityStats();

            return response()->json([
                'success' => true,
                'data' => [
                    'migration_progress' => $progress,
                    'delivery_stats' => $deliveryStats,
                    'synchronization_stats' => $syncStats,
                    'capability_stats' => $capabilityStats
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('마이그레이션 진행 상황 조회 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '진행 상황 조회 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 메시지 동기화 상태 확인
     */
    public function checkMessageSync(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '메시지 ID가 필요합니다.',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $messageId = $request->input('message_id');
            $syncStatus = $this->syncService->checkSynchronizationStatus($messageId);

            return response()->json([
                'success' => true,
                'data' => $syncStatus
            ]);

        } catch (\Exception $e) {
            Log::error('메시지 동기화 상태 확인 실패', [
                'message_id' => $request->input('message_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '동기화 상태 확인 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 시스템 상태 확인
     */
    public function getSystemStatus(): JsonResponse
    {
        try {
            $status = [
                'sse_enabled' => true,
                'pusher_enabled' => config('broadcasting.connections.pusher.key') !== null,
                'hybrid_mode' => config('sse.migration.hybrid_mode', false),
                'pusher_fallback' => config('sse.migration.pusher_fallback_enabled', false),
                'migration_percentage' => config('sse.migration.migration_percentage', 0),
                'redis_connected' => $this->checkRedisConnection(),
                'timestamp' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('시스템 상태 확인 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '시스템 상태 확인 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 동기화 통계 조회
     */
    public function getSyncStats(Request $request): JsonResponse
    {
        $hours = $request->input('hours', 24);

        if ($hours < 1 || $hours > 168) { // 최대 1주일
            return response()->json([
                'success' => false,
                'message' => '시간 범위는 1시간에서 168시간(1주일) 사이여야 합니다.'
            ], 400);
        }

        try {
            $stats = $this->syncService->getSynchronizationStats($hours);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('동기화 통계 조회 실패', [
                'hours' => $hours,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '통계 조회 중 오류가 발생했습니다.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Redis 연결 상태 확인
     */
    private function checkRedisConnection(): bool
    {
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
