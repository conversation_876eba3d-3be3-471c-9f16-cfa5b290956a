<?php

namespace App\Http\Controllers\Api\SSE;

use App\Http\Controllers\Controller;
use App\Services\ConnectionManager;
use App\Services\PerformanceOptimizer;
use App\Services\SseMonitoringService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

/**
 * SSE 모니터링 컨트롤러
 *
 * SSE 시스템의 실시간 모니터링 및 성능 메트릭을 제공합니다.
 */
class SseMonitoringController extends Controller
{
    private ConnectionManager $connectionManager;
    private SseMonitoringService $monitoringService;
    private PerformanceOptimizer $performanceOptimizer;

    public function __construct(
        ConnectionManager $connectionManager,
        SseMonitoringService $monitoringService,
        PerformanceOptimizer $performanceOptimizer
    ) {
        $this->connectionManager = $connectionManager;
        $this->monitoringService = $monitoringService;
        $this->performanceOptimizer = $performanceOptimizer;
    }

    /**
     * 실시간 모니터링 대시보드 데이터
     */
    public function dashboard(): JsonResponse
    {
        try {
            $metrics = [
                'timestamp' => now()->toISOString(),
                'connections' => $this->getConnectionMetrics(),
                'performance' => $this->getPerformanceMetrics(),
                'system' => $this->getSystemMetrics(),
                'redis' => $this->getRedisMetrics(),
                'alerts' => $this->getActiveAlerts()
            ];

            return response()->json([
                'success' => true,
                'data' => $metrics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '모니터링 데이터 수집 실패: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 연결 통계 조회
     */
    public function connectionStats(): JsonResponse
    {
        try {
            $stats = [
                'current_connections' => $this->connectionManager->getActiveConnectionCount(),
                'authenticated_connections' => $this->connectionManager->getAuthenticatedConnectionCount(),
                'guest_connections' => $this->connectionManager->getGuestConnectionCount(),
                'connections_by_hour' => $this->getConnectionsByHour(),
                'top_users' => $this->getTopConnectedUsers(),
                'connection_duration' => $this->getAverageConnectionDuration()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '연결 통계 조회 실패: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 메시지 통계 조회
     */
    public function messageStats(): JsonResponse
    {
        try {
            $stats = [
                'total_messages_sent' => $this->monitoringService->getTotalMessagesSent(),
                'messages_sent_today' => $this->monitoringService->getMessagesSentToday(),
                'messages_per_hour' => $this->getMessagesPerHour(),
                'message_types' => $this->getMessageTypeDistribution(),
                'failed_messages' => $this->monitoringService->getFailedMessagesCount(),
                'average_message_size' => $this->getAverageMessageSize(),
                'delivery_success_rate' => $this->getDeliverySuccessRate()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '메시지 통계 조회 실패: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 성능 메트릭 조회
     */
    public function performanceMetrics(): JsonResponse
    {
        try {
            $metrics = [
                'response_times' => $this->getResponseTimeMetrics(),
                'throughput' => $this->getThroughputMetrics(),
                'memory_usage' => $this->getMemoryUsageMetrics(),
                'cpu_usage' => $this->getCpuUsageMetrics(),
                'network_io' => $this->getNetworkIOMetrics(),
                'error_rates' => $this->getErrorRateMetrics()
            ];

            return response()->json([
                'success' => true,
                'data' => $metrics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '성능 메트릭 조회 실패: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 시스템 상태 확인
     */
    public function healthCheck(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabaseHealth(),
            'redis' => $this->checkRedisHealth(),
            'sse_service' => $this->checkSseServiceHealth(),
            'memory' => $this->checkMemoryHealth(),
            'disk' => $this->checkDiskHealth()
        ];

        $overallHealth = collect($checks)->every(fn($check) => $check['status'] === 'healthy');

        return response()->json([
            'success' => true,
            'overall_status' => $overallHealth ? 'healthy' : 'unhealthy',
            'checks' => $checks,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * 알림 및 경고 조회
     */
    public function alerts(): JsonResponse
    {
        try {
            $alerts = [
                'critical' => $this->getCriticalAlerts(),
                'warnings' => $this->getWarningAlerts(),
                'info' => $this->getInfoAlerts(),
                'resolved' => $this->getResolvedAlerts()
            ];

            return response()->json([
                'success' => true,
                'data' => $alerts
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => '알림 조회 실패: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 실시간 로그 스트림
     */
    public function logStream(Request $request)
    {
        $level = $request->get('level', 'info');
        $limit = min($request->get('limit', 100), 1000);

        return response()->stream(function () use ($level, $limit) {
            $this->sendSseHeaders();

            // 초기 로그 데이터 전송
            $logs = $this->monitoringService->getRecentLogs($level, $limit);
            $this->sendSseData('initial_logs', $logs);

            // 실시간 로그 스트림
            $lastLogId = $logs->last()?->id ?? 0;

            while (true) {
                $newLogs = $this->monitoringService->getLogsAfter($lastLogId, $level);

                if ($newLogs->isNotEmpty()) {
                    foreach ($newLogs as $log) {
                        $this->sendSseData('new_log', $log);
                        $lastLogId = $log->id;
                    }
                }

                // 연결 상태 확인
                if (connection_aborted()) {
                    break;
                }

                sleep(1); // 1초 간격으로 확인
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    /**
     * 연결 메트릭 수집
     */
    private function getConnectionMetrics(): array
    {
        return [
            'total' => $this->connectionManager->getActiveConnectionCount(),
            'authenticated' => $this->connectionManager->getAuthenticatedConnectionCount(),
            'guest' => $this->connectionManager->getGuestConnectionCount(),
            'by_user_agent' => $this->getConnectionsByUserAgent(),
            'by_ip' => $this->getConnectionsByIP(),
            'average_duration' => $this->getAverageConnectionDuration()
        ];
    }

    /**
     * 성능 메트릭 수집
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => $this->getMemoryLimit()
            ],
            'response_time' => [
                'average' => $this->monitoringService->getAverageResponseTime(),
                'p95' => $this->monitoringService->getResponseTimePercentile(95),
                'p99' => $this->monitoringService->getResponseTimePercentile(99)
            ],
            'throughput' => [
                'messages_per_second' => $this->monitoringService->getMessagesPerSecond(),
                'connections_per_second' => $this->monitoringService->getConnectionsPerSecond()
            ]
        ];
    }

    /**
     * 시스템 메트릭 수집
     */
    private function getSystemMetrics(): array
    {
        return [
            'load_average' => sys_getloadavg(),
            'disk_usage' => $this->getDiskUsage(),
            'network_stats' => $this->getNetworkStats(),
            'process_count' => $this->getProcessCount()
        ];
    }

    /**
     * Redis 메트릭 수집
     */
    private function getRedisMetrics(): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();

            return [
                'connected_clients' => $info['connected_clients'] ?? 0,
                'used_memory' => $info['used_memory'] ?? 0,
                'used_memory_human' => $info['used_memory_human'] ?? '0B',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateRedisHitRate($info),
                'keys_count' => $this->getRedisKeysCount()
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Redis 메트릭 수집 실패: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 활성 알림 조회
     */
    private function getActiveAlerts(): array
    {
        $alerts = [];

        // 메모리 사용량 경고
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->getMemoryLimit();
        if ($memoryUsage > $memoryLimit * 0.8) {
            $alerts[] = [
                'type' => 'warning',
                'message' => '메모리 사용량이 80%를 초과했습니다.',
                'value' => round(($memoryUsage / $memoryLimit) * 100, 1) . '%',
                'timestamp' => now()->toISOString()
            ];
        }

        // 연결 수 경고
        $connectionCount = $this->connectionManager->getActiveConnectionCount();
        if ($connectionCount > 1000) {
            $alerts[] = [
                'type' => 'warning',
                'message' => '활성 연결 수가 1000개를 초과했습니다.',
                'value' => $connectionCount,
                'timestamp' => now()->toISOString()
            ];
        }

        // 오류율 경고
        $errorRate = $this->monitoringService->getErrorRate();
        if ($errorRate > 5) {
            $alerts[] = [
                'type' => 'critical',
                'message' => '오류율이 5%를 초과했습니다.',
                'value' => round($errorRate, 2) . '%',
                'timestamp' => now()->toISOString()
            ];
        }

        return $alerts;
    }

    /**
     * 시간별 연결 수 조회
     */
    private function getConnectionsByHour(): array
    {
        $hours = [];
        for ($i = 23; $i >= 0; $i--) {
            $hour = now()->subHours($i)->format('H:00');
            $count = Cache::get("sse:connections:hour:{$hour}", 0);
            $hours[] = ['hour' => $hour, 'count' => $count];
        }
        return $hours;
    }

    /**
     * 최다 연결 사용자 조회
     */
    private function getTopConnectedUsers(): array
    {
        // Redis에서 사용자별 연결 수 조회
        $userConnections = [];
        $pattern = 'sse:user_connections:*';
        $keys = Redis::keys($pattern);

        foreach ($keys as $key) {
            $userId = str_replace('sse:user_connections:', '', $key);
            $connectionCount = Redis::scard($key);
            if ($connectionCount > 0) {
                $userConnections[] = [
                    'user_id' => $userId,
                    'connection_count' => $connectionCount
                ];
            }
        }

        // 연결 수 기준으로 정렬
        usort($userConnections, fn($a, $b) => $b['connection_count'] - $a['connection_count']);

        return array_slice($userConnections, 0, 10); // 상위 10명
    }

    /**
     * 평균 연결 지속 시간 계산
     */
    private function getAverageConnectionDuration(): float
    {
        $connections = $this->connectionManager->getActiveConnections();
        $totalDuration = 0;
        $count = 0;

        foreach ($connections as $connectionId) {
            $connectionInfo = Redis::get("sse:connections:{$connectionId}");
            if ($connectionInfo) {
                $info = json_decode($connectionInfo, true);
                $connectedAt = \Carbon\Carbon::parse($info['connected_at']);
                $duration = now()->diffInSeconds($connectedAt);
                $totalDuration += $duration;
                $count++;
            }
        }

        return $count > 0 ? $totalDuration / $count : 0;
    }

    /**
     * SSE 헤더 전송
     */
    private function sendSseHeaders(): void
    {
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');

        if (ob_get_level()) {
            ob_end_flush();
        }
    }

    /**
     * SSE 데이터 전송
     */
    private function sendSseData(string $event, $data): void
    {
        echo "event: {$event}\n";
        echo "data: " . json_encode($data) . "\n\n";

        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }

    /**
     * 데이터베이스 상태 확인
     */
    private function checkDatabaseHealth(): array
    {
        try {
            \DB::connection()->getPdo();
            return ['status' => 'healthy', 'message' => '데이터베이스 연결 정상'];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'message' => '데이터베이스 연결 실패: ' . $e->getMessage()];
        }
    }

    /**
     * Redis 상태 확인
     */
    private function checkRedisHealth(): array
    {
        try {
            Redis::ping();
            return ['status' => 'healthy', 'message' => 'Redis 연결 정상'];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'message' => 'Redis 연결 실패: ' . $e->getMessage()];
        }
    }

    /**
     * SSE 서비스 상태 확인
     */
    private function checkSseServiceHealth(): array
    {
        try {
            $stats = $this->connectionManager->getConnectionStats();
            $connectionCount = $stats['total_connections'];
            return [
                'status' => 'healthy',
                'message' => "SSE 서비스 정상 ({$connectionCount}개 연결)"
            ];
        } catch (\Exception $e) {
            return ['status' => 'unhealthy', 'message' => 'SSE 서비스 오류: ' . $e->getMessage()];
        }
    }

    /**
     * 메모리 상태 확인
     */
    private function checkMemoryHealth(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->getMemoryLimit();
        $usagePercent = ($memoryUsage / $memoryLimit) * 100;

        if ($usagePercent > 90) {
            return ['status' => 'critical', 'message' => "메모리 사용량 위험: {$usagePercent}%"];
        } elseif ($usagePercent > 80) {
            return ['status' => 'warning', 'message' => "메모리 사용량 주의: {$usagePercent}%"];
        } else {
            return ['status' => 'healthy', 'message' => "메모리 사용량 정상: {$usagePercent}%"];
        }
    }

    /**
     * 디스크 상태 확인
     */
    private function checkDiskHealth(): array
    {
        $diskFree = disk_free_space('/');
        $diskTotal = disk_total_space('/');
        $usagePercent = (($diskTotal - $diskFree) / $diskTotal) * 100;

        if ($usagePercent > 90) {
            return ['status' => 'critical', 'message' => "디스크 사용량 위험: {$usagePercent}%"];
        } elseif ($usagePercent > 80) {
            return ['status' => 'warning', 'message' => "디스크 사용량 주의: {$usagePercent}%"];
        } else {
            return ['status' => 'healthy', 'message' => "디스크 사용량 정상: {$usagePercent}%"];
        }
    }

    /**
     * 메모리 제한 조회
     */
    private function getMemoryLimit(): int
    {
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }

        return $this->convertToBytes($memoryLimit);
    }

    /**
     * 문자열을 바이트로 변환
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Redis 히트율 계산
     */
    private function calculateRedisHitRate(array $info): float
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;

        return $total > 0 ? ($hits / $total) * 100 : 0;
    }

    /**
     * Redis 키 개수 조회
     */
    private function getRedisKeysCount(): int
    {
        try {
            $keys = Redis::keys('sse:*');
            return count($keys);
        } catch (\Exception $e) {
            return 0;
        }
    }
}
