<?php

namespace App\Console;

use App\Jobs\UpdateAllCount;
use App\Jobs\UpdateDuplicateProducts;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // 0: 일요일, 1: 월요일, 2: 화요일, 3: 수요일, 4: 목요일, 5: 금요일, 6: 토요일
        // $schedule->job(new UpdateDuplicateProducts)
        //     ->days([3, 4, 5, 6])
        //     ->at('04:00');

        $schedule->job(new UpdateAllCount)
            ->days([0, 2, 3, 4, 5, 6])
            ->at('05:00');

        // SSE 오프라인 알림 정리 (매일 새벽 2시)
        $schedule->command('sse:cleanup-notifications')
            ->daily()
            ->at('02:00')
            ->withoutOverlapping()
            ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
