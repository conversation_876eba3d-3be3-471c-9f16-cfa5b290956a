<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\MonitorSizeLookup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * 전체 상품을 스캔하여 monitor_size_lookups 테이블과 매칭하고
 * monitor_size_lookup_id를 업데이트하는 명령어
 * 
 * ## 사용법
 * 
 * ### 기본 실행 (전체 모니터 상품 처리)
 * ```bash
 * docker compose exec laravel php artisan monitor:update-lookup-ids
 * ```
 * 
 * ### 특정 개수만 처리
 * ```bash
 * docker compose exec laravel php artisan monitor:update-lookup-ids --limit=5000
 * ```
 * 
 * ### 드라이 런 (실제 업데이트하지 않고 확인만)
 * ```bash
 * docker compose exec laravel php artisan monitor:update-lookup-ids --dry-run
 * ```
 * 
 * ### 상세 로그 출력
 * ```bash
 * docker compose exec laravel php artisan monitor:update-lookup-ids --verbose
 * ```
 * 
 * ## 기능
 * 
 * 1. **모니터 상품 필터링**: cate4_id = 2 또는 cate5_id = 423인 상품만 처리
 * 2. **해시 기반 매칭**: 상품명의 MD5 해시로 빠른 매칭
 * 3. **청크 단위 처리**: 메모리 효율적인 대용량 데이터 처리
 * 4. **진행률 표시**: 실시간 처리 진행 상황 표시
 * 5. **결과 요약**: 매칭 성공/실패 통계 제공
 * 6. **트랜잭션 안전성**: 데이터 일관성 보장
 * 7. **오류 처리**: 개별 상품 오류 시에도 전체 프로세스 계속 진행
 * 
 * ## 처리 과정
 * 
 * 1. monitor_size_lookups 테이블의 모든 레코드를 메모리에 로드
 * 2. 모니터 상품들을 청크 단위로 처리
 * 3. 각 상품명의 MD5 해시를 생성하여 매칭
 * 4. 매칭되는 경우 monitor_size_lookup_id 업데이트
 * 5. 결과 통계 출력
 * 
 * ## 출력 예시
 * 
 * ```
 * 모니터 상품 monitor_size_lookup_id 업데이트를 시작합니다...
 * 총 모니터 상품 수: 81683
 * monitor_size_lookups 테이블 레코드 수: 6956
 * 
 * 81683/81683 [▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓] 100%
 * 
 * === 업데이트 결과 요약 ===
 * 총 모니터 상품: 81683
 * 처리된 상품: 81683
 * 매칭 성공: 4850
 * 매칭 실패: 76833
 * 성공률: 5.94%
 * 
 * === 브랜드별 매칭 결과 ===
 * 브랜드 제품 매칭: 3200개
 * 기타 제품 매칭: 1650개
 * 
 * === 단위별 매칭 결과 ===
 * INCH 단위 매칭: 4200개
 * CM 단위 매칭: 650개
 * 
 * 업데이트가 완료되었습니다!
 * ```
 */
class UpdateMonitorSizeLookupIds extends Command
{
    /**
     * 명령어 시그니처
     */
    protected $signature = 'monitor:update-lookup-ids 
                            {--limit= : 처리할 상품 수 제한}
                            {--dry-run : 실제 업데이트하지 않고 확인만}
                            {--show-details : 상세 로그 출력}';

    /**
     * 명령어 설명
     */
    protected $description = '전체 모니터 상품을 스캔하여 monitor_size_lookups 테이블과 매칭하고 monitor_size_lookup_id를 업데이트합니다';

    /**
     * monitor_size_lookups 테이블의 모든 레코드 (메모리 캐시)
     */
    private array $lookupCache = [];

    /**
     * 통계 데이터
     */
    private array $stats = [
        'total_products' => 0,
        'processed_products' => 0,
        'matched_products' => 0,
        'unmatched_products' => 0,
        'brand_matches' => 0,
        'etc_matches' => 0,
        'inch_matches' => 0,
        'cm_matches' => 0,
        'errors' => 0
    ];

    /**
     * 명령어 실행
     */
    public function handle(): int
    {
        $limit = $this->option('limit');
        $dryRun = $this->option('dry-run');
        $verbose = $this->option('show-details');

        $this->info("모니터 상품 monitor_size_lookup_id 업데이트를 시작합니다...");

        if ($dryRun) {
            $this->warn("드라이 런 모드: 실제 업데이트하지 않습니다.");
        }

        try {
            // 1. monitor_size_lookups 테이블 로드
            $this->loadLookupCache();

            // 2. 모니터 상품 수 조회
            $totalCount = $this->getMonitorProductCount($limit);

            if ($totalCount === 0) {
                $this->error('처리할 모니터 상품이 없습니다.');
                return 1;
            }

            $this->info("총 모니터 상품 수: " . number_format($totalCount));
            $this->info("monitor_size_lookups 테이블 레코드 수: " . number_format(count($this->lookupCache)));

            // 3. 상품 처리
            $this->processProducts($limit, $dryRun, $verbose);

            // 4. 결과 출력
            $this->displayResults();

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('업데이트 중 오류가 발생했습니다: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * monitor_size_lookups 테이블을 메모리에 로드
     */
    private function loadLookupCache(): void
    {
        $this->info("monitor_size_lookups 테이블을 메모리에 로드 중...");

        $lookups = MonitorSizeLookup::select('id', 'name', 'name_hash', 'brand', 'size', 'unit')->get();

        foreach ($lookups as $lookup) {
            $this->lookupCache[$lookup->name_hash] = [
                'id' => $lookup->id,
                'name' => $lookup->name,
                'brand' => $lookup->brand,
                'size' => $lookup->size,
                'unit' => $lookup->unit
            ];
        }

        $this->info("  ✓ " . number_format(count($this->lookupCache)) . "개 레코드 로드 완료");
    }

    /**
     * 모니터 상품 수 조회
     */
    private function getMonitorProductCount(?int $limit): int
    {
        $query = Product::query()
            ->where(function ($query) {
                $query->where('cate4_id', 2)
                      ->orWhere('cate5_id', 423);
            });

        if ($limit) {
            return min($query->count(), $limit);
        }

        return $query->count();
    }

    /**
     * 상품들을 청크 단위로 처리
     */
    private function processProducts(?int $limit, bool $dryRun, bool $verbose): void
    {
        $this->info("상품 처리를 시작합니다...");

        $chunkSize = 1000;
        $processedCount = 0;

        $progressBar = $this->output->createProgressBar($limit ?: $this->getMonitorProductCount(null));
        $progressBar->start();

        // 트랜잭션 시작
        if (!$dryRun) {
            DB::beginTransaction();
        }

        try {
            Product::query()
                ->where(function ($query) {
                    $query->where('cate4_id', 2)
                          ->orWhere('cate5_id', 423);
                })
                ->select('id', 'qaid', 'name', 'monitor_size_lookup_id')
                ->chunk($chunkSize, function ($products) use (&$processedCount, $limit, $dryRun, $verbose, $progressBar) {
                    foreach ($products as $product) {
                        // 제한된 수만큼만 처리
                        if ($limit && $processedCount >= $limit) {
                            break;
                        }

                        $processedCount++;
                        $this->stats['processed_products']++;

                        try {
                            $this->processProduct($product, $dryRun, $verbose);
                        } catch (\Exception $e) {
                            $this->stats['errors']++;
                            if ($verbose) {
                                $this->error("상품 ID {$product->id} 처리 중 오류: " . $e->getMessage());
                            }
                        }

                        $progressBar->advance();
                    }
                });

            // 트랜잭션 커밋
            if (!$dryRun) {
                DB::commit();
                $this->info("  ✓ 트랜잭션 커밋 완료");
            }

        } catch (\Exception $e) {
            // 트랜잭션 롤백
            if (!$dryRun) {
                DB::rollBack();
                $this->error("  ✗ 트랜잭션 롤백: " . $e->getMessage());
            }
            throw $e;
        }

        $progressBar->finish();
        $this->newLine(2);
    }

    /**
     * 개별 상품 처리
     */
    private function processProduct(Product $product, bool $dryRun, bool $verbose): void
    {
        $productNameHash = md5(trim($product->name));

        // monitor_size_lookups 테이블에서 매칭 검색
        if (isset($this->lookupCache[$productNameHash])) {
            $lookup = $this->lookupCache[$productNameHash];

            // 이미 올바른 ID가 설정되어 있는지 확인
            if ($product->monitor_size_lookup_id == $lookup['id']) {
                return; // 이미 올바르게 설정됨
            }

            // 통계 업데이트
            $this->stats['matched_products']++;
            
            if ($lookup['brand'] === 'brand') {
                $this->stats['brand_matches']++;
            } else {
                $this->stats['etc_matches']++;
            }

            if ($lookup['unit'] === 'INCH') {
                $this->stats['inch_matches']++;
            } elseif ($lookup['unit'] === 'CM') {
                $this->stats['cm_matches']++;
            }

            // 실제 업데이트 수행
            if (!$dryRun) {
                $product->update(['monitor_size_lookup_id' => $lookup['id']]);
            }

            if ($verbose) {
                $this->line("  ✓ 매칭 성공: {$product->name} → {$lookup['size']} {$lookup['unit']}");
            }

        } else {
            $this->stats['unmatched_products']++;
            
            if ($verbose) {
                $this->line("  ✗ 매칭 실패: {$product->name}");
            }
        }
    }

    /**
     * 결과 출력
     */
    private function displayResults(): void
    {
        $this->info("\n=== 업데이트 결과 요약 ===");
        $this->info("총 모니터 상품: " . number_format($this->stats['total_products']));
        $this->info("처리된 상품: " . number_format($this->stats['processed_products']));
        $this->info("매칭 성공: " . number_format($this->stats['matched_products']));
        $this->info("매칭 실패: " . number_format($this->stats['unmatched_products']));
        
        if ($this->stats['processed_products'] > 0) {
            $successRate = round(($this->stats['matched_products'] / $this->stats['processed_products']) * 100, 2);
            $this->info("성공률: {$successRate}%");
        }

        if ($this->stats['errors'] > 0) {
            $this->warn("처리 오류: " . number_format($this->stats['errors']) . "개");
        }

        $this->info("\n=== 브랜드별 매칭 결과 ===");
        $this->info("브랜드 제품 매칭: " . number_format($this->stats['brand_matches']) . "개");
        $this->info("기타 제품 매칭: " . number_format($this->stats['etc_matches']) . "개");

        $this->info("\n=== 단위별 매칭 결과 ===");
        $this->info("INCH 단위 매칭: " . number_format($this->stats['inch_matches']) . "개");
        $this->info("CM 단위 매칭: " . number_format($this->stats['cm_matches']) . "개");

        $this->info("\n업데이트가 완료되었습니다!");
    }
} 