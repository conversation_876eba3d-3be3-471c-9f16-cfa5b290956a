<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\ConnectionManager;
use App\Services\NotificationManager;
use App\Services\PerformanceOptimizer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * SSE 성능 벤치마크 명령어
 * 
 * SSE 시스템의 성능을 측정하고 벤치마크 결과를 제공합니다.
 */
class SseBenchmarkCommand extends Command
{
    protected $signature = 'sse:benchmark 
                           {--connections=100 : 동시 연결 수}
                           {--messages=1000 : 전송할 메시지 수}
                           {--duration=60 : 테스트 지속 시간(초)}
                           {--batch-size=10 : 배치 크기}
                           {--output=console : 출력 형식 (console|json|csv)}
                           {--save-results : 결과를 파일로 저장}';

    protected $description = 'SSE 시스템 성능 벤치마크 실행';

    private ConnectionManager $connectionManager;
    private NotificationManager $notificationManager;
    private PerformanceOptimizer $performanceOptimizer;

    public function __construct(
        ConnectionManager $connectionManager,
        NotificationManager $notificationManager,
        PerformanceOptimizer $performanceOptimizer
    ) {
        parent::__construct();
        $this->connectionManager = $connectionManager;
        $this->notificationManager = $notificationManager;
        $this->performanceOptimizer = $performanceOptimizer;
    }

    public function handle(): int
    {
        $this->info('🚀 SSE 성능 벤치마크 시작');
        $this->newLine();

        // 옵션 파싱
        $connections = (int) $this->option('connections');
        $messages = (int) $this->option('messages');
        $duration = (int) $this->option('duration');
        $batchSize = (int) $this->option('batch-size');
        $outputFormat = $this->option('output');
        $saveResults = $this->option('save-results');

        // 벤치마크 설정 출력
        $this->displayBenchmarkConfig($connections, $messages, $duration, $batchSize);

        try {
            // Redis 정리
            $this->info('🧹 테스트 환경 정리 중...');
            Redis::flushdb();

            // 벤치마크 실행
            $results = $this->runBenchmark($connections, $messages, $duration, $batchSize);

            // 결과 출력
            $this->displayResults($results, $outputFormat);

            // 결과 저장
            if ($saveResults) {
                $this->saveResults($results);
            }

            $this->newLine();
            $this->info('✅ 벤치마크 완료');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ 벤치마크 실행 실패: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 벤치마크 실행
     */
    private function runBenchmark(int $connections, int $messages, int $duration, int $batchSize): array
    {
        $results = [
            'config' => [
                'connections' => $connections,
                'messages' => $messages,
                'duration' => $duration,
                'batch_size' => $batchSize,
                'start_time' => now()->toISOString()
            ],
            'connection_test' => $this->runConnectionTest($connections, $batchSize),
            'message_throughput_test' => $this->runMessageThroughputTest($messages, $batchSize),
            'load_test' => $this->runLoadTest($connections, $duration),
            'memory_test' => $this->runMemoryTest($connections, $messages),
            'latency_test' => $this->runLatencyTest(),
            'system_metrics' => $this->collectSystemMetrics()
        ];

        $results['config']['end_time'] = now()->toISOString();
        $results['config']['total_duration'] = now()->diffInSeconds($results['config']['start_time']);

        return $results;
    }

    /**
     * 연결 테스트 실행
     */
    private function runConnectionTest(int $targetConnections, int $batchSize): array
    {
        $this->info("🔗 연결 테스트 실행 중... (목표: {$targetConnections}개)");
        
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $connections = [];
        $users = User::factory()->count($targetConnections)->create();

        $progressBar = $this->output->createProgressBar($targetConnections);
        $progressBar->start();

        // 배치 단위로 연결 생성
        for ($i = 0; $i < $targetConnections; $i += $batchSize) {
            $batchEnd = min($i + $batchSize, $targetConnections);
            
            for ($j = $i; $j < $batchEnd; $j++) {
                try {
                    $user = $users[$j];
                    $connectionId = "benchmark-{$user->id}-" . uniqid();
                    $this->connectionManager->addConnection($connectionId, $user->id);
                    $connections[] = $connectionId;
                } catch (\Exception $e) {
                    // 연결 실패는 기록하지만 계속 진행
                }
                $progressBar->advance();
            }
            
            // 배치 간 짧은 지연
            usleep(10000); // 10ms
        }

        $progressBar->finish();
        $this->newLine();

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $actualConnections = count($connections);

        return [
            'target_connections' => $targetConnections,
            'actual_connections' => $actualConnections,
            'success_rate' => ($actualConnections / $targetConnections) * 100,
            'duration' => $endTime - $startTime,
            'connections_per_second' => $actualConnections / ($endTime - $startTime),
            'memory_used' => $endMemory - $startMemory,
            'connections' => $connections
        ];
    }

    /**
     * 메시지 처리량 테스트 실행
     */
    private function runMessageThroughputTest(int $messageCount, int $batchSize): array
    {
        $this->info("📨 메시지 처리량 테스트 실행 중... (목표: {$messageCount}개)");
        
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $successCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($messageCount);
        $progressBar->start();

        // 배치 단위로 메시지 전송
        for ($i = 0; $i < $messageCount; $i += $batchSize) {
            $batchEnd = min($i + $batchSize, $messageCount);
            $batchMessages = [];
            
            // 배치 메시지 준비
            for ($j = $i; $j < $batchEnd; $j++) {
                $batchMessages[] = [
                    'title' => "벤치마크 메시지 {$j}",
                    'message' => "처리량 테스트 메시지 번호 {$j}",
                    'sequence' => $j,
                    'timestamp' => now()->toISOString()
                ];
            }

            // 배치 전송
            try {
                foreach ($batchMessages as $message) {
                    $this->notificationManager->sendToAll($message);
                    $successCount++;
                    $progressBar->advance();
                }
            } catch (\Exception $e) {
                $errorCount += count($batchMessages);
                $progressBar->advance(count($batchMessages));
            }

            // 배치 간 짧은 지연
            usleep(1000); // 1ms
        }

        $progressBar->finish();
        $this->newLine();

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $totalTime = $endTime - $startTime;

        return [
            'target_messages' => $messageCount,
            'successful_messages' => $successCount,
            'failed_messages' => $errorCount,
            'success_rate' => ($successCount / $messageCount) * 100,
            'duration' => $totalTime,
            'messages_per_second' => $successCount / $totalTime,
            'memory_used' => $endMemory - $startMemory,
            'average_message_time' => $totalTime / $successCount
        ];
    }

    /**
     * 부하 테스트 실행
     */
    private function runLoadTest(int $connections, int $duration): array
    {
        $this->info("⚡ 부하 테스트 실행 중... ({$duration}초간)");
        
        $startTime = time();
        $endTime = $startTime + $duration;
        $metrics = [];
        $messageCount = 0;

        $progressBar = $this->output->createProgressBar($duration);
        $progressBar->start();

        while (time() < $endTime) {
            $iterationStart = microtime(true);
            
            // 메시지 전송
            try {
                $this->notificationManager->sendToAll([
                    'title' => '부하 테스트',
                    'message' => '지속적인 부하 테스트 메시지',
                    'timestamp' => now()->toISOString()
                ]);
                $messageCount++;
            } catch (\Exception $e) {
                // 오류 기록
            }

            // 메트릭 수집
            $metrics[] = [
                'timestamp' => time() - $startTime,
                'memory_usage' => memory_get_usage(true),
                'active_connections' => $this->connectionManager->getActiveConnectionCount(),
                'iteration_time' => microtime(true) - $iterationStart
            ];

            $progressBar->advance();
            sleep(1); // 1초 간격
        }

        $progressBar->finish();
        $this->newLine();

        return [
            'duration' => $duration,
            'messages_sent' => $messageCount,
            'messages_per_second' => $messageCount / $duration,
            'metrics' => $metrics,
            'memory_trend' => $this->analyzeMemoryTrend($metrics),
            'connection_stability' => $this->analyzeConnectionStability($metrics)
        ];
    }

    /**
     * 메모리 테스트 실행
     */
    private function runMemoryTest(int $connections, int $messages): array
    {
        $this->info("🧠 메모리 사용량 테스트 실행 중...");
        
        $initialMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        // 대용량 메시지 테스트
        $largeMessage = [
            'title' => '메모리 테스트',
            'message' => '대용량 메시지 테스트',
            'payload' => str_repeat('A', 1024 * 100) // 100KB
        ];

        $startTime = microtime(true);
        
        for ($i = 0; $i < 10; $i++) {
            $this->notificationManager->sendToAll($largeMessage);
        }
        
        $endTime = microtime(true);
        $finalMemory = memory_get_usage(true);
        $finalPeakMemory = memory_get_peak_usage(true);

        // 가비지 컬렉션 실행
        if (function_exists('gc_collect_cycles')) {
            $gcCycles = gc_collect_cycles();
        }

        $afterGcMemory = memory_get_usage(true);

        return [
            'initial_memory' => $initialMemory,
            'peak_memory' => $peakMemory,
            'final_memory' => $finalMemory,
            'final_peak_memory' => $finalPeakMemory,
            'after_gc_memory' => $afterGcMemory,
            'memory_increase' => $finalMemory - $initialMemory,
            'memory_recovered' => $finalMemory - $afterGcMemory,
            'gc_cycles' => $gcCycles ?? 0,
            'large_message_duration' => $endTime - $startTime
        ];
    }

    /**
     * 지연 시간 테스트 실행
     */
    private function runLatencyTest(): array
    {
        $this->info("⏱️ 지연 시간 테스트 실행 중...");
        
        $latencies = [];
        $testCount = 100;

        $progressBar = $this->output->createProgressBar($testCount);
        $progressBar->start();

        for ($i = 0; $i < $testCount; $i++) {
            $startTime = microtime(true);
            
            $this->notificationManager->sendToAll([
                'title' => '지연 시간 테스트',
                'message' => "지연 시간 측정 메시지 {$i}",
                'send_time' => $startTime
            ]);
            
            $endTime = microtime(true);
            $latencies[] = ($endTime - $startTime) * 1000; // 밀리초로 변환
            
            $progressBar->advance();
            usleep(10000); // 10ms 지연
        }

        $progressBar->finish();
        $this->newLine();

        sort($latencies);
        $count = count($latencies);

        return [
            'sample_count' => $count,
            'min_latency' => min($latencies),
            'max_latency' => max($latencies),
            'average_latency' => array_sum($latencies) / $count,
            'median_latency' => $latencies[intval($count / 2)],
            'p95_latency' => $latencies[intval($count * 0.95)],
            'p99_latency' => $latencies[intval($count * 0.99)],
            'latencies' => $latencies
        ];
    }

    /**
     * 시스템 메트릭 수집
     */
    private function collectSystemMetrics(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'load_average' => sys_getloadavg(),
            'disk_free_space' => disk_free_space('/'),
            'disk_total_space' => disk_total_space('/'),
            'redis_info' => $this->getRedisInfo(),
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * 벤치마크 설정 출력
     */
    private function displayBenchmarkConfig(int $connections, int $messages, int $duration, int $batchSize): void
    {
        $this->table(
            ['설정', '값'],
            [
                ['동시 연결 수', number_format($connections)],
                ['메시지 수', number_format($messages)],
                ['테스트 지속 시간', $duration . '초'],
                ['배치 크기', $batchSize],
                ['시작 시간', now()->format('Y-m-d H:i:s')]
            ]
        );
        $this->newLine();
    }

    /**
     * 결과 출력
     */
    private function displayResults(array $results, string $format): void
    {
        switch ($format) {
            case 'json':
                $this->line(json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                break;
            
            case 'csv':
                $this->outputCsv($results);
                break;
            
            default:
                $this->displayConsoleResults($results);
                break;
        }
    }

    /**
     * 콘솔 결과 출력
     */
    private function displayConsoleResults(array $results): void
    {
        $this->newLine();
        $this->info('📊 벤치마크 결과');
        $this->line(str_repeat('=', 60));

        // 연결 테스트 결과
        $conn = $results['connection_test'];
        $this->info('🔗 연결 테스트');
        $this->table(
            ['메트릭', '값'],
            [
                ['목표 연결 수', number_format($conn['target_connections'])],
                ['실제 연결 수', number_format($conn['actual_connections'])],
                ['성공률', round($conn['success_rate'], 2) . '%'],
                ['연결 시간', round($conn['duration'], 3) . '초'],
                ['초당 연결 수', round($conn['connections_per_second'], 1)],
                ['메모리 사용량', $this->formatBytes($conn['memory_used'])]
            ]
        );

        // 메시지 처리량 테스트 결과
        $msg = $results['message_throughput_test'];
        $this->info('📨 메시지 처리량 테스트');
        $this->table(
            ['메트릭', '값'],
            [
                ['목표 메시지 수', number_format($msg['target_messages'])],
                ['성공 메시지 수', number_format($msg['successful_messages'])],
                ['실패 메시지 수', number_format($msg['failed_messages'])],
                ['성공률', round($msg['success_rate'], 2) . '%'],
                ['처리 시간', round($msg['duration'], 3) . '초'],
                ['초당 메시지 수', round($msg['messages_per_second'], 1)],
                ['평균 메시지 시간', round($msg['average_message_time'] * 1000, 3) . 'ms']
            ]
        );

        // 지연 시간 테스트 결과
        $lat = $results['latency_test'];
        $this->info('⏱️ 지연 시간 테스트');
        $this->table(
            ['메트릭', '값'],
            [
                ['샘플 수', number_format($lat['sample_count'])],
                ['최소 지연 시간', round($lat['min_latency'], 3) . 'ms'],
                ['최대 지연 시간', round($lat['max_latency'], 3) . 'ms'],
                ['평균 지연 시간', round($lat['average_latency'], 3) . 'ms'],
                ['중간값 지연 시간', round($lat['median_latency'], 3) . 'ms'],
                ['95% 지연 시간', round($lat['p95_latency'], 3) . 'ms'],
                ['99% 지연 시간', round($lat['p99_latency'], 3) . 'ms']
            ]
        );

        // 메모리 테스트 결과
        $mem = $results['memory_test'];
        $this->info('🧠 메모리 테스트');
        $this->table(
            ['메트릭', '값'],
            [
                ['초기 메모리', $this->formatBytes($mem['initial_memory'])],
                ['최종 메모리', $this->formatBytes($mem['final_memory'])],
                ['최대 메모리', $this->formatBytes($mem['final_peak_memory'])],
                ['GC 후 메모리', $this->formatBytes($mem['after_gc_memory'])],
                ['메모리 증가', $this->formatBytes($mem['memory_increase'])],
                ['메모리 회수', $this->formatBytes($mem['memory_recovered'])],
                ['GC 사이클', number_format($mem['gc_cycles'])]
            ]
        );

        // 성능 등급 평가
        $this->displayPerformanceGrade($results);
    }

    /**
     * 성능 등급 평가 출력
     */
    private function displayPerformanceGrade(array $results): void
    {
        $this->newLine();
        $this->info('🏆 성능 등급 평가');
        
        $grades = [];
        
        // 연결 성능 등급
        $connSuccessRate = $results['connection_test']['success_rate'];
        $connPerSec = $results['connection_test']['connections_per_second'];
        $connGrade = $this->calculateGrade([
            ['value' => $connSuccessRate, 'weight' => 0.6, 'thresholds' => [99, 95, 90, 80]],
            ['value' => $connPerSec, 'weight' => 0.4, 'thresholds' => [100, 50, 25, 10]]
        ]);
        $grades[] = ['연결 성능', $connGrade];

        // 메시지 처리량 등급
        $msgSuccessRate = $results['message_throughput_test']['success_rate'];
        $msgPerSec = $results['message_throughput_test']['messages_per_second'];
        $msgGrade = $this->calculateGrade([
            ['value' => $msgSuccessRate, 'weight' => 0.6, 'thresholds' => [99, 95, 90, 80]],
            ['value' => $msgPerSec, 'weight' => 0.4, 'thresholds' => [200, 100, 50, 25]]
        ]);
        $grades[] = ['메시지 처리량', $msgGrade];

        // 지연 시간 등급 (낮을수록 좋음)
        $avgLatency = $results['latency_test']['average_latency'];
        $p95Latency = $results['latency_test']['p95_latency'];
        $latGrade = $this->calculateGrade([
            ['value' => 100 - min($avgLatency, 100), 'weight' => 0.5, 'thresholds' => [95, 90, 80, 70]],
            ['value' => 200 - min($p95Latency, 200), 'weight' => 0.5, 'thresholds' => [180, 150, 120, 100]]
        ]);
        $grades[] = ['지연 시간', $latGrade];

        $this->table(['카테고리', '등급'], $grades);
    }

    /**
     * 등급 계산
     */
    private function calculateGrade(array $metrics): string
    {
        $totalScore = 0;
        $totalWeight = 0;

        foreach ($metrics as $metric) {
            $value = $metric['value'];
            $weight = $metric['weight'];
            $thresholds = $metric['thresholds'];

            $score = 0;
            if ($value >= $thresholds[0]) $score = 4; // A
            elseif ($value >= $thresholds[1]) $score = 3; // B
            elseif ($value >= $thresholds[2]) $score = 2; // C
            elseif ($value >= $thresholds[3]) $score = 1; // D
            else $score = 0; // F

            $totalScore += $score * $weight;
            $totalWeight += $weight;
        }

        $avgScore = $totalWeight > 0 ? $totalScore / $totalWeight : 0;

        if ($avgScore >= 3.5) return 'A';
        elseif ($avgScore >= 2.5) return 'B';
        elseif ($avgScore >= 1.5) return 'C';
        elseif ($avgScore >= 0.5) return 'D';
        else return 'F';
    }

    /**
     * 결과 저장
     */
    private function saveResults(array $results): void
    {
        $filename = 'sse_benchmark_' . now()->format('Y-m-d_H-i-s') . '.json';
        $filepath = storage_path('app/benchmarks/' . $filename);
        
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        file_put_contents($filepath, json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $this->info("📁 결과가 저장되었습니다: {$filepath}");
    }

    /**
     * 메모리 트렌드 분석
     */
    private function analyzeMemoryTrend(array $metrics): float
    {
        if (count($metrics) < 2) return 0;

        $first = reset($metrics);
        $last = end($metrics);
        
        $memoryDiff = $last['memory_usage'] - $first['memory_usage'];
        $timeDiff = $last['timestamp'] - $first['timestamp'];
        
        return $timeDiff > 0 ? ($memoryDiff / $timeDiff) * 60 : 0; // 분당 증가량
    }

    /**
     * 연결 안정성 분석
     */
    private function analyzeConnectionStability(array $metrics): float
    {
        if (empty($metrics)) return 0;

        $expectedConnections = $metrics[0]['active_connections'];
        $stableCount = 0;
        
        foreach ($metrics as $metric) {
            if ($metric['active_connections'] === $expectedConnections) {
                $stableCount++;
            }
        }
        
        return (count($metrics) > 0) ? ($stableCount / count($metrics)) * 100 : 0;
    }

    /**
     * Redis 정보 수집
     */
    private function getRedisInfo(): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();
            
            return [
                'version' => $info['redis_version'] ?? 'unknown',
                'connected_clients' => $info['connected_clients'] ?? 0,
                'used_memory_human' => $info['used_memory_human'] ?? '0B',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 바이트를 읽기 쉬운 형태로 변환
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}