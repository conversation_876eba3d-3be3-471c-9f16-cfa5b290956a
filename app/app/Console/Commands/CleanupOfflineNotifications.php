<?php

namespace App\Console\Commands;

use App\Services\NotificationManager;
use App\Models\SseOfflineNotification;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Exception;

/**
 * 오래된 오프라인 알림을 정리하는 Artisan 명령어
 * 
 * 전달 완료된 오래된 알림과 미전달 상태의 너무 오래된 알림을
 * 자동으로 삭제하여 데이터베이스 용량을 관리합니다.
 */
class CleanupOfflineNotifications extends Command
{
    /**
     * 명령어 시그니처
     *
     * @var string
     */
    protected $signature = 'sse:cleanup-notifications 
                            {--delivered-days=7 : 전달 완료된 알림을 삭제할 일수}
                            {--undelivered-days=30 : 미전달 알림을 삭제할 일수}
                            {--dry-run : 실제 삭제 없이 삭제 대상만 확인}';

    /**
     * 명령어 설명
     *
     * @var string
     */
    protected $description = '오래된 SSE 오프라인 알림을 정리합니다';

    private NotificationManager $notificationManager;

    /**
     * 명령어 생성자
     */
    public function __construct(NotificationManager $notificationManager)
    {
        parent::__construct();
        $this->notificationManager = $notificationManager;
    }

    /**
     * 명령어 실행
     */
    public function handle(): int
    {
        try {
            $deliveredDays = (int) $this->option('delivered-days');
            $undeliveredDays = (int) $this->option('undelivered-days');
            $dryRun = $this->option('dry-run');

            $this->info('SSE 오프라인 알림 정리 시작');
            $this->info("전달 완료 알림 삭제 기준: {$deliveredDays}일 이전");
            $this->info("미전달 알림 삭제 기준: {$undeliveredDays}일 이전");
            
            if ($dryRun) {
                $this->warn('DRY RUN 모드: 실제 삭제는 수행되지 않습니다');
            }

            // 전달 완료된 오래된 알림 정리
            $deliveredCount = $this->cleanupDeliveredNotifications($deliveredDays, $dryRun);
            
            // 미전달 상태의 너무 오래된 알림 정리
            $undeliveredCount = $this->cleanupUndeliveredNotifications($undeliveredDays, $dryRun);

            $this->info('정리 완료');
            $this->table(
                ['구분', '삭제된 알림 수'],
                [
                    ['전달 완료 알림', $deliveredCount],
                    ['미전달 알림', $undeliveredCount],
                    ['총계', $deliveredCount + $undeliveredCount]
                ]
            );

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error('알림 정리 중 오류 발생: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 전달 완료된 오래된 알림을 정리합니다
     */
    private function cleanupDeliveredNotifications(int $days, bool $dryRun): int
    {
        try {
            $cutoffDate = Carbon::now()->subDays($days);
            
            $query = SseOfflineNotification::whereNotNull('delivered_at')
                ->where('delivered_at', '<', $cutoffDate);

            $count = $query->count();
            
            if ($count > 0) {
                $this->info("전달 완료된 오래된 알림 {$count}개 발견 (기준일: {$cutoffDate->format('Y-m-d H:i:s')})");
                
                if (!$dryRun) {
                    $deletedCount = $query->delete();
                    $this->info("전달 완료 알림 {$deletedCount}개 삭제 완료");
                    return $deletedCount;
                } else {
                    $this->info('DRY RUN: 실제 삭제는 수행되지 않음');
                }
            } else {
                $this->info('삭제할 전달 완료 알림이 없습니다');
            }

            return $dryRun ? 0 : $count;

        } catch (Exception $e) {
            $this->error('전달 완료 알림 정리 중 오류: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 미전달 상태의 너무 오래된 알림을 정리합니다
     */
    private function cleanupUndeliveredNotifications(int $days, bool $dryRun): int
    {
        try {
            $cutoffDate = Carbon::now()->subDays($days);
            
            $query = SseOfflineNotification::whereNull('delivered_at')
                ->where('created_at', '<', $cutoffDate);

            $count = $query->count();
            
            if ($count > 0) {
                $this->info("미전달 상태의 오래된 알림 {$count}개 발견 (기준일: {$cutoffDate->format('Y-m-d H:i:s')})");
                
                if (!$dryRun) {
                    $deletedCount = $query->delete();
                    $this->info("미전달 알림 {$deletedCount}개 삭제 완료");
                    return $deletedCount;
                } else {
                    $this->info('DRY RUN: 실제 삭제는 수행되지 않음');
                }
            } else {
                $this->info('삭제할 미전달 알림이 없습니다');
            }

            return $dryRun ? 0 : $count;

        } catch (Exception $e) {
            $this->error('미전달 알림 정리 중 오류: ' . $e->getMessage());
            return 0;
        }
    }
}
