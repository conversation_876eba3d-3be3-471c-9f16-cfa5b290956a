<?php

namespace App\Console\Commands;

use App\Helpers\CacheHelper;
use App\Services\CategoryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CheckCategoryCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:check-categories {--clear : 캐시를 무효화하고 새로 생성}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '카테고리 캐시 상태를 확인합니다.';

    protected CategoryService $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        parent::__construct();
        $this->categoryService = $categoryService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $cacheKey = 'categories.all';
            $tag = ['categories'];

            $this->info('🔍 카테고리 캐시 상태 확인 중...');
            $this->newLine();

            // 캐시 존재 여부 확인 (여러 방법으로 확인)
            $hasCacheDirect = Cache::has($cacheKey);
            $hasCacheWithTag = CacheHelper::has($cacheKey, $tag);
            $cachedData = CacheHelper::getCache($cacheKey, $tag);

            $this->info("📊 캐시 확인 결과:");
            $this->info("   - 직접 확인 (Cache::has): " . ($hasCacheDirect ? '✅ 존재' : '❌ 없음'));
            $this->info("   - 태그 확인 (CacheHelper::has): " . ($hasCacheWithTag ? '✅ 존재' : '❌ 없음'));
            $this->info("   - 실제 데이터 조회: " . ($cachedData !== null ? '✅ 존재' : '❌ 없음'));

            if ($cachedData !== null) {
                $this->info('✅ 캐시가 존재합니다!');
                $this->info("📊 캐시된 카테고리 수: " . count($cachedData));
                $this->info("⏰ 캐시 만료 시간: 24시간");
            } else {
                $this->warn('❌ 캐시가 존재하지 않습니다.');
            }

            $this->newLine();

            // 성능 테스트
            $this->info('🚀 성능 테스트 시작...');

            // 첫 번째 호출 (캐시 없을 수 있음)
            $startTime = microtime(true);
            $categories1 = $this->categoryService->getAllCategories();
            $time1 = (microtime(true) - $startTime) * 1000;

            // 두 번째 호출 (캐시에서 조회)
            $startTime = microtime(true);
            $categories2 = $this->categoryService->getAllCategories();
            $time2 = (microtime(true) - $startTime) * 1000;

            $this->info("📈 첫 번째 호출 시간: " . number_format($time1, 2) . "ms");
            $this->info("📈 두 번째 호출 시간: " . number_format($time2, 2) . "ms");

            if ($time2 < $time1) {
                $improvement = (($time1 - $time2) / $time1) * 100;
                $this->info("🎉 캐시 효과: " . number_format($improvement, 1) . "% 성능 향상!");
            }

            $this->newLine();

            // 캐시 드라이버 확인
            $this->info('🔧 캐시 드라이버 확인...');
            $driver = config('cache.default');
            $this->info("✅ 현재 캐시 드라이버: {$driver}");

            // 캐시 연결 테스트
            Cache::put('test_key', 'test_value', 60);
            $testValue = Cache::get('test_key');
            if ($testValue === 'test_value') {
                $this->info('✅ 캐시 연결 정상');
                Cache::forget('test_key');
            } else {
                $this->error('❌ 캐시 연결 실패');
            }

            // 태그 기반 캐시 테스트
            $this->info('🏷️  태그 기반 캐시 테스트...');
            Cache::tags($tag)->put('test_tagged_key', 'test_tagged_value', 60);
            $taggedValue = Cache::tags($tag)->get('test_tagged_key');
            if ($taggedValue === 'test_tagged_value') {
                $this->info('✅ 태그 기반 캐시 정상');
                Cache::tags($tag)->forget('test_tagged_key');
            } else {
                $this->error('❌ 태그 기반 캐시 실패');
            }

            // 실제 카테고리 캐시 강제 생성 테스트
            $this->newLine();
            $this->info('🔧 실제 카테고리 캐시 강제 생성 테스트...');
            
            // 캐시를 먼저 무효화
            $this->categoryService->flushCategoryCache();
            
            // 캐시 생성
            $startTime = microtime(true);
            $categories = $this->categoryService->getAllCategories();
            $cacheTime = (microtime(true) - $startTime) * 1000;
            
            $this->info("📊 카테고리 데이터 생성 완료 (소요시간: " . number_format($cacheTime, 2) . "ms)");
            $this->info("📈 생성된 카테고리 수: " . count($categories));
            
            // 생성 후 캐시 상태 재확인
            $cachedDataAfter = CacheHelper::getCache($cacheKey, $tag);
            $this->info("🔍 생성 후 캐시 상태: " . ($cachedDataAfter !== null ? '✅ 존재' : '❌ 없음'));
            
            if ($cachedDataAfter !== null) {
                $this->info("📊 캐시된 카테고리 수: " . count($cachedDataAfter));
            }

            // 캐시 무효화 옵션
            if ($this->option('clear')) {
                $this->newLine();
                $this->info('🗑️  캐시를 무효화하고 새로 생성합니다...');
                $this->categoryService->flushCategoryCache();

                // 새로 캐시 생성
                $startTime = microtime(true);
                $this->categoryService->getAllCategories();
                $time = (microtime(true) - $startTime) * 1000;

                $this->info("✅ 새 캐시 생성 완료 (소요시간: " . number_format($time, 2) . "ms)");
            }

            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ 캐시 연결 오류: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
