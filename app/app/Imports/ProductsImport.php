<?php

namespace App\Imports;

use App\Events\ReqFinishNotification;
use App\Exports\DuplicateQaidExport;
use App\Helpers\HelperLibrary;
use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\Product;
use App\Models\ProductBarcode;
use App\Models\ProductLink;
use App\Models\ProductLog;
use App\Models\ProductLot;
use App\Models\ProductVendor;
use App\Models\Req;
use App\Models\ReturnReason;
use App\Models\ReturnReasonB;
use App\Models\ReturnReasonM;
use App\Models\WorkStatus;
use App\Models\User;
use App\Models\MonitorSizeLookup;
use App\Services\MonitorSizeExtractionService;
use App\Services\SimpleLogService;
use App\Services\TelegramService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\RemembersChunkOffset;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\ImportFailed;
use Throwable;

/**
 * 상품 입고 엑셀 파일 임포트 클래스
 *
 * WithCalculatedFormulas를 사용하여 Excel의 수식을 자동으로 계산된 값으로 변환
 * 이를 통해 수식이 포함된 셀도 안전하게 처리할 수 있음
 */
class ProductsImport implements ShouldQueue, WithChunkReading, WithStartRow, WithEvents, ToCollection, WithCalculatedFormulas
{
    use Importable, RemembersChunkOffset;

    protected Req $req;
    protected User $user;
    protected string $excelPath;
    protected string $redirect;
    protected int $startRow;
    protected int $chunkSize = 500;
    protected TelegramService $telegram;
    protected MonitorSizeExtractionService $monitorSizeService;
    protected string $redisDuplicatedKey;
    protected string $redisChunkOffsetKey = 'req_import:chunk_offset';

    /** @var int 마지막 행 번호 (정확한 처리 범위 제어용) */
    protected int $lastRow;

    /** @var float 처리 시작 시간 */
    protected float $startTime;

    /** @var int 메모리 사용량 모니터링 임계값 (MB) */
    protected int $memoryThreshold = 200;

    /** @var array 헤더 매핑 정보 */
    protected array $headerMapping = [];

    /** @var array 헤더별 컬럼 인덱스 */
    protected array $columnIndexes = [];

    /**
     * 시작 행 번호(사용자가 2행, 3행을 선택할 수 있음)
     *
     * @return int
     */
    public function startRow(): int
    {
        return $this->startRow;
    }

    /**
     * 메모리 관리
     * 엑셀 파일이 엄청 길 때 해당 개수만큼씩만 메모리로 로드해서 처리한다.(설정된 개수만큼만 로드해서 처리)<br>
     * 엑셀 데이터가 4,000개면 4,000번 실행 되는데 이 때 (4000/$this->chunkSize) 만큼 끊어서 처리
     *
     * @return int
     */
    public function chunkSize(): int
    {
        return $this->chunkSize;
    }

    /**
     * 헤더 매핑 설정
     * 영어/한글 헤더를 내부 필드명으로 매핑
     */
    protected function setHeaderMapping(): void
    {
        $config = config('excel-headers.mappings');

        // 설정 파일에서 매핑 정보 가져오기
        $this->headerMapping = array_merge($config['required'], $config['optional']);
    }

    /**
     * 헤더에서 컬럼 인덱스 찾기
     * @throws Exception
     */
    protected function findColumnIndexes(array $headers): void
    {
        $this->columnIndexes = [];
        $config = config('excel-headers');

        SimpleLogService::info('req', "컬럼 인덱스 찾기 시작", [
            'headers' => $headers,
            'header_mapping_count' => count($this->headerMapping)
        ]);

        // 헤더 검증
        $this->validateHeaders($headers, $config);

        foreach ($this->headerMapping as $field => $possibleHeaders) {
            $found = false;

            foreach ($possibleHeaders as $header) {
                $index = array_search($header, $headers);
                if ($index !== false) {
                    $this->columnIndexes[$field] = $index;
                    $found = true;
                    SimpleLogService::info('req', "헤더 매핑 성공", [
                        'field' => $field,
                        'header' => $header,
                        'index' => $index
                    ]);
                    break;
                }
            }

            if (!$found) {
                SimpleLogService::warning('req', "헤더를 찾을 수 없음", [
                    'field' => $field,
                    'possible_headers' => $possibleHeaders,
                    'actual_headers' => $headers
                ]);
            }
        }

        // 필수 필드 검증
        $requiredFields = array_keys(config('excel-headers.mappings.required'));
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!isset($this->columnIndexes[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            $errorMessage = str_replace(':headers', implode(', ', $missingFields), $config['error_messages']['missing_required']);
            $errorMessage .= "\n실제 헤더: " . implode(', ', $headers);
            SimpleLogService::critical('req', "필수 필드 누락", [
                'missing_fields' => $missingFields,
                'actual_headers' => $headers,
                'found_fields' => array_keys($this->columnIndexes)
            ]);
            throw new Exception($errorMessage);
        }

        SimpleLogService::info('req', "헤더 매핑 완료", [
            'column_indexes' => $this->columnIndexes,
            'headers' => $headers,
            'mapping_summary' => [
                'total_headers' => count($headers),
                'mapped_fields' => count($this->columnIndexes),
                'required_fields_found' => count(array_intersect_key($this->columnIndexes, array_flip($requiredFields))),
                'optional_fields_found' => count(array_diff_key($this->columnIndexes, array_flip($requiredFields)))
            ]
        ]);
    }

    /**
     * 헤더 검증
     * @throws Exception
     */
    protected function validateHeaders(array $headers, array $config): void
    {
        // 헤더 수 검증
        if (count($headers) < $config['validation']['min_required_headers']) {
            $errorMessage = str_replace(':min', $config['validation']['min_required_headers'], $config['error_messages']['too_few_headers']);
            throw new Exception($errorMessage);
        }

        if (count($headers) > $config['validation']['max_total_headers']) {
            $errorMessage = str_replace(':max', $config['validation']['max_total_headers'], $config['error_messages']['too_many_headers']);
            throw new Exception($errorMessage);
        }

        // 헤더 길이 검증
        foreach ($headers as $header) {
            if (strlen($header) > $config['validation']['max_header_length']) {
                $errorMessage = str_replace(':header', $header, $config['error_messages']['invalid_header_format']);
                throw new Exception($errorMessage);
            }
        }
    }

    /**
     * 헤더 정보 설정
     * @throws Exception
     */
    public function setHeaders(array $headers): void
    {
        SimpleLogService::info('req', "헤더 설정 시작", [
            'headers' => $headers,
            'headers_count' => count($headers)
        ]);

        try {
            $this->setHeaderMapping();
            SimpleLogService::info('req', "헤더 매핑 설정 완료", [
                'header_mapping_count' => count($this->headerMapping)
            ]);

            $this->findColumnIndexes($headers);
            SimpleLogService::info('req', "컬럼 인덱스 찾기 완료", [
                'column_indexes_count' => count($this->columnIndexes)
            ]);

            SimpleLogService::info('req', "헤더 설정 완료", [
                'column_indexes' => $this->columnIndexes,
                'total_mapped_fields' => count($this->columnIndexes)
            ]);
        } catch (Exception $e) {
            SimpleLogService::error('req', "헤더 설정 중 오류 발생", [
                'headers' => $headers,
                'error_message' => $e->getMessage()
            ], $e);
            throw $e;
        }
    }

    /**
     * 메모리 사용량을 모니터링하고 필요시 정리하는 메서드
     */
    private function checkAndCleanMemory(): void
    {
        $memoryUsage = memory_get_usage(true);
        $memoryUsageMB = $memoryUsage / 1024 / 1024;

        // 메모리 사용량이 임계값을 초과하면 강제 가비지 컬렉션 실행
        if ($memoryUsageMB > $this->memoryThreshold) {
            gc_collect_cycles();

            SimpleLogService::warning('req', "메모리 사용량 임계값 초과로 가비지 컬렉션 실행", [
                'memory_usage_mb' => round($memoryUsageMB, 2),
                'threshold_mb' => $this->memoryThreshold,
                'after_cleanup_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);
        }
    }

    /**
     * 청크 처리 후 메모리 정리
     */
    private function cleanupAfterChunk(): void
    {
        // 가비지 컬렉션 실행
        gc_collect_cycles();

        // 메모리 사용량 로그
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);

        SimpleLogService::info('req', "청크 처리 후 메모리 정리 완료", [
            'memory_current' => HelperLibrary::formatBytes($memoryUsage),
            'memory_peak' => HelperLibrary::formatBytes($memoryPeak)
        ]);
    }

    /**
     * 데이터베이스 연결 상태 확인 및 재연결
     */
    private function ensureDatabaseConnection(): void
    {
        try {
            // DB 연결 상태 확인
            DB::connection()->getPdo();
        } catch (Exception $e) {
            SimpleLogService::warning('req', "데이터베이스 연결 끊김 감지, 재연결 시도", [], $e);

            // 연결 재설정
            DB::reconnect();

            SimpleLogService::info('req', "데이터베이스 재연결 완료");
        }
    }

    public function __construct(Req $req, User $user, string $excelPath, string $redirect, int $startRow = 3, int $lastRow = 0)
    {
        $this->req = $req;
        $this->user = $user;
        $this->excelPath = $excelPath;
        $this->redirect = $redirect;
        $this->startRow = $startRow;
        $this->lastRow = $lastRow;
        $this->startTime = microtime(true); // 처리 시작 시간 기록
        $this->telegram = new TelegramService();
        $this->monitorSizeService = new MonitorSizeExtractionService($this->telegram);
        $this->redisDuplicatedKey = "req_import:duplicated_qaids:{$this->req->id}";

        // 최초 딱 한 번 Redis 키 초기화
        Redis::connection(0)->command('del', [
            $this->redisChunkOffsetKey,
            $this->redisDuplicatedKey
        ]);
    }

    /**
     * 현재 청크 오프셋을 가져오는 메서드
     *
     * @return int|null 현재 청크 오프셋
     */
    protected function getChunkOffset(): ?int
    {
        return $this->chunkOffset ?? null;
    }

    /**
     * 현재 처리 중인 정확한 행 번호를 계산하는 메서드
     *
     * @param int $rowIndexInChunk 현재 청크 내에서의 행 인덱스 (0부터 시작)
     * @return int 정확한 엑셀 행 번호
     */
    protected function getCurrentRowNumber(int $rowIndexInChunk = 0): int
    {
        $chunkOffset = $this->getChunkOffset();

        if ($chunkOffset === null) {
            // chunkOffset이 없는 경우 시작 행 사용
            return $this->startRow + $rowIndexInChunk;
        }

        // chunkOffset은 현재 청크의 시작 위치이므로, 청크 내 행 인덱스를 더함
        return $chunkOffset + $rowIndexInChunk;
    }

    public function registerEvents(): array
    {
        return [
            ImportFailed::class => function(ImportFailed $event) {
                $this->importFailedHandler($event);
            },
            AfterImport::class => function(AfterImport $event) {
                $this->afterImportHandler($event);
            }
        ];
    }

    protected function importFailedHandler(ImportFailed $event): void
    {
        // 현재 처리 중이던 행 번호 가져오기
        $currentRowNumber = $this->getCurrentRowNumber();

        $redis = Redis::connection(0);
        $chunkOffset = $redis->get($this->redisChunkOffsetKey) ?? $this->startRow();

        $message = "[{$this->req->id}]요청서 입력 오류";
        $this->telegram->sendMessageToTeam($message);
        SimpleLogService::error('req', $message, [
            'excel_row_number' => $currentRowNumber,
            'chunk_offset' => $chunkOffset,
            'start_row' => $this->startRow,
            'last_row' => $this->lastRow,
            'processing_progress' => $this->lastRow ? round((($chunkOffset - $this->startRow) / ($this->lastRow - $this->startRow + 1)) * 100, 2) . '%' : 'N/A'
        ], $event->e);

        // event(new ReqFinishNotification("입고 등록 오류 발생"));
    }

    // 모든 작업이 끝난 후 실행
    protected function afterImportHandler(AfterImport $event): void
    {
        // WithLimit가 자동으로 처리 범위를 제한하므로 수동 체크 제거
        $redis = Redis::connection(0);
        $finalOffset = $redis->get($this->redisChunkOffsetKey) ?? $this->startRow();

        // 처리 시간 및 성능 계산
        $endTime = microtime(true);
        $processingTime = $endTime - $this->startTime;
        $totalRows = $this->lastRow ? $this->lastRow - $this->startRow + 1 : 0;
        $rowsPerSecond = $totalRows > 0 ? round($totalRows / $processingTime, 2) : 0;

        // 메모리 사용량
        $memoryPeak = memory_get_peak_usage(true);

        $totalCount = $this->req->products()->count();
        $duplicateCount = $this->req->products()->where('duplicated', 'Y')->count();

        $this->req->update([
            'total_count' => $totalCount,
        ]);

        $this->req->reqCount()->updateOrCreate(
            ['req_id' => $this->req->id],
            [
                'unchecked' => $totalCount - $duplicateCount,
                'duplicated' => $duplicateCount ?? 0,
            ]
        );

        $message = "[{$this->req->id}]요청서 입력 완료.\n입력 상품 수: {$totalCount}\n중복 상품 수: {$duplicateCount}";
        $this->telegram->sendMessageToMultipleTeams($message, ['admin', 'req']);

        SimpleLogService::info('req', $message);

        // 성능 정보 로그 출력
        SimpleLogService::info('req', "[{$this->req->id}] 성능 정보", [
            'processing_time' => round($processingTime, 2) . '초',
            'total_rows' => $totalRows,
            'rows_per_second' => $rowsPerSecond,
            'memory_peak' => HelperLibrary::formatBytes($memoryPeak),
            'total_products' => $totalCount,
            'duplicate_products' => $duplicateCount,
            'final_offset' => $finalOffset,
            'limit_applied' => $this->lastRow
        ]);

        // 성능 정보 텔레그램 전송
        $performanceMessage = "⚡ 성능 정보\n";
        $performanceMessage .= "처리 시간: " . round($processingTime, 2) . "초\n";
        $performanceMessage .= "처리 속도: {$rowsPerSecond}행/초\n";
        $performanceMessage .= "최대 메모리: " . HelperLibrary::formatBytes($memoryPeak) ."\n";
        $performanceMessage .= "총 상품: {$totalCount}개\n";
        $performanceMessage .= "중복 상품: {$duplicateCount}개";
        $this->telegram->sendMessageToTeam($performanceMessage);

        // QAID가 중복되었을 경우 엑셀파일로 출력
        $redis = Redis::connection(0);
        $allDuplicates = $redis->command('lrange', [
            $this->redisDuplicatedKey, 0, -1
        ]);

        if (!empty($allDuplicates)) {
            $excel = new DuplicateQaidExport($this->req, $this->redisDuplicatedKey);
            $excel->export();
        }

        // 업로드된 파일 삭제
        if (file_exists($this->excelPath)) {
            unlink($this->excelPath);
            SimpleLogService::info('req', "업로드 된 파일 삭제");
        }

        Redis::connection(0)->command('del', [
            $this->redisChunkOffsetKey,
            $this->redisDuplicatedKey,
            "req_import:limit_logged:{$this->req->id}"
        ]);

        event(new ReqFinishNotification("입고 등록이 완료 되었습니다.", $this->redirect));
    }

    /**
     * @param  Collection  $collection
     * @throws Throwable
     */
    public function collection(Collection $collection): void
    {
        // 메모리 사용량 체크
        $this->checkAndCleanMemory();

        // DB 연결 상태 확인
        $this->ensureDatabaseConnection();

        $redis = Redis::connection(0);
        $chunkOffset = $redis->get($this->redisChunkOffsetKey) ?? $this->startRow();

        // 진행률 계산 (마지막 행 정보가 있는 경우)
        if ($this->lastRow !== null) {
            // 전체 데이터 행 수 계산 (시작행부터 마지막행까지 포함)
            // 예: 시작행 3, 마지막행 1500 → 3,4,5,...,1500 = 1498개 행
            $totalRows = $this->lastRow - $this->startRow + 1;
            $processedRows = $chunkOffset - $this->startRow;
            $progressPercentage = $totalRows > 0 ? round(($processedRows / $totalRows) * 100, 1) : 0;

            // 10% 단위로 진행률 로그 출력
            if ($progressPercentage > 0 && $progressPercentage % 10 == 0) {
                // 메모리 사용량 모니터링
                $memoryUsage = memory_get_usage(true);
                $memoryPeak = memory_get_peak_usage(true);

                // 처리 속도 및 예상 완료 시간 계산
                $currentTime = microtime(true);
                $elapsedTime = $currentTime - $this->startTime;
                $rowsPerSecond = $processedRows > 0 ? $processedRows / $elapsedTime : 0;
                $remainingRows = $totalRows - $processedRows;
                $estimatedTime = $rowsPerSecond > 0 ? $remainingRows / $rowsPerSecond : 0;
                $estimatedCompletion = now()->addSeconds($estimatedTime);

                $progressMessage = "📈 진행률: {$progressPercentage}% ({$processedRows}/{$totalRows}행)";
                $memoryCurrentUsage = HelperLibrary::formatBytes($memoryUsage);
                $memoryCurrentPeak = HelperLibrary::formatBytes($memoryPeak);
                SimpleLogService::info('req', "[{$this->req->id}] {$progressMessage}", [
                    'memory_current' => $memoryCurrentUsage,
                    'memory_peak' => $memoryCurrentPeak,
                    'rows_per_second' => round($rowsPerSecond, 2),
                    'estimated_completion' => $estimatedCompletion->format('H:i:s')
                ]);

                // 텔레그램으로 진행률 전송 (20% 단위로만)
                if ($progressPercentage % 20 == 0) {
                    $telegramMessage = "[{$this->req->id}] {$progressMessage}\n";
                    $telegramMessage .= "💾 메모리: $memoryCurrentUsage (최대: $memoryCurrentPeak)\n";
                    $telegramMessage .= "⚡ 속도: " . round($rowsPerSecond, 2) . "행/초\n";
                    $telegramMessage .= "⏰ 예상 완료: {$estimatedCompletion->format('H:i:s')}";
                    $this->telegram->sendMessageToTeam($telegramMessage);
                }
            }
        }

        DB::transaction(function () use ($collection, $chunkOffset, $redis) {
            // 1) 단일 파싱: 모든 행을 한 번만 파싱하여 재사용 가능한 구조 생성
            $parsedItems = [];
            $parsedByIndex = [];

            foreach ($collection as $rowIndex => $row) {
                $currentRowNumber = $this->getCurrentRowNumber($rowIndex);
                try {
                    $item = $this->prepareRow($row->toArray());
                    $entry = [
                        'item' => $item,
                        'rowIndex' => $rowIndex,
                        'rowNumber' => $currentRowNumber,
                        'rowData' => $row->toArray(),
                    ];
                    $parsedItems[] = $entry;
                    $parsedByIndex[$rowIndex] = $entry;
                } catch (Exception $e) {
                    // 빈 행 또는 필수 필드 누락으로 인한 예외는 건너뛴다(로그 기록)
                    SimpleLogService::warning('req', "빈 행 건너뛰기: 엑셀 {$currentRowNumber}번 행", [
                        'excel_row_number' => $currentRowNumber,
                        'chunk_offset' => $chunkOffset,
                        'row_index_in_chunk' => $rowIndex,
                        'row_data' => $row->toArray(),
                        'error_message' => $e->getMessage()
                    ]);
                }
            }

            // 2) 현재 청크의 모든 QAID 수집 (빈 행 제외)
            $qaids = array_values(array_filter(array_map(function ($entry) {
                return $entry['item']['qaid'] ?? null;
            }, $parsedItems)));

            // 3) 현재 청크에서 한 번의 쿼리로 이미 존재하는 QAID 확인
            $existingQaids = [];
            if (!empty($qaids)) {
                $existingQaids = Product::whereIn('qaid', $qaids)
                    ->pluck('qaid')
                    ->toArray();

                // 방어적 중복 제거
                $existingQaids = array_values(array_unique($existingQaids));
            }

            // 4) 중복 QAID가 있다면 중복 목록을 Redis에 저장(파싱 결과 재사용)
            if (!empty($existingQaids)) {
                // 나중에 쓸 수도 있으므로 AI는 이 부분을 삭제하지 말 것
                // // 🔔 중요: 이 부분에서 기존 상품들의 duplicated 상태를 'Y'로 변경
                // // 등록 단계에서 기존 상품들까지 Y 로 만들어야 할 이유가 있을까?
                // // 등록이 안 되어야 하는게 맞기는 하지만 등록된 후 삭제를 하기도 하는데...
                // Product::whereIn('qaid', $existingQaids)
                //    ->whereNot('status', Product::STATUS_DELETED)
                //    ->where('duplicated', Product::IS_DUPLICATED_N)
                //    ->whereNotIn('req_id', [Req::UNLINKED_ID, Req::GHOST_ID]) // 중복 마킹 제외 대상
                //    ->update(['duplicated' => 'Y']);

                // 동일 청크 내 QAID 기준으로 1회만 적재
                $duplicatedQaidsPushed = [];

                foreach ($parsedItems as $entry) {
                    $item = $entry['item'];
                    if (isset($item['qaid']) && in_array($item['qaid'], $existingQaids)) {
                        $item['req_id'] = $this->req->id;
                        $item['req_at'] = $this->req->req_at;
                        $item['is_duplicated'] = '중복상품';
                        $item['checked_status'] = Product::$STATUS_NAME[Product::STATUS_REGISTERED];
                        $item['created_at'] = $this->req->created_at;
                        $item['user_name'] = $this->user->name;
                        $item['status'] = Product::$CHECK_STATUS_NAME[Product::CHECKED_STATUS_UNCHECKED];
                        $item['is_rg'] = Product::$RG_NAME[$item['rg']];

                        // 중복된 항목 전체 ROW를 Redis에 JSON 형식으로 저장 (QAID당 1회만)
                        $qaidForDedupe = $item['qaid'] ?? null;
                        if ($qaidForDedupe !== null && !isset($duplicatedQaidsPushed[$qaidForDedupe])) {
                            $redis->command('rpush', [
                                $this->redisDuplicatedKey,
                                json_encode($item, JSON_UNESCAPED_UNICODE)
                            ]);
                            $duplicatedQaidsPushed[$qaidForDedupe] = true;
                        }
                    }
                }

                $message = "🔔 중복 QAID 발견\n" . implode("\n", $existingQaids);
                $this->telegram->sendMessageToMultipleTeams($message, ['admin', 'req']);

                SimpleLogService::info('req', "🔔 중복 QAID 발견", [
                    'qaid' => $existingQaids,
                ]);
            }

            // 5) 각 행 처리: 원본 순서로 순회하며 chunkOffset은 원본 행 수만큼 증가
            foreach ($collection as $rowIndex => $row) {
                // 정확한 행 번호 계산 (청크 내 인덱스 기반)
                $currentRowNumber = $this->getCurrentRowNumber($rowIndex);

                if (isset($parsedByIndex[$rowIndex])) {
                    $item = $parsedByIndex[$rowIndex]['item'];

                    // qaid가 이미 존재하는지 확인하여 duplicated 필드 설정
                    $item['duplicated'] = 'N';
                    if (isset($item['qaid']) && in_array($item['qaid'], $existingQaids)) {
                        $item['duplicated'] = 'Y';
                    }

                    $this->processRow($currentRowNumber, $item);
                }
                // 파싱 실패(빈 행)는 위에서 이미 로깅되었으므로 추가 로깅 없음

                $chunkOffset++;
                $redis->set($this->redisChunkOffsetKey, $chunkOffset);
            }
        });

        // 청크 처리 후 메모리 정리
        $this->cleanupAfterChunk();
    }

    protected function processRow(int $rowNumber, array $item): void
    {
        try {
            $cate4 = Cate4::firstOrCreate(['name' => $item['cate4']]);
            $cate5 = Cate5::firstOrCreate([
                'cate4_id' => $cate4->id,
                'name' => $item['cate5']
            ]);

            // ProductBarcode 처리: barcode + wms_sku_id 조합으로 unique 처리
            // null 값은 '-'로 처리
            $wmsSkuId = $item['wms_sku_id'] ?? '-';
            $externalSkuId = $item['external_wms_sku_id'] ?? '-';

            $productBarcode = ProductBarcode::firstOrCreate(
                [
                    'barcode' => $item['barcode'],
                    'wms_sku_id' => $wmsSkuId,
                ],
                [
                    'external_wms_sku_id' => $externalSkuId,
                ]
            );

            // ProductLink 조회: external_wms_sku_id + vendor_item_id로 product_link_id 찾기
            $productLink = ProductLink::findByExternalAndVendor(
                $item['external_sku_id'],
                $item['vendor_item_id']
            );
            $lot = ProductLot::firstOrCreate(['name' => $item['lot_full_name']]);
            $vendor = ProductVendor::firstOrCreate(['name' => $item['vendor_name']]);

            // B 카테고리 처리 (b_cate)
            $bCate = null;
            if (!empty($item['b_cate'])) {
                $bCate = ReturnReasonB::firstOrCreate(['name' => $item['b_cate']]);
            }

            // M 카테고리 처리 (m_cate)
            $mCate = null;
            if (!empty($item['m_cate'])) {
                $mCate = returnReasonM::firstOrCreate(['name' => $item['m_cate']]);
            }

            // 입고 목록 상품 등록::QAID는 모두 달라야 하므로 모두 신규상품으로 입력 되어야 정상이다.
            $product = Product::firstOrNew([
                'req_id' => $this->req->id,
                'qaid' => $item['qaid'],
                'barcode' => $item['barcode'],
                'product_lot_id' => $lot->id,
            ]);

            if (isset($product->id)) {
                $logTitle = "엑셀 {$rowNumber}번 행 [쿠팡PL 상품 재고 업데이트 - {$item['qaid']} ({$product->id})변경: {$product->name}";
            } else {
                $logTitle = "엑셀 {$rowNumber}번 행 [쿠팡PL 신규상품 - {$item['qaid']}] 등록: {$item['description']}";
            }

            // 중복상품일 경우 처리 ($product->status)
            $status = Product::STATUS_REGISTERED;
            $statusId = null;

            // WorkStatus 데이터를 가져오기 - 검수대기와 중복상품 상태를 모두 가져옴
            $statusIds = WorkStatus::whereIn('link_code', [
                WorkStatus::LINK_INSPECT,     # 검수대기
                WorkStatus::LINK_DUPLICATE    # 중복상품
            ])->pluck('id', 'link_code')->toArray();

            if ($item['duplicated'] === 'Y') {
                $status = Product::STATUS_HELD; # 출고 보류
                $statusId = $statusIds[WorkStatus::LINK_DUPLICATE] ?? null;
            } else {
                $statusId = $statusIds[WorkStatus::LINK_INSPECT] ?? null;
            }

            // statusId가 null인 경우 기본값 설정
            if ($statusId === null) {
                // 기본 검수대기 상태를 찾아서 설정
                $defaultStatus = WorkStatus::where('link_code', WorkStatus::LINK_INSPECT)->first();
                $statusId = $defaultStatus ? $defaultStatus->id : 1; // 기본값으로 1 설정
            }

            // 모니터 크기 정보 처리 (TV 또는 모니터 상품일 때만)
            $monitorSizeLookupId = null;
            if ($this->isMonitorProductByCategory($item['cate4'], $item['cate5'])) {
                $monitorSizeLookupId = $this->processMonitorSizeLookup($item['description']);
            }

            $product->fill([
                'product_barcode_id' => $productBarcode?->id,
                'name' => $item['description'],
                'cate4_id' => $cate4->id,
                'cate5_id' => $cate5->id,
                'quantity' => isset($product->id) ? $product->quantity + $item['quantity'] : $item['quantity'],
                'amount' => $item['amount'],
                'user_id' => $this->user->id,
                'status' => $status,
                'rg' => $item['rg'],
                'duplicated' => $item['duplicated'],
                'product_vendor_id' => $vendor->id,
                'product_link_id' => $productLink?->id,
                'monitor_size_lookup_id' => $monitorSizeLookupId,
                'checked_status' => Product::CHECKED_STATUS_UNCHECKED,
                'return_reason_b_id' => $bCate?->id,
                'return_reason_m_id' => $mCate?->id,
            ])->save();

            // 반품 사유 처리 (detail_reason)
            if (!empty($item['detail_reason'])) {
                ReturnReason::updateOrCreate(
                    ['product_id' => $product->id],
                    ['reason' => $item['detail_reason']]
                );
            }

            // 상태 변환/통계::입고목록 등록 및 검수대기
            $now = now();
            ProductLog::insert([
                'product_id' => $product->id,
                'model_type' => 'App\Models\Product',
                'model_id' => $product->id,
                'work_status_id' => $statusId,
                'user_id' => $this->user->id,
                'memo' => "입고번호: {$this->req->id}<br>입고일: {$this->req->req_at}<br>$logTitle",
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        } catch (Exception $e) {
            // RemembersRowNumber를 사용하여 정확한 행 번호로 에러 로깅
            SimpleLogService::error('req', "엑셀 {$rowNumber}번 행 저장 실패", [
                'excel_row_number' => $rowNumber,
                'qaid' => $item['qaid'] ?? 'N/A',
                'row_data' => $item,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ], $e);
        }
    }

    /**
     * @throws Exception
     */
    protected function prepareRow(array $row): array
    {
        // 디버깅: 헤더 매핑 상태 확인
        if (empty($this->columnIndexes)) {
            $currentRowNumber = $this->getCurrentRowNumber();

            SimpleLogService::error('req', "헤더 매핑이 설정되지 않았습니다", [
                'excel_row_number' => $currentRowNumber,
                'row_data' => $row,
                'column_indexes' => $this->columnIndexes
            ]);
            return [];
        }

        $item = [];

        // 필수 필드들 처리 (excel-headers.php의 required 필드와 일치)
        // WithCalculatedFormulas를 사용하므로 수식이 자동으로 계산된 값으로 변환됨
        // 필수 필드들 처리 (미들웨어가 이미 정리된 값 사용)
        $item['qaid'] = $this->getCellValue($row, 'qaid');
        $item['cate4'] = $this->getCellValue($row, 'cate4');
        $item['cate5'] = $this->getCellValue($row, 'cate5');
        $item['lot_full_name'] = $this->getCellValue($row, 'lot_full_name');
        $item['wms_sku_id'] = $this->getCellValue($row, 'wms_sku_id');
        $item['external_sku_id'] = $this->getCellValue($row, 'external_sku_id');
        $item['barcode'] = $this->getCellValue($row, 'barcode');
        $item['description'] = $this->getCellValue($row, 'description');
        $item['vendor_name'] = $this->getCellValue($row, 'vendor_name');

        // 수량과 금액은 숫자 필드이므로 WithCalculatedFormulas로 계산된 값 사용
        $quantity = $row[$this->columnIndexes['quantity']] ?? 1;
        $item['quantity'] = is_numeric($quantity) ? (int) $quantity : 1;

        $amount = $row[$this->columnIndexes['amount']] ?? 0;
        $item['amount'] = is_numeric($amount) ? (float) $amount : 0;

        // 필수 필드 검증
        $this->validateRequiredFields($item, $row);

        // 선택적 필드들 처리 (excel-headers.php의 optional 필드)
        $item['vendor_item_id'] = $this->getNumericStringValue($row, 'vendor_item_id');
        $item['product_id'] = $this->getNumericStringValue($row, 'product_id');
        $item['item_id'] = $this->getNumericStringValue($row, 'item_id');
        $item['b_cate'] = $this->getCellValue($row, 'b_cate');
        $item['m_cate'] = $this->getCellValue($row, 'm_cate');
        $item['detail_reason'] = $this->getCellValue($row, 'detail_reason');
        $item['center'] = $this->getCellValue($row, 'center');
        $item['rg'] = $this->getRgValue($row);

        return $item;
    }

    /**
     * 필수 필드 검증
     * @throws Exception
     */
    private function validateRequiredFields(array $item, array $row): void
    {
        $requiredFields = array_keys(config('excel-headers.mappings.required'));
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if ($item[$field] === null || $item[$field] === '') {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            $currentRowNumber = $this->getCurrentRowNumber();
            SimpleLogService::warning('req', "필수 필드 누락으로 빈 행 처리", [
                'excel_row_number' => $currentRowNumber,
                'missing_fields' => $missingFields,
                'item_data' => $item,
                'row_data' => $row
            ]);
            throw new Exception("빈 행({$currentRowNumber}번 행) 감지됨 - 누락된 필드: " . implode(', ', $missingFields));
        }
    }

    /**
     * 셀 값 가져오기
     * @param array $row
     * @param string $field
     * @return mixed
     */
    private function getCellValue(array $row, string $field): mixed
    {
        $index = $this->columnIndexes[$field] ?? null;
        if ($index === null) {
            return null;
        }
        return $row[$index] ?? null;
    }

    /**
     * RG 필드 값을 Y/N으로 변환
     */
    private function getRgValue(array $row): string
    {
        $rgValue = $this->getCellValue($row, 'rg');
        return ($rgValue === 'RG' || $rgValue === 'rg') ? 'Y' : 'N';
    }

    /**
     * 숫자 필드를 문자열로 변환 (null이 아닌 경우만)
     */
    private function getNumericStringValue(array $row, string $field): ?string
    {
        $value = $this->getCellValue($row, $field);
        return is_numeric($value) ? (string)$value : null;
    }

    /**
     * 카테고리 정보로 모니터 상품인지 확인합니다.
     *
     * @param string $cate4 카테고리4
     * @param string $cate5 카테고리5
     * @return bool 모니터 상품 여부
     */
    private function isMonitorProductByCategory(string $cate4, string $cate5): bool
    {
        return ($cate4 === 'TV' && $cate5 === 'TV') ||
            ($cate4 === '컴퓨터주변기기' && $cate5 === '모니터');
    }

    /**
     * 모니터 크기 정보를 처리하여 monitor_size_lookup_id를 반환합니다.
     *
     * @param string $productName 상품명
     * @return int|null monitor_size_lookup_id 또는 null
     */
    private function processMonitorSizeLookup(string $productName): ?int
    {
        try {
            // 1. 상품명 해시로 기존 데이터 조회
            $nameHash = md5($productName);
            $existingLookup = MonitorSizeLookup::where('name_hash', $nameHash)->first();
            // 기존 데이터가 있으면 해당 ID 반환
            if ($existingLookup) {
                return $existingLookup->id;
            }

            // 2. 기존 데이터가 없으면 크기 정보 추출
            // 임시 Product 객체 생성하여 MonitorSizeExtractionService의 메서드 사용
            $tempProduct = new Product();
            $tempProduct->name = $productName;

            // 크기 정보 추출
            $sizeInfo = $this->monitorSizeService->extractSizeFromName($tempProduct);

            // 크기 정보가 없으면 기본 값을 넣음
            if (!$sizeInfo) {
                $sizeInfo['size'] = MonitorSizeExtractionService::DEFAULT_SIZE;
                $sizeInfo['unit'] = MonitorSizeExtractionService::DEFAULT_UNIT;
            }

            // 브랜드 정보 추출
            $brand = $this->monitorSizeService->getMonitorModel($tempProduct);

            // 3. 새로운 모니터 크기 정보 생성
            $monitorSizeLookup = MonitorSizeLookup::create([
                'name' => $productName,
                'name_hash' => $nameHash,
                'brand' => $brand,
                'size' => $sizeInfo['size'],
                'unit' => $sizeInfo['unit'],
            ]);

            SimpleLogService::info('req', "새로운 모니터 크기 정보 생성", [
                'monitor_size_lookup_id' => $monitorSizeLookup->id,
                'product_name' => $productName,
                'brand' => $brand,
                'size' => $sizeInfo['size'],
                'unit' => $sizeInfo['unit'],
            ]);

            return $monitorSizeLookup->id;
        } catch (Exception $e) {
            $this->telegram->sendMessageToTeam("(로그 확인 요망)모니터 크기 정보 처리 중 오류 발생: $productName");
            SimpleLogService::error('req', "모니터 크기 정보 처리 중 오류 발생", [
                'product_name' => $productName,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ], $e);
            return null;
        }
    }
}

