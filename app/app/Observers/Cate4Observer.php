<?php

namespace App\Observers;

use App\Models\Cate4;
use App\Services\CategoryService;
use App\Services\EventManager;
use Illuminate\Support\Facades\Log;
use Exception;

class Cate4Observer
{
    protected CategoryService $categoryService;
    protected EventManager $eventManager;

    public function __construct(
        CategoryService $categoryService,
        EventManager $eventManager
    ) {
        $this->categoryService = $categoryService;
        $this->eventManager = $eventManager;
    }

    /**
     * Cate4 모델이 생성된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function created(Cate4 $cate4): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리
            $this->eventManager->handleModelEvent('created', $cate4);
            
            Log::info('Cate4 생성 이벤트 처리 완료', [
                'cate4_id' => $cate4->id,
                'cate4_name' => $cate4->name
            ]);

        } catch (Exception $e) {
            Log::error('Cate4 생성 이벤트 처리 실패', [
                'cate4_id' => $cate4->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cate4 모델이 업데이트된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function updated(Cate4 $cate4): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리
            $this->eventManager->handleModelEvent('updated', $cate4);
            
            Log::info('Cate4 업데이트 이벤트 처리 완료', [
                'cate4_id' => $cate4->id,
                'cate4_name' => $cate4->name,
                'changes' => $cate4->getChanges()
            ]);

        } catch (Exception $e) {
            Log::error('Cate4 업데이트 이벤트 처리 실패', [
                'cate4_id' => $cate4->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cate4 모델이 삭제된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function deleted(Cate4 $cate4): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리
            $this->eventManager->handleModelEvent('deleted', $cate4);
            
            Log::info('Cate4 삭제 이벤트 처리 완료', [
                'cate4_id' => $cate4->id,
                'cate4_name' => $cate4->name
            ]);

        } catch (Exception $e) {
            Log::error('Cate4 삭제 이벤트 처리 실패', [
                'cate4_id' => $cate4->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cate4 모델이 복원된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function restored(Cate4 $cate4): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리 (복원은 생성으로 처리)
            $this->eventManager->handleModelEvent('created', $cate4);
            
            Log::info('Cate4 복원 이벤트 처리 완료', [
                'cate4_id' => $cate4->id,
                'cate4_name' => $cate4->name
            ]);

        } catch (Exception $e) {
            Log::error('Cate4 복원 이벤트 처리 실패', [
                'cate4_id' => $cate4->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
