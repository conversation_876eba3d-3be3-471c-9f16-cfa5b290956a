<?php

namespace App\Observers;

use App\Models\Cate5;
use App\Services\CategoryService;
use App\Services\EventManager;
use Illuminate\Support\Facades\Log;
use Exception;

class Cate5Observer
{
    protected CategoryService $categoryService;
    protected EventManager $eventManager;

    public function __construct(
        CategoryService $categoryService,
        EventManager $eventManager
    ) {
        $this->categoryService = $categoryService;
        $this->eventManager = $eventManager;
    }

    /**
     * Cate5 모델이 생성된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function created(Cate5 $cate5): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리
            $this->eventManager->handleModelEvent('created', $cate5);
            
            Log::info('Cate5 생성 이벤트 처리 완료', [
                'cate5_id' => $cate5->id,
                'cate5_name' => $cate5->name,
                'cate4_id' => $cate5->cate4_id
            ]);

        } catch (Exception $e) {
            Log::error('Cate5 생성 이벤트 처리 실패', [
                'cate5_id' => $cate5->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cate5 모델이 업데이트된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function updated(Cate5 $cate5): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리
            $this->eventManager->handleModelEvent('updated', $cate5);
            
            Log::info('Cate5 업데이트 이벤트 처리 완료', [
                'cate5_id' => $cate5->id,
                'cate5_name' => $cate5->name,
                'cate4_id' => $cate5->cate4_id,
                'changes' => $cate5->getChanges()
            ]);

        } catch (Exception $e) {
            Log::error('Cate5 업데이트 이벤트 처리 실패', [
                'cate5_id' => $cate5->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cate5 모델이 삭제된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function deleted(Cate5 $cate5): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리
            $this->eventManager->handleModelEvent('deleted', $cate5);
            
            Log::info('Cate5 삭제 이벤트 처리 완료', [
                'cate5_id' => $cate5->id,
                'cate5_name' => $cate5->name,
                'cate4_id' => $cate5->cate4_id
            ]);

        } catch (Exception $e) {
            Log::error('Cate5 삭제 이벤트 처리 실패', [
                'cate5_id' => $cate5->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cate5 모델이 복원된 후 캐시 무효화 및 SSE 이벤트 처리
     */
    public function restored(Cate5 $cate5): void
    {
        try {
            // 기존 캐시 무효화
            $this->categoryService->flushCategoryCache();
            
            // SSE 이벤트 처리 (복원은 생성으로 처리)
            $this->eventManager->handleModelEvent('created', $cate5);
            
            Log::info('Cate5 복원 이벤트 처리 완료', [
                'cate5_id' => $cate5->id,
                'cate5_name' => $cate5->name,
                'cate4_id' => $cate5->cate4_id
            ]);

        } catch (Exception $e) {
            Log::error('Cate5 복원 이벤트 처리 실패', [
                'cate5_id' => $cate5->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
