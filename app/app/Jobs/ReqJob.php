<?php

namespace App\Jobs;

use App\Events\ReqFinishNotification;
use App\Imports\ProductsImport;
use App\Models\Req;
use App\Models\User;
use App\Services\SimpleLogService;
use App\Services\TelegramService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use Throwable;
use App\Exceptions\ValidationException;

/**
 * Class ReqJob
 *
 * This class represents a job for handling reqs.
 * It implements the ShouldQueue interface, indicating that it can be queued for asynchronous processing.
 *
 * @package App\Jobs
 */
class ReqJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var int 실패시 최대 재시도 횟수 */
    public int $tries = 1;

    /** @var int 최대 예외 횟수 */
    public int $maxExceptions = 1;

    /** @var int 최대 실행 시간(30분) */
    public int $timeout = 1800;

    /** @var int 메모리 제한 (MB) */
    protected int $memoryLimit = 512;

    protected Req $req;
    protected User $user;
    protected string $path;
    protected string $filename;

    /** @var string 엑셀파일이 저장되어 있는 위치(public 디렉토리 기준 - 링크) */
    protected string $excelPath;

    /** @var string|null Redirect URL */
    protected string|null $redirect = null;

    /** @var int 제목행 위치 */
    protected int $startRow;

    protected TelegramService $telegram;

    /** @var array 허용된 시트 이름 */
    protected array $allowedSheets = ['작업요청리스트', 'Sheet1', 'RAW', '시트1'];

    /**
     * Create a new job instance.
     */
    public function __construct(Req $req, User $user, string $path, string $filename, string|null $redirect, int $startRow = 3)
    {
        $this->req = $req;
        $this->user = $user;
        $this->path = $path;
        $this->filename = $filename;
        $this->excelPath = storage_path('app/' . $path);
        $this->redirect = $redirect;
        $this->startRow = $startRow;
        $this->telegram = new TelegramService();
    }

    /**
     * 작업의 고유 ID를 생성하여 반환
     *
     * @return string
     */
    public function uniqueId(): string
    {
        // 요청 ID, 파일명, 현재 타임스탬프를 조합하여 고유 ID 생성
        // 이렇게 하면 같은 파일을 다시 업로드하더라도 타임스탬프가 다르므로 다른 작업으로 인식됨
        return 'req_job_' . $this->req->id . '_' . md5($this->path) . '_' . now()->timestamp;
    }

    /**
     * 엑셀 파일의 헤더 정보를 구하는 메서드
     *
     * @param string $excelPath 엑셀 파일 경로
     * @param string $sheetName 시트 이름
     * @param int $headerRow 헤더 행 번호 (기본값: 1)
     * @return array 헤더 정보 배열
     */
    private function getHeaderInfo(string $excelPath, string $sheetName, int $headerRow = 1): array
    {
        try {
            $reader = new Xlsx();
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($excelPath);

            // 지정된 시트 가져오기
            $worksheet = $spreadsheet->getSheetByName($sheetName);
            if (!$worksheet) {
                throw new Exception("시트 '{$sheetName}'을 찾을 수 없습니다.");
            }

            // 헤더 행 데이터 가져오기
            $headerData = $worksheet->rangeToArray("A{$headerRow}:Z{$headerRow}", null, true, false);
            $headers = array_filter($headerData[0], function($value) {
                return !empty(trim($value));
            });

            return array_values($headers);
        } catch (Exception $e) {
            SimpleLogService::error('req', "헤더 정보 구하기 실패", [
                'excel_path' => $excelPath,
                'sheet_name' => $sheetName,
                'header_row' => $headerRow
            ], $e);

            // 오류 발생 시 빈 배열 반환
            return [];
        }
    }

    /**
     * 엑셀 파일의 마지막 행 번호를 구하는 메서드 (성능 최적화 버전)
     *
     * @param string $excelPath 엑셀 파일 경로
     * @param string $sheetName 시트 이름
     * @return int 마지막 행 번호
     */
    private function getLastRowNumber(string $excelPath, string $sheetName): int
    {
        try {
            $reader = new Xlsx();
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($excelPath);

            // 지정된 시트 가져오기
            $worksheet = $spreadsheet->getSheetByName($sheetName);
            if (!$worksheet) {
                $message = "입고등록 오류: [{$this->req->id}] 유효한 시트를 찾을 수 없습니다. 허용된 시트 이름: " . implode(', ', $this->allowedSheets);
                $this->telegram->sendMessageToTeam($message, 'req');
                throw new Exception($message);
            }

            // 마지막 행 번호 구하기
            $lastRow = $worksheet->getHighestRow();

            // 성능 최적화: 이진 탐색으로 실제 데이터가 있는 마지막 행 찾기
            $actualLastRow = $this->findLastDataRow($worksheet, $lastRow);

            return $actualLastRow;
        } catch (Exception $e) {
            SimpleLogService::error('req', "마지막 행 구하기 실패", [
                'excel_path' => $excelPath,
                'sheet_name' => $sheetName
            ], $e);

            // 오류 발생 시 기본값 반환
            return 0;
        }
    }

    /**
     * 이진 탐색으로 실제 데이터가 있는 마지막 행을 찾는 메서드
     *
     * @param Worksheet $worksheet 워크시트
     * @param int $lastRow 마지막 행 번호
     * @return int 실제 데이터가 있는 마지막 행 번호
     */
    private function findLastDataRow(Worksheet $worksheet, int $lastRow): int
    {
        $left = 1;
        $right = $lastRow;
        $result = 0;

        while ($left <= $right) {
            $mid = (int)(($left + $right) / 2);

            // 중간 행의 데이터 확인
            $rowData = $worksheet->rangeToArray("A{$mid}:Z{$mid}", null, true, false);
            $hasData = false;

            foreach ($rowData[0] as $cellValue) {
                if (!empty(trim($cellValue))) {
                    $hasData = true;
                    break;
                }
            }

            if ($hasData) {
                // 데이터가 있으면 더 큰 범위에서 찾기
                $result = $mid;
                $left = $mid + 1;
            } else {
                // 데이터가 없으면 더 작은 범위에서 찾기
                $right = $mid - 1;
            }
        }

        return $result;
    }

    /**
     * [전처리] RG/구분 컬럼에서 '회송' 포함 여부 확인 후 발견 시 업로드 중단
     *
     * @throws ValidationException|\PhpOffice\PhpSpreadsheet\Calculation\Exception
     */
    private function abortIfContainsBannedRg(Worksheet $worksheet, array $headers, int $startRow, int $lastRow): void
    {
        // excel-headers 설정에서 rg 필드의 가능한 헤더명들을 이용해 인덱스 찾기
        $rgPossibleHeaders = config('excel-headers.mappings.optional.rg')
            ?? config('excel-headers.mappings.required.rg')
            ?? ['구분', 'RG', 'rg'];

        $rgIndex = null;
        foreach ($rgPossibleHeaders as $h) {
            $idx = array_search($h, $headers, true);
            if ($idx !== false) {
                $rgIndex = $idx; // 0-based
                break;
            }
        }

        // RG 헤더를 못 찾으면 검사 생략(업로드 계속)
        if ($rgIndex === null) {
            SimpleLogService::info('req', "[전처리] RG 헤더를 찾지 못해 '회송' 검사 생략", [
                'headers' => $headers,
                'rg_possible_headers' => $rgPossibleHeaders
            ]);
            return;
        }

        // 컬럼 인덱스를 엑셀 컬럼 문자로 변환
        $columnLetter = Coordinate::stringFromColumnIndex($rgIndex + 1);

        $foundRows = [];
        for ($row = $startRow; $row <= $lastRow; $row++) {
            $cellValue = (string)$worksheet->getCell("{$columnLetter}{$row}")->getCalculatedValue();
            if ($cellValue !== '' && mb_stripos($cellValue, '회송') !== false) {
                $foundRows[] = $row; // 실제 엑셀 행 번호
            }
        }

        if (!empty($foundRows)) {
            $message = "엑셀의 '구분(RG)' 컬럼에 '회송' 문구가 포함된 행이 있어 등록을 중단했습니다.<br>"
                . "문구를 제거한 뒤 다시 업로드해 주세요.<br>"
                . "문제 행: " . implode(', ', $foundRows) . "행";
            SimpleLogService::warning('req', "[전처리] 회송 행 발견으로 업로드 중단", [
                'rows' => $foundRows
            ]);

            // 422로 전파되어 클라이언트 경고에 활용 가능
            throw new ValidationException($message, ['rows' => $foundRows]);
        }
    }

    /**
     * Execute the job.
     * @throws Exception|Throwable
     */
    public function handle(): void
    {
        // 대용량 파일 처리를 위한 메모리 및 실행 시간 설정
        ini_set('memory_limit', $this->memoryLimit . 'M');
        set_time_limit($this->timeout);

        // 등록중 오류 발생 시 삭제가 안 된 Redis 키를 다시 삭제
        try {
            Redis::connection(0)->command('del', [
                'req_import:chunk_offset',
                "req_import:duplicated_qaids:{$this->req->id}"
            ]);
        } catch (Exception $e) {
            SimpleLogService::warning('req', 'Redis 키 정리 중 오류', [], $e);
        }

        // 데이터베이스 등록 시작을 알리는 알림을 클라이언트로 보낸다.j
        // event(new ReqStartNotification('입고상품 등록을 시작 합니다.<br>등록하는 동안 시스템이 느려질 수 있습니다.'));
        SimpleLogService::info('req', str_repeat("=", 100));
        SimpleLogService::info('req', "[{$this->req->id}]번 입력시작(" .  date("Y-m-d H:i:s") . ")");

        try {
            // 엑셀 파일의 모든 시트 이름 가져오기
            $reader = new Xlsx();
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($this->excelPath);
            $allSheetNames = $spreadsheet->getSheetNames();

            // 허용된 시트 이름 목록과 일치하는 시트 찾기
            $sheetsToProcess = array_intersect($allSheetNames, $this->allowedSheets);
            if (empty($sheetsToProcess)) {
                // 허용된 시트 이름이 파일에 없는 경우 에러 로그 남기고 작업 중단
                $errorMessage = "입고등록 오류: [{$this->req->id}] 유효한 시트를 찾을 수 없습니다. 허용된 시트 이름: " . implode(', ', $this->allowedSheets);
                $this->telegram->sendMessageToTeam($errorMessage, 'req');

                throw new Exception($errorMessage);
            }

            // 처리할 시트가 발견된 경우
            $sheetName = reset($sheetsToProcess); // 첫 번째 일치하는 시트 사용
            $worksheet = $spreadsheet->getSheetByName($sheetName);

            // 마지막 행 번호 구하기
            $lastRowNumber = $this->getLastRowNumber($this->excelPath, $sheetName);

            // 헤더 정보 구하기 (시작행 - 1 행에서 헤더 확인)
            $headerRow = $this->startRow - 1;
            $headers = $this->getHeaderInfo($this->excelPath, $sheetName, $headerRow);

            // 처리할 데이터 행 수 계산 (시작행부터 마지막행까지 포함)
            // 예: 시작행 3, 마지막행 1500 → 3,4,5,...,1500 = 1498개 행
            $dataRowCount = $lastRowNumber - $this->startRow + 1;

            // 엑셀 파일 정보 로그 출력
            SimpleLogService::info('req', "[{$this->req->id}] 엑셀 파일 정보", [
                'sheet_name' => $sheetName,
                'start_row' => $this->startRow,
                'last_row' => $lastRowNumber,
                'total_data_rows' => $dataRowCount,
                'file_size' => filesize($this->excelPath) . ' bytes',
                'headers' => $headers,
                'memory_limit' => $this->memoryLimit . 'M',
                'timeout' => $this->timeout . '초'
            ]);

            // [전처리] '구분(RG)' 컬럼에서 '회송' 포함 여부 검사 → 발견 시 업로드 중단
            $this->abortIfContainsBannedRg($worksheet, $headers, $this->startRow, $lastRowNumber);

            // 입고요청 시작 로그 등록
            $now = date("Y-m-d H:i:s");
            $excelInfo = "📊 [$now]입고 요청서\n";
            $excelInfo .= "🖥️ 엑셀 파일 정보\n";
            $excelInfo .= "시트: {$sheetName}\n";
            $excelInfo .= "시작행: {$this->startRow}\n";
            $excelInfo .= "마지막행: {$lastRowNumber}\n";
            $excelInfo .= "처리할 데이터: {$dataRowCount}행\n";
            $excelInfo .= "헤더 정보: " . implode(', ', $headers);
            $this->telegram->sendMessageToTeam($excelInfo);
            $this->telegram->sendDocumentToTeam($this->path, $this->filename, "{$this->req->id}번 입고요청 엑셀 파일");

            // ProductsImport 객체 생성 시 마지막 행 정보 전달
            $import = new ProductsImport($this->req, $this->user, $this->excelPath, $this->redirect, $this->startRow, $lastRowNumber);

            // 헤더 정보를 ProductsImport에 전달
            $import->setHeaders($headers);

            try {
                Excel::import($import, $this->excelPath);
                SimpleLogService::info('req', "[{$this->req->id}] Excel::import 완료");
            } catch (Exception $e) {
                SimpleLogService::error('req', "[{$this->req->id}] Excel::import 실행 중 오류 발생", [
                    'excel_path' => $this->excelPath,
                    'error_message' => $e->getMessage(),
                    'error_trace' => $e->getTraceAsString()
                ], $e);
                throw $e;
            }

            SimpleLogService::info('req', "[{$this->req->id}] 시트 '{$sheetName}' 처리 완료");
        } catch (Throwable $e) {
            // Job을 실패 처리
            $this->fail($e);
        }
    }

    /**
     * 실패한 Job 처리 로직
     */
    public function failed(Throwable $e): void
    {
        $errorMessage = "[{$this->req->id}] 요청서 입력중 오류 발생";

        // 상세한 에러 로깅
        SimpleLogService::critical('req', $errorMessage, [
            'req_id' => $this->req->id,
            'user_id' => $this->user->id ?? 'N/A',
            'excel_path' => $this->excelPath,
            'filename' => $this->filename,
            'start_row' => $this->startRow,
            'error_message' => $e->getMessage(),
            'error_class' => get_class($e),
            'error_code' => $e->getCode(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'error_trace' => $e->getTraceAsString(),
            'job_attempts' => $this->attempts(),
            'max_attempts' => $this->tries,
            'job_timeout' => $this->timeout,
            'memory_limit' => $this->memoryLimit
        ], $e);

        event(new ReqFinishNotification($e->getMessage()));

        // 텔레그램으로 에러 알림 전송
        $this->telegram->sendMessageToTeam($errorMessage);
    }
}
