<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

/**
 * SSE 연결 관리 서비스
 * 
 * Redis를 사용하여 클라이언트 연결 상태를 관리하고
 * 사용자별 연결 매핑을 처리합니다.
 */
class ConnectionManager
{
    private const CONNECTION_PREFIX = 'sse:connections:';
    private const USER_CONNECTIONS_PREFIX = 'sse:user_connections:';
    private const ACTIVE_CONNECTIONS_KEY = 'sse:active_connections';
    private const CONNECTION_TTL = 3600; // 1시간
    private const HEARTBEAT_INTERVAL = 30; // 30초

    /**
     * 새로운 연결을 추가합니다.
     *
     * @param string $connectionId 고유 연결 ID
     * @param int|null $userId 사용자 ID (게스트인 경우 null)
     * @param array $metadata 추가 연결 정보
     * @return void
     * @throws Exception Redis 연결 실패 시
     */
    public function addConnection(string $connectionId, ?int $userId = null, array $metadata = []): void
    {
        try {
            $connectionData = [
                'connection_id' => $connectionId,
                'user_id' => $userId,
                'ip_address' => $metadata['ip_address'] ?? null,
                'user_agent' => $metadata['user_agent'] ?? null,
                'connected_at' => Carbon::now()->toISOString(),
                'last_heartbeat' => Carbon::now()->toISOString(),
                'is_authenticated' => $userId !== null,
                'subscriptions' => $metadata['subscriptions'] ?? ['notifications']
            ];

            // 연결 정보를 Redis에 저장
            $this->storeConnectionInRedis($connectionId, $connectionData);

            // 활성 연결 집합에 추가
            Redis::sadd(self::ACTIVE_CONNECTIONS_KEY, $connectionId);

            // 사용자별 연결 매핑 (인증된 사용자만)
            if ($userId !== null) {
                $userConnectionsKey = self::USER_CONNECTIONS_PREFIX . $userId;
                Redis::sadd($userConnectionsKey, $connectionId);
                Redis::expire($userConnectionsKey, self::CONNECTION_TTL);
            }

            Log::info('SSE 연결이 추가되었습니다', [
                'connection_id' => $connectionId,
                'user_id' => $userId,
                'ip_address' => $metadata['ip_address'] ?? 'unknown'
            ]);

        } catch (Exception $e) {
            Log::error('SSE 연결 추가 실패', [
                'connection_id' => $connectionId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 연결을 제거합니다.
     *
     * @param string $connectionId 연결 ID
     * @return void
     */
    public function removeConnection(string $connectionId): void
    {
        try {
            // 연결 정보 조회
            $connectionData = $this->getConnection($connectionId);
            
            if ($connectionData) {
                $userId = $connectionData['user_id'] ?? null;

                // 사용자별 연결에서 제거
                if ($userId) {
                    $userConnectionsKey = self::USER_CONNECTIONS_PREFIX . $userId;
                    Redis::srem($userConnectionsKey, $connectionId);
                }

                Log::info('SSE 연결이 제거되었습니다', [
                    'connection_id' => $connectionId,
                    'user_id' => $userId
                ]);
            }

            // Redis에서 연결 정보 제거
            $this->removeConnectionFromRedis($connectionId);

            // 활성 연결 집합에서 제거
            Redis::srem(self::ACTIVE_CONNECTIONS_KEY, $connectionId);

        } catch (Exception $e) {
            Log::error('SSE 연결 제거 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 모든 활성 연결 목록을 반환합니다.
     *
     * @return array 활성 연결 ID 배열
     */
    public function getActiveConnections(): array
    {
        try {
            return Redis::smembers(self::ACTIVE_CONNECTIONS_KEY) ?: [];
        } catch (Exception $e) {
            Log::error('활성 연결 조회 실패', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 특정 사용자의 모든 연결을 반환합니다.
     *
     * @param int $userId 사용자 ID
     * @return array 사용자의 연결 ID 배열
     */
    public function getUserConnections(int $userId): array
    {
        try {
            $userConnectionsKey = self::USER_CONNECTIONS_PREFIX . $userId;
            return Redis::smembers($userConnectionsKey) ?: [];
        } catch (Exception $e) {
            Log::error('사용자 연결 조회 실패', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 사용자가 온라인 상태인지 확인합니다.
     *
     * @param int $userId 사용자 ID
     * @return bool 온라인 여부
     */
    public function isUserOnline(int $userId): bool
    {
        $connections = $this->getUserConnections($userId);
        return !empty($connections);
    }

    /**
     * 특정 연결의 정보를 반환합니다.
     *
     * @param string $connectionId 연결 ID
     * @return array|null 연결 정보 또는 null
     */
    public function getConnection(string $connectionId): ?array
    {
        try {
            $connectionKey = self::CONNECTION_PREFIX . $connectionId;
            $data = Redis::get($connectionKey);
            
            return $data ? json_decode($data, true) : null;
        } catch (Exception $e) {
            Log::error('연결 정보 조회 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 연결의 하트비트를 업데이트합니다.
     *
     * @param string $connectionId 연결 ID
     * @return bool 업데이트 성공 여부
     */
    public function updateHeartbeat(string $connectionId): bool
    {
        try {
            $connectionData = $this->getConnection($connectionId);
            
            if (!$connectionData) {
                return false;
            }

            $connectionData['last_heartbeat'] = Carbon::now()->toISOString();
            $this->storeConnectionInRedis($connectionId, $connectionData);

            return true;
        } catch (Exception $e) {
            Log::error('하트비트 업데이트 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 만료된 연결들을 정리합니다.
     *
     * @return int 정리된 연결 수
     */
    public function cleanupExpiredConnections(): int
    {
        $cleanedCount = 0;
        $activeConnections = $this->getActiveConnections();
        $cutoffTime = Carbon::now()->subSeconds(self::HEARTBEAT_INTERVAL * 3);

        foreach ($activeConnections as $connectionId) {
            $connectionData = $this->getConnection($connectionId);
            
            if (!$connectionData) {
                $this->removeConnection($connectionId);
                $cleanedCount++;
                continue;
            }

            $lastHeartbeat = Carbon::parse($connectionData['last_heartbeat']);
            
            if ($lastHeartbeat->lt($cutoffTime)) {
                $this->removeConnection($connectionId);
                $cleanedCount++;
                
                Log::info('만료된 SSE 연결이 정리되었습니다', [
                    'connection_id' => $connectionId,
                    'last_heartbeat' => $lastHeartbeat->toISOString()
                ]);
            }
        }

        if ($cleanedCount > 0) {
            Log::info('SSE 연결 정리 완료', ['cleaned_count' => $cleanedCount]);
        }

        return $cleanedCount;
    }

    /**
     * 연결 통계를 반환합니다.
     *
     * @return array 연결 통계 정보
     */
    public function getConnectionStats(): array
    {
        $activeConnections = $this->getActiveConnections();
        $authenticatedCount = 0;
        $guestCount = 0;

        foreach ($activeConnections as $connectionId) {
            $connectionData = $this->getConnection($connectionId);
            
            if ($connectionData) {
                if ($connectionData['is_authenticated']) {
                    $authenticatedCount++;
                } else {
                    $guestCount++;
                }
            }
        }

        return [
            'total_connections' => count($activeConnections),
            'authenticated_connections' => $authenticatedCount,
            'guest_connections' => $guestCount,
            'timestamp' => Carbon::now()->toISOString()
        ];
    }

    /**
     * 연결 정보를 Redis에 저장합니다.
     *
     * @param string $connectionId 연결 ID
     * @param array $data 연결 데이터
     * @return void
     * @throws Exception Redis 저장 실패 시
     */
    private function storeConnectionInRedis(string $connectionId, array $data): void
    {
        $connectionKey = self::CONNECTION_PREFIX . $connectionId;
        $jsonData = json_encode($data);
        
        if ($jsonData === false) {
            throw new Exception('연결 데이터 JSON 인코딩 실패');
        }

        Redis::setex($connectionKey, self::CONNECTION_TTL, $jsonData);
    }

    /**
     * Redis에서 연결 정보를 제거합니다.
     *
     * @param string $connectionId 연결 ID
     * @return void
     */
    private function removeConnectionFromRedis(string $connectionId): void
    {
        $connectionKey = self::CONNECTION_PREFIX . $connectionId;
        Redis::del($connectionKey);
    }
}