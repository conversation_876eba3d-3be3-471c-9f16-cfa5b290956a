<?php

namespace App\Services;

use App\Models\Product;
use App\Models\MonitorRule;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

/**
 * 모니터 크기 추출 서비스
 *
 * 기존 MonitorTrait의 로직을 서비스로 이전하고 데이터베이스 기반 규칙 관리로 개선
 */
class MonitorSizeExtractionService
{
    /**
     * 텔레그램 서비스
     */
    protected TelegramService $telegramService;

    /**
     * 기본 크기 (추출 실패 시 사용)
     */
    const DEFAULT_SIZE = 0;

    /**
     * 기본 단위 (추출 실패 시 사용)
     */
    const DEFAULT_UNIT = 'INCH';

    /**
     * 패턴에서 제외할 키워드들
     */
    private const PATTERN_EXCLUDE_KEYWORDS = [
        'Hz', 'HDR', 'K', 'UHD', 'FHD', 'QHD', 'OLED', 'IPS', 'VA', 'TN',
        'Gaming', 'Curved', 'Ultra', 'Wide', 'Pro', 'Plus', 'Max',
        'PD', 'USB', 'HDMI', 'DP', 'VGA', 'DVI', 'W'
    ];

    /**
     * 캐시 키 접두사
     */
    const CACHE_PREFIX = 'monitor_rules_';

    /**
     * 제외 키워드 패턴 생성
     */
    private function getExcludePattern(): string
    {
        $keywords = array_map('preg_quote', self::PATTERN_EXCLUDE_KEYWORDS);
        return implode('|', $keywords);
    }

    /**
     * 캐시 만료 시간 (분)
     */
    const CACHE_TTL = 60;

    public function __construct(TelegramService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    /**
     * 모니터 제품인지 확인
     *
     * @param Product $product
     * @return bool
     */
    public function isMonitorProduct(Product $product): bool
    {
        if (!$product->cate4 || !$product->cate5) {
            return false;
        }

        return ($product->cate4->name === 'TV' && $product->cate5->name === 'TV') ||
               ($product->cate4->name === '컴퓨터주변기기' && $product->cate5->name === '모니터');
    }

    /**
     * 모니터 수리비 모델명 결정
     * - 삼성, LG: brand
     * - 그 외: general
     *
     * @param Product $product
     * @return string
     */
    public function getMonitorModel(Product $product): string
    {
        $brandRules = $this->getBrandRules();
        $excludeRules = $this->getExcludeRules();

        $model = 'general';

        // 브랜드 키워드 확인
        foreach ($brandRules as $rule) {
            if (stripos($product->name, $rule->pattern) !== false) {
                $model = 'brand';

                // 제외 키워드 확인
                foreach ($excludeRules as $excludeRule) {
                    if (stripos($product->name, $excludeRule->pattern) !== false) {
                        $model = 'general';
                        break 2;
                    }
                }
                break;
            }
        }

        return $model;
    }

    /**
     * 상품명에서 크기 정보 추출
     *
     * @param Product $product
     * @return array|null ['size' => float|int, 'unit' => string] 또는 null
     */
    public function extractSizeFromName(Product $product): ?array
    {
        try {
            $productName = $product->name;
            $defaultUnit = self::DEFAULT_UNIT;

            SimpleLogService::debug('monitor', '모니터 크기 추출 시작', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'product_name' => $productName
            ]);

            // 데이터베이스 기반 크기 패턴 규칙 적용 (우선순위 순으로 처리 - 숫자가 낮을수록 우선)
            $sizePatternRules = $this->getSizePatternRules();
            $bestMatch = null;
            $bestPriority = PHP_INT_MAX;

            SimpleLogService::debug('monitor', '크기 패턴 규칙 적용', [
                'product_id' => $product->id,
                'rules_count' => $sizePatternRules->count()
            ]);

            foreach ($sizePatternRules as $rule) {
                if (preg_match($rule->pattern, $productName, $matches)) {
                    SimpleLogService::debug('monitor', '크기 패턴 매칭됨', [
                        'product_id' => $product->id,
                        'rule_id' => $rule->id,
                        'rule_pattern' => $rule->pattern,
                        'rule_priority' => $rule->priority,
                        'matches' => $matches,
                        'current_best_priority' => $bestPriority
                    ]);

                    // 숫자가 더 낮은(우선순위가 더 높은) 규칙이 매칭되면 업데이트
                    if ($rule->priority < $bestPriority) {
                        $bestPriority = $rule->priority;
                        $bestMatch = $this->processSizeMatch($rule, $matches, $defaultUnit);

                        SimpleLogService::debug('monitor', '더 높은(숫자 낮은) 우선순위 규칙으로 업데이트', [
                            'product_id' => $product->id,
                            'new_best_priority' => $bestPriority,
                            'best_match' => $bestMatch
                        ]);
                    }
                }
            }

            // 최적의 매칭 결과 반환
            if ($bestMatch) {
                SimpleLogService::info('monitor', '모니터 크기 추출 성공', [
                    'product_id' => $product->id,
                    'qaid' => $product->qaid,
                    'product_name' => $productName,
                    'extracted_size' => $bestMatch,
                    'best_priority' => $bestPriority
                ]);
                return $bestMatch;
            }

            // 크기 추출 실패 시 로그만 기록 (텔레그램 메시지 제거)
            SimpleLogService::warning('monitor', '모니터 크기 추출 실패 - 기본값 사용', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'product_name' => $productName,
                'rules_count' => $sizePatternRules->count(),
                'best_priority' => $bestPriority
            ]);

            return [
                'size' => self::DEFAULT_SIZE,
                'unit' => $defaultUnit
            ];

        } catch (Exception $e) {
            SimpleLogService::error('monitor', '모니터 크기 추출 중 오류 발생', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'product_name' => $product->name,
                'error' => $e->getMessage()
            ], $e);

            return [
                'size' => self::DEFAULT_SIZE,
                'unit' => self::DEFAULT_UNIT
            ];
        }
    }

    /**
     * 브랜드 규칙 조회 (캐시 사용)
     *
     * @return Collection
     */
    public function getBrandRules(): Collection
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'brand',
            self::CACHE_TTL,
            fn() => MonitorRule::getBrandRules()
        );
    }

    /**
     * 제외 규칙 조회 (캐시 사용)
     *
     * @return Collection
     */
    public function getExcludeRules(): Collection
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'exclude',
            self::CACHE_TTL,
            fn() => MonitorRule::getExcludeRules()
        );
    }

    /**
     * 크기 패턴 규칙 조회 (캐시 사용)
     *
     * @return Collection
     */
    public function getSizePatternRules(): Collection
    {
        return Cache::remember(
            self::CACHE_PREFIX . 'size_pattern',
            self::CACHE_TTL,
            fn() => MonitorRule::getSizePatternRules()
        );
    }

    /**
     * 규칙 캐시 초기화
     *
     * @return void
     */
    public function clearRulesCache(): void
    {
        Cache::forget(self::CACHE_PREFIX . 'brand');
        Cache::forget(self::CACHE_PREFIX . 'exclude');
        Cache::forget(self::CACHE_PREFIX . 'size_pattern');
    }

    /**
     * 크기 매칭 결과 처리
     *
     * @param MonitorRule $rule
     * @param array $matches
     * @param string $defaultUnit
     * @return array|null
     */
    protected function processSizeMatch(MonitorRule $rule, array $matches, string $defaultUnit): ?array
    {
        // 직접적인 단위 패턴 처리 (최우선)
        if (str_contains($rule->description, '직접적인')) {
            $size = isset($matches[1]) ? floatval($matches[1]) : null;
            if ($size) {
                $unit = str_contains($rule->description, 'cm') ? 'CM' : $defaultUnit;
                return [
                    'size' => $size,
                    'unit' => $unit
                ];
            }
        }

        // CM 단위 패턴 처리
        if (str_contains($rule->description, 'cm')) {
            $size = isset($matches[1]) ? floatval($matches[1]) : null;
            if ($size && $size >= 20 && $size <= 250) {
                return [
                    'size' => $size,
                    'unit' => 'CM'
                ];
            }
        }

        // 일반적인 숫자 패턴 처리
        $size = isset($matches[1]) ? floatval($matches[1]) : null;
        if ($size && $size >= 8 && $size <= 100) {
            return [
                'size' => $size,
                'unit' => $defaultUnit
            ];
        }

        return null;
    }

    /**
     * 크기 추출 테스트 (관리자용)
     *
     * @param string $productName
     * @return array
     */
    public function testExtraction(string $productName): array
    {
        try {
            // 임시 Product 객체 생성 (최소한의 속성만 설정)
            $tempProduct = new Product();
            $tempProduct->name = $productName;
            $tempProduct->qaid = 'TEST';

            // 기본 카테고리 정보 설정 (모니터로 가정)
            $tempProduct->cate4 = (object)['name' => 'TV'];
            $tempProduct->cate5 = (object)['name' => 'TV'];

            $result = $this->extractSizeFromName($tempProduct);

            return [
                'product_name' => $productName,
                'extracted_size' => $result,
                'applied_rules' => $this->getAppliedRules($productName)
            ];
        } catch (Exception $e) {
            SimpleLogService::error('monitor', '크기 추출 테스트 실패', [
                'product_name' => $productName,
                'error' => $e->getMessage()
            ], $e);

            return [
                'product_name' => $productName,
                'extracted_size' => null,
                'applied_rules' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 적용된 규칙 정보 반환 (디버깅용)
     *
     * @param string $productName
     * @return array
     */
    protected function getAppliedRules(string $productName): array
    {
        $appliedRules = [];

        // 브랜드 규칙 확인
        foreach ($this->getBrandRules() as $rule) {
            if (stripos($productName, $rule->pattern) !== false) {
                $appliedRules[] = [
                    'type' => 'brand',
                    'pattern' => $rule->pattern,
                    'description' => $rule->description,
                    'priority' => $rule->priority
                ];
            }
        }

        // 제외 규칙 확인
        foreach ($this->getExcludeRules() as $rule) {
            if (stripos($productName, $rule->pattern) !== false) {
                $appliedRules[] = [
                    'type' => 'exclude',
                    'pattern' => $rule->pattern,
                    'description' => $rule->description,
                    'priority' => $rule->priority
                ];
            }
        }

        // 크기 패턴 규칙 확인 (우선순위 순으로)
        $sizeMatches = [];
        foreach ($this->getSizePatternRules() as $rule) {
            if (preg_match($rule->pattern, $productName, $matches)) {
                $sizeMatches[] = [
                    'type' => 'size_pattern',
                    'pattern' => $rule->pattern,
                    'description' => $rule->description,
                    'priority' => $rule->priority,
                    'matches' => $matches
                ];
            }
        }

        // 크기 패턴은 우선순위 순으로 정렬 (숫자가 낮을수록 우선)
        usort($sizeMatches, function($a, $b) {
            return $a['priority'] <=> $b['priority']; // 낮은 우선순위 숫자(더 높은 우선) 먼저
        });

        return array_merge($appliedRules, $sizeMatches);
    }

    /**
     * 특정 패턴 테스트 (디버깅용)
     *
     * @param string $productName
     * @return array
     */
    public function testPattern(string $productName): array
    {
        $results = [];

        // 직접적인 단위 패턴 테스트 (최우선)
        if (preg_match('/(\d{2,3}(?:\.\d+)?)(?:cm|센치|센티미터)/iu', $productName, $matches)) {
            $results['direct_cm_pattern'] = [
                'matched' => true,
                'size' => floatval($matches[1]),
                'full_match' => $matches[0],
                'unit' => 'CM'
            ];
        }

        if (preg_match('/(\d{1,3}(?:\.\d+)?)\s?(?:inch|인치|형)/iu', $productName, $matches)) {
            $results['direct_inch_pattern'] = [
                'matched' => true,
                'size' => floatval($matches[1]),
                'full_match' => $matches[0],
                'unit' => 'INCH'
            ];
        }

        // TFG32U14PQ 형식 테스트 (3글자 알파벳 + 숫자 + 2글자 이상)
        if (preg_match('/\b[A-Z]{3}(\d{2})[A-Z0-9]{2,}\b/i', $productName, $matches)) {
            $results['TFG_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => substr($matches[0], 0, 3)
            ];
        }

        // X/Y34QC-65W 형식 테스트
        if (preg_match('/\b[XY](\d{2})[A-Z0-9]+[-\s]?[A-Z0-9]*\b/i', $productName, $matches)) {
            $results['XY_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[0][0] // X 또는 Y
            ];
        }

        // G32-QC-10, T24t-20 형식 테스트
        if (preg_match('/\b[A-Z](\d{2})[-\s]?[A-Z0-9]+\b/i', $productName, $matches)) {
            $results['single_letter_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[0][0]
            ];
        }

        // 일반적인 알파벳 + 숫자 패턴 테스트
        if (preg_match('/\b[A-Z](\d{2})[A-Z0-9]+\b/i', $productName, $matches)) {
            $results['general_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[0][0]
            ];
        }

        // 해상도 패턴 테스트 (GL27QHD 등)
        if (preg_match('/\b[A-Z]{2}(\d{2})(?:QHD|FHD|UHD|HD)\b/i', $productName, $matches)) {
            $results['resolution_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => substr($matches[0], 0, 2)
            ];
        }

        // CURVED-27 형식 테스트
        if (preg_match('/\b[A-Z]+-(\d{2})\b/i', $productName, $matches)) {
            $results['curved_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => explode('-', $matches[0])[0]
            ];
        }

        // QUEEN-325 형식 테스트
        if (preg_match('/\b[A-Z]+-(\d{2})\d\b/i', $productName, $matches)) {
            $results['queen_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => explode('-', $matches[0])[0]
            ];
        }

        // 321URX 형식 테스트
        if (preg_match('/\b(\d{2})\d[A-Z]{2,}[A-Z0-9]*\b/i', $productName, $matches)) {
            $results['three_digit_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => substr($matches[0], 0, 3)
            ];
        }

        // 246E9 형식 테스트
        if (preg_match('/\b(\d{2})\d[A-Z0-9]+\b/i', $productName, $matches)) {
            $results['three_digit_alphanumeric_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => substr($matches[0], 0, 3)
            ];
        }

        // 24MP3 형식 테스트
        if (preg_match('/\b(\d{2})[A-Z]{2}\d\b/i', $productName, $matches)) {
            $results['two_digit_alpha_two_digit_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[1]
            ];
        }

        // 24MP3, 27IPS1 형식 테스트 (2-3글자 알파벳)
        if (preg_match('/\b(\d{2})[A-Z]{2,3}\d\b/i', $productName, $matches)) {
            $results['two_digit_alpha_three_digit_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[1]
            ];
        }

        // MP341CQW 형식 테스트 (제외 키워드 제외)
        if (preg_match('/\b[A-Z]{2}(\d{2})(?!(' . $this->getExcludePattern() . ')\b)[A-Z0-9]+\b/i', $productName, $matches)) {
            $results['mp_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => substr($matches[0], 0, 2)
            ];
        }

        // CP-2775 형식 테스트
        if (preg_match('/\b[A-Z]{2}-(\d{2})\d{2}\b/i', $productName, $matches)) {
            $results['cp_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => substr($matches[0], 0, 2)
            ];
        }

        // 2400 형식 테스트
        if (preg_match('/\b(\d{2})00\b/i', $productName, $matches)) {
            $results['four_digit_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[1]
            ];
        }

                // 320 형식 테스트
        if (preg_match('/\b(\d{2})\d\b/i', $productName, $matches)) {
            $results['three_digit_standalone_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[1]
            ];
        }

        // 2자리 숫자 + 알파벳 패턴 테스트 (뒤 제외 키워드 제외)
        if (preg_match('/\b(\d{2})(?!(' . $this->getExcludePattern() . ')\b)[A-Z]+\b/i', $productName, $matches)) {
            $results['two_digit_alpha_pattern'] = [
                'matched' => true,
                'size' => intval($matches[1]),
                'full_match' => $matches[0],
                'prefix' => $matches[1]
            ];
        }

        return $results;
    }
}
