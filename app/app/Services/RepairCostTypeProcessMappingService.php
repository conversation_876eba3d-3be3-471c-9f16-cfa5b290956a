<?php

namespace App\Services;

use App\Models\RepairProcess;
use App\Models\RepairCostType;
use App\Models\RepairCostTypeProcessMapping;
use Exception;
use Illuminate\Support\Collection;
use InvalidArgumentException;

class RepairCostTypeProcessMappingService
{
    /**
     * 수리 유형별 프로세스 매핑 생성
     */
    public function createMapping(string $costTypeCode, array $processCodes): array
    {
        $costType = RepairCostType::where('code', $costTypeCode)->first();

        if (!$costType) {
            throw new InvalidArgumentException("수리 유형 '{$costTypeCode}'을 찾을 수 없습니다.");
        }

        $createdMappings = [];
        $errors = [];

        foreach ($processCodes as $processCode) {
            $process = RepairProcess::where('code', $processCode)->first();

            if (!$process) {
                $errors[] = "수리 프로세스 '{$processCode}'을 찾을 수 없습니다.";
                continue;
            }

            try {
                $mapping = RepairCostTypeProcessMapping::updateOrCreate(
                    ['repair_process_id' => $process->id],
                    [
                        'repair_cost_type_id' => $costType->id,
                        'is_active' => true
                    ]
                );

                $createdMappings[] = $mapping;
            } catch (Exception $e) {
                $errors[] = "매핑 생성 실패: {$processCode} - {$e->getMessage()}";
            }
        }

        return [
            'created' => $createdMappings,
            'errors' => $errors
        ];
    }

    /**
     * 프로세스 코드로 수리 유형 조회
     */
    public function getCostTypeByProcessCode(string $processCode): ?RepairCostType
    {
        $mapping = RepairCostTypeProcessMapping::with('repairCostType')
            ->whereHas('repairProcess', function($query) use ($processCode) {
                $query->where('code', $processCode);
            })
            ->active()
            ->first();

        return $mapping?->repairCostType;
    }

    /**
     * 수리 유형 코드로 관련 프로세스들 조회
     */
    public function getProcessesByCostTypeCode(string $costTypeCode): Collection
    {
        return RepairProcess::whereHas('costTypeProcessMapping', function($query) use ($costTypeCode) {
            $query->whereHas('repairCostType', function($q) use ($costTypeCode) {
                $q->where('code', $costTypeCode);
            })->active();
        })->get();
    }

    /**
     * 매핑 비활성화
     */
    public function deactivateMapping(int $processId): bool
    {
        $mapping = RepairCostTypeProcessMapping::where('repair_process_id', $processId)->first();

        if ($mapping) {
            $mapping->update(['is_active' => false]);
            return true;
        }

        return false;
    }

    /**
     * 매핑 활성화
     */
    public function activateMapping(int $processId): bool
    {
        $mapping = RepairCostTypeProcessMapping::where('repair_process_id', $processId)->first();

        if ($mapping) {
            $mapping->update(['is_active' => true]);
            return true;
        }

        return false;
    }

    /**
     * 매핑 변경
     */
    public function updateMapping(int $processId, string $newCostTypeCode): bool
    {
        $costType = RepairCostType::where('code', $newCostTypeCode)->first();

        if (!$costType) {
            throw new InvalidArgumentException("수리 유형 '{$newCostTypeCode}'을 찾을 수 없습니다.");
        }

        $mapping = RepairCostTypeProcessMapping::where('repair_process_id', $processId)->first();

        if ($mapping) {
            $mapping->update(['repair_cost_type_id' => $costType->id]);
            return true;
        }

        return false;
    }

    /**
     * 매핑 통계 조회
     */
    public function getMappingStatistics(): array
    {
        $stats = RepairCostType::withCount(['costTypeProcessMappings' => function($query) {
            $query->active();
        }])->active()->get();

        return $stats->mapWithKeys(function ($costType) {
            return [$costType->name => $costType->process_mappings_count];
        })->toArray();
    }

    /**
     * 매핑되지 않은 프로세스 조회
     */
    public function getUnmappedProcesses(): Collection
    {
        return RepairProcess::whereDoesntHave('costTypeProcessMapping')->get();
    }

    /**
     * 매핑되지 않은 수리 유형 조회
     */
    public function getUnmappedCostTypes(): Collection
    {
        return RepairCostType::whereDoesntHave('costTypeProcessMappings')->active()->get();
    }
}
