<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Exception;

/**
 * SSE 이벤트 관리 서비스
 * 
 * 모델 변경 이벤트를 처리하고 실시간 데이터 동기화를 위한
 * 브로드캐스팅을 관리합니다.
 */
class EventManager
{
    private NotificationManager $notificationManager;
    private DataSerializationService $dataSerializer;
    
    private const DEBOUNCE_CACHE_PREFIX = 'sse:debounce:';
    private const DEBOUNCE_DELAY_SECONDS = 2;
    private const MAX_DEBOUNCE_EVENTS = 10;

    /**
     * 등록된 모델 이벤트 리스너 목록
     */
    private array $registeredObservers = [];

    /**
     * 디바운싱 대상 이벤트 타입
     */
    private array $debounceableEvents = [
        'created',
        'updated',
        'deleted'
    ];

    public function __construct(
        NotificationManager $notificationManager,
        DataSerializationService $dataSerializer
    ) {
        $this->notificationManager = $notificationManager;
        $this->dataSerializer = $dataSerializer;
    }

    /**
     * 모델 변경 이벤트를 처리합니다.
     *
     * @param string $eventType 이벤트 타입 (created, updated, deleted)
     * @param Model $model 변경된 모델 인스턴스
     * @return void
     */
    public function handleModelEvent(string $eventType, Model $model): void
    {
        try {
            if (!$this->shouldBroadcast($eventType, $model)) {
                Log::debug('이벤트 브로드캐스트 건너뜀', [
                    'event_type' => $eventType,
                    'model_class' => get_class($model),
                    'model_id' => $model->getKey()
                ]);
                return;
            }

            $modelClass = get_class($model);
            $modelName = $this->getModelName($modelClass);

            Log::info('모델 이벤트 처리 시작', [
                'event_type' => $eventType,
                'model_class' => $modelClass,
                'model_name' => $modelName,
                'model_id' => $model->getKey()
            ]);

            // 디바운싱 적용 여부 확인
            if ($this->shouldDebounce($eventType, $modelName)) {
                $this->handleDebouncedEvent($eventType, $modelName, $model);
            } else {
                $this->processModelEvent($eventType, $modelName, $model);
            }

        } catch (Exception $e) {
            Log::error('모델 이벤트 처리 실패', [
                'event_type' => $eventType,
                'model_class' => get_class($model),
                'model_id' => $model->getKey(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 데이터 업데이트를 브로드캐스트합니다.
     *
     * @param string $dataType 데이터 타입
     * @param array $data 브로드캐스트할 데이터
     * @param string $action 액션 타입 (기본값: 'updated')
     * @return array 브로드캐스트 결과
     */
    public function broadcastDataUpdate(string $dataType, array $data, string $action = 'updated'): array
    {
        try {
            Log::info('데이터 업데이트 브로드캐스트 시작', [
                'data_type' => $dataType,
                'action' => $action,
                'data_size' => count($data)
            ]);

            $result = $this->notificationManager->sendDataUpdate($dataType, $action, $data);

            Log::info('데이터 업데이트 브로드캐스트 완료', [
                'data_type' => $dataType,
                'action' => $action,
                'result' => $result
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('데이터 업데이트 브로드캐스트 실패', [
                'data_type' => $dataType,
                'action' => $action,
                'error' => $e->getMessage()
            ]);

            return [
                'total_connections' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 모델 Observer를 등록합니다.
     *
     * @param string $modelClass 모델 클래스명
     * @param string $observerClass Observer 클래스명 (선택사항)
     * @return bool 등록 성공 여부
     */
    public function registerModelObserver(string $modelClass, string $observerClass = null): bool
    {
        try {
            if (in_array($modelClass, $this->registeredObservers)) {
                Log::warning('이미 등록된 모델 Observer', [
                    'model_class' => $modelClass
                ]);
                return true;
            }

            // Observer 클래스가 지정되지 않은 경우 기본 Observer 사용
            if ($observerClass === null) {
                $observerClass = $this->getDefaultObserverClass($modelClass);
            }

            if (!class_exists($observerClass)) {
                Log::error('Observer 클래스를 찾을 수 없음', [
                    'model_class' => $modelClass,
                    'observer_class' => $observerClass
                ]);
                return false;
            }

            // Observer 등록
            $modelClass::observe($observerClass);
            $this->registeredObservers[] = $modelClass;

            Log::info('모델 Observer 등록 완료', [
                'model_class' => $modelClass,
                'observer_class' => $observerClass
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('모델 Observer 등록 실패', [
                'model_class' => $modelClass,
                'observer_class' => $observerClass,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 이벤트 브로드캐스트 여부를 결정합니다.
     *
     * @param string $eventType 이벤트 타입
     * @param Model $model 모델 인스턴스
     * @return bool 브로드캐스트 여부
     */
    private function shouldBroadcast(string $eventType, Model $model): bool
    {
        $modelClass = get_class($model);
        $modelName = $this->getModelName($modelClass);

        // 지원하는 모델인지 확인
        $supportedModels = ['cate4', 'cate5'];
        if (!in_array($modelName, $supportedModels)) {
            return false;
        }

        // 지원하는 이벤트 타입인지 확인
        $supportedEvents = ['created', 'updated', 'deleted'];
        if (!in_array($eventType, $supportedEvents)) {
            return false;
        }

        // 모델별 특별한 조건 확인
        return $this->checkModelSpecificConditions($eventType, $model);
    }

    /**
     * 모델별 특별한 브로드캐스트 조건을 확인합니다.
     *
     * @param string $eventType 이벤트 타입
     * @param Model $model 모델 인스턴스
     * @return bool 브로드캐스트 허용 여부
     */
    private function checkModelSpecificConditions(string $eventType, Model $model): bool
    {
        $modelName = $this->getModelName(get_class($model));

        switch ($modelName) {
            case 'cate4':
            case 'cate5':
                // 카테고리 모델은 모든 변경사항을 브로드캐스트
                return true;

            default:
                return false;
        }
    }

    /**
     * 디바운싱 적용 여부를 확인합니다.
     *
     * @param string $eventType 이벤트 타입
     * @param string $modelName 모델명
     * @return bool 디바운싱 적용 여부
     */
    private function shouldDebounce(string $eventType, string $modelName): bool
    {
        // 디바운싱 대상 이벤트인지 확인
        if (!in_array($eventType, $this->debounceableEvents)) {
            return false;
        }

        // 카테고리 모델은 디바운싱 적용
        return in_array($modelName, ['cate4', 'cate5']);
    }

    /**
     * 디바운싱이 적용된 이벤트를 처리합니다.
     *
     * @param string $eventType 이벤트 타입
     * @param string $modelName 모델명
     * @param Model $model 모델 인스턴스
     * @return void
     */
    private function handleDebouncedEvent(string $eventType, string $modelName, Model $model): void
    {
        $debounceKey = $this->getDebounceKey($modelName);
        $eventData = [
            'event_type' => $eventType,
            'model_name' => $modelName,
            'model_id' => $model->getKey(),
            'timestamp' => Carbon::now()->toISOString()
        ];

        try {
            // 기존 디바운스 데이터 조회
            $existingEvents = Cache::get($debounceKey, []);
            
            // 새 이벤트 추가
            $existingEvents[] = $eventData;
            
            // 최대 이벤트 수 제한
            if (count($existingEvents) > self::MAX_DEBOUNCE_EVENTS) {
                $existingEvents = array_slice($existingEvents, -self::MAX_DEBOUNCE_EVENTS);
            }

            // 디바운스 캐시 업데이트
            Cache::put($debounceKey, $existingEvents, self::DEBOUNCE_DELAY_SECONDS);

            Log::debug('디바운스 이벤트 추가', [
                'debounce_key' => $debounceKey,
                'event_count' => count($existingEvents),
                'latest_event' => $eventData
            ]);

            // 디바운스 처리 스케줄링
            $this->scheduleDebouncedProcessing($modelName);

        } catch (Exception $e) {
            Log::error('디바운스 이벤트 처리 실패', [
                'event_type' => $eventType,
                'model_name' => $modelName,
                'error' => $e->getMessage()
            ]);

            // 디바운싱 실패 시 즉시 처리
            $this->processModelEvent($eventType, $modelName, $model);
        }
    }

    /**
     * 디바운스된 이벤트 처리를 스케줄링합니다.
     *
     * @param string $modelName 모델명
     * @return void
     */
    private function scheduleDebouncedProcessing(string $modelName): void
    {
        $scheduleKey = "sse:schedule:{$modelName}";
        
        // 이미 스케줄링된 경우 건너뜀
        if (Cache::has($scheduleKey)) {
            return;
        }

        // 스케줄링 마크 설정
        Cache::put($scheduleKey, true, self::DEBOUNCE_DELAY_SECONDS + 1);

        // 지연 처리 (실제 환경에서는 Queue Job 사용 권장)
        dispatch(function () use ($modelName) {
            sleep(self::DEBOUNCE_DELAY_SECONDS);
            $this->processDebouncedEvents($modelName);
        })->afterResponse();
    }

    /**
     * 디바운스된 이벤트들을 일괄 처리합니다.
     *
     * @param string $modelName 모델명
     * @return void
     */
    private function processDebouncedEvents(string $modelName): void
    {
        try {
            $debounceKey = $this->getDebounceKey($modelName);
            $events = Cache::get($debounceKey, []);

            if (empty($events)) {
                return;
            }

            Log::info('디바운스된 이벤트 일괄 처리 시작', [
                'model_name' => $modelName,
                'event_count' => count($events)
            ]);

            // 캐시 정리
            Cache::forget($debounceKey);
            Cache::forget("sse:schedule:{$modelName}");

            // 카테고리 데이터 직렬화 및 브로드캐스트
            if (in_array($modelName, ['cate4', 'cate5'])) {
                $categoryData = $this->dataSerializer->serializeCategoryData();
                $this->broadcastDataUpdate('categories', $categoryData, 'updated');
            }

            Log::info('디바운스된 이벤트 일괄 처리 완료', [
                'model_name' => $modelName,
                'processed_events' => count($events)
            ]);

        } catch (Exception $e) {
            Log::error('디바운스된 이벤트 일괄 처리 실패', [
                'model_name' => $modelName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 개별 모델 이벤트를 즉시 처리합니다.
     *
     * @param string $eventType 이벤트 타입
     * @param string $modelName 모델명
     * @param Model $model 모델 인스턴스
     * @return void
     */
    private function processModelEvent(string $eventType, string $modelName, Model $model): void
    {
        try {
            Log::info('모델 이벤트 즉시 처리', [
                'event_type' => $eventType,
                'model_name' => $modelName,
                'model_id' => $model->getKey()
            ]);

            // 카테고리 모델 처리
            if (in_array($modelName, ['cate4', 'cate5'])) {
                $categoryData = $this->dataSerializer->serializeCategoryData();
                $this->broadcastDataUpdate('categories', $categoryData, $eventType);
            }

        } catch (Exception $e) {
            Log::error('모델 이벤트 즉시 처리 실패', [
                'event_type' => $eventType,
                'model_name' => $modelName,
                'model_id' => $model->getKey(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 모델 클래스명에서 모델명을 추출합니다.
     *
     * @param string $modelClass 모델 클래스명
     * @return string 모델명
     */
    private function getModelName(string $modelClass): string
    {
        $className = class_basename($modelClass);
        return strtolower($className);
    }

    /**
     * 기본 Observer 클래스명을 생성합니다.
     *
     * @param string $modelClass 모델 클래스명
     * @return string Observer 클래스명
     */
    private function getDefaultObserverClass(string $modelClass): string
    {
        $modelName = class_basename($modelClass);
        return "App\\Observers\\{$modelName}Observer";
    }

    /**
     * 디바운스 캐시 키를 생성합니다.
     *
     * @param string $modelName 모델명
     * @return string 디바운스 캐시 키
     */
    private function getDebounceKey(string $modelName): string
    {
        return self::DEBOUNCE_CACHE_PREFIX . $modelName;
    }

    /**
     * 등록된 Observer 목록을 반환합니다.
     *
     * @return array 등록된 Observer 목록
     */
    public function getRegisteredObservers(): array
    {
        return $this->registeredObservers;
    }

    /**
     * 디바운스 상태를 확인합니다.
     *
     * @param string $modelName 모델명
     * @return array 디바운스 상태 정보
     */
    public function getDebounceStatus(string $modelName): array
    {
        $debounceKey = $this->getDebounceKey($modelName);
        $scheduleKey = "sse:schedule:{$modelName}";
        
        return [
            'model_name' => $modelName,
            'pending_events' => Cache::get($debounceKey, []),
            'is_scheduled' => Cache::has($scheduleKey),
            'debounce_delay' => self::DEBOUNCE_DELAY_SECONDS,
            'max_events' => self::MAX_DEBOUNCE_EVENTS
        ];
    }

    /**
     * 특정 모델의 디바운스 캐시를 강제로 처리합니다.
     *
     * @param string $modelName 모델명
     * @return bool 처리 성공 여부
     */
    public function flushDebounceCache(string $modelName): bool
    {
        try {
            $this->processDebouncedEvents($modelName);
            
            Log::info('디바운스 캐시 강제 처리 완료', [
                'model_name' => $modelName
            ]);
            
            return true;

        } catch (Exception $e) {
            Log::error('디바운스 캐시 강제 처리 실패', [
                'model_name' => $modelName,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}