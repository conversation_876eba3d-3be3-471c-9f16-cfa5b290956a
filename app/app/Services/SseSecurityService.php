<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * SSE 보안 강화 서비스
 * 
 * 민감 정보 필터링, XSS 방지, 의심스러운 활동 탐지 등
 * 고급 보안 기능을 제공합니다.
 */
class SseSecurityService
{
    private SseLogger $logger;
    
    /**
     * 민감 정보 패턴 정의
     */
    private const SENSITIVE_PATTERNS = [
        'password' => '/password["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'token' => '/token["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'secret' => '/secret["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'key' => '/(?:api_?key|access_?key)["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'authorization' => '/authorization["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'cookie' => '/cookie["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'session' => '/session["\']?\s*[:=]\s*["\']?([^"\'\\s,}]+)/i',
        'email' => '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/',
        'phone' => '/(?:\+82|0)(?:10|11|16|17|18|19)-?\d{3,4}-?\d{4}/',
        'card_number' => '/\b(?:\d{4}[-\s]?){3}\d{4}\b/',
        'ssn' => '/\b\d{6}-[1-4]\d{6}\b/', // 주민등록번호
    ];
    
    /**
     * XSS 공격 패턴
     */
    private const XSS_PATTERNS = [
        '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
        '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
        '/javascript:/i',
        '/on\w+\s*=/i',
        '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi',
        '/<embed\b[^>]*>/i',
        '/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/mi',
        '/expression\s*\(/i',
        '/vbscript:/i',
        '/data:text\/html/i',
    ];
    
    /**
     * SQL Injection 패턴
     */
    private const SQL_INJECTION_PATTERNS = [
        '/(\bunion\b|\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b|\bcreate\b|\balter\b)/i',
        '/(\bor\b|\band\b)\s+\d+\s*=\s*\d+/i',
        '/[\'";].*(\bor\b|\band\b).*[\'";]/i',
        '/\b(exec|execute|sp_|xp_)\w*/i',
        '/(\-\-|\#|\/\*|\*\/)/i',
    ];
    
    /**
     * 의심스러운 활동 임계값
     */
    private const SUSPICIOUS_THRESHOLDS = [
        'failed_auth_attempts' => 5,
        'rapid_requests' => 100, // 1분 내 요청 수
        'large_payload_size' => 1048576, // 1MB
        'unusual_user_agent' => true,
        'multiple_ips_per_user' => 3,
    ];
    
    public function __construct(SseLogger $logger)
    {
        $this->logger = $logger;
    }
    
    /**
     * 메시지 데이터에서 민감 정보 필터링
     */
    public function filterSensitiveData(array $data): array
    {
        return $this->recursiveFilter($data);
    }
    
    /**
     * 재귀적으로 민감 정보 필터링
     */
    private function recursiveFilter($data): mixed
    {
        if (is_array($data)) {
            $filtered = [];
            foreach ($data as $key => $value) {
                // 키 자체가 민감한 경우
                if ($this->isSensitiveKey($key)) {
                    $filtered[$key] = $this->maskValue($value);
                } else {
                    $filtered[$key] = $this->recursiveFilter($value);
                }
            }
            return $filtered;
        }
        
        if (is_string($data)) {
            return $this->filterSensitiveString($data);
        }
        
        return $data;
    }
    
    /**
     * 키가 민감한 정보인지 확인
     */
    private function isSensitiveKey(string $key): bool
    {
        $lowerKey = strtolower($key);
        $sensitiveKeys = [
            'password', 'passwd', 'pwd',
            'token', 'access_token', 'refresh_token', 'api_token',
            'secret', 'api_secret', 'client_secret',
            'key', 'api_key', 'access_key', 'private_key',
            'authorization', 'auth',
            'cookie', 'session', 'session_id',
            'credit_card', 'card_number', 'cvv', 'cvc',
            'ssn', 'social_security', 'resident_number',
        ];
        
        foreach ($sensitiveKeys as $sensitiveKey) {
            if (str_contains($lowerKey, $sensitiveKey)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 문자열에서 민감 정보 패턴 필터링
     */
    private function filterSensitiveString(string $data): string
    {
        $filtered = $data;
        
        foreach (self::SENSITIVE_PATTERNS as $type => $pattern) {
            $filtered = preg_replace_callback($pattern, function ($matches) use ($type) {
                $this->logger->logSecurityEvent(
                    'sensitive_data_detected',
                    "민감 정보 감지됨: {$type}"
                );
                
                // 첫 번째와 마지막 문자만 보여주고 나머지는 마스킹
                if (isset($matches[1]) && strlen($matches[1]) > 2) {
                    $value = $matches[1];
                    return str_replace($value, $this->maskValue($value), $matches[0]);
                }
                
                return str_replace($matches[0], '[민감정보 마스킹됨]', $matches[0]);
            }, $filtered);
        }
        
        return $filtered;
    }
    
    /**
     * 값을 마스킹 처리
     */
    private function maskValue(string $value): string
    {
        $length = strlen($value);
        
        if ($length <= 2) {
            return str_repeat('*', $length);
        }
        
        if ($length <= 4) {
            return $value[0] . str_repeat('*', $length - 2) . $value[-1];
        }
        
        return $value[0] . $value[1] . str_repeat('*', $length - 4) . $value[-2] . $value[-1];
    }
    
    /**
     * XSS 공격 패턴 검증
     */
    public function validateAgainstXss(string $input): bool
    {
        foreach (self::XSS_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                $this->logger->logSecurityEvent(
                    'xss_attempt_detected',
                    'XSS 공격 시도 감지',
                    ['input_sample' => substr($input, 0, 100)]
                );
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * SQL Injection 공격 패턴 검증
     */
    public function validateAgainstSqlInjection(string $input): bool
    {
        foreach (self::SQL_INJECTION_PATTERNS as $pattern) {
            if (preg_match($pattern, $input)) {
                $this->logger->logSecurityEvent(
                    'sql_injection_attempt_detected',
                    'SQL Injection 공격 시도 감지',
                    ['input_sample' => substr($input, 0, 100)]
                );
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 메시지 내용 전체 검증
     */
    public function validateMessageContent(array $data): bool
    {
        $jsonString = json_encode($data);
        
        // XSS 검증
        if (!$this->validateAgainstXss($jsonString)) {
            return false;
        }
        
        // SQL Injection 검증
        if (!$this->validateAgainstSqlInjection($jsonString)) {
            return false;
        }
        
        // 메시지 크기 검증
        if (strlen($jsonString) > self::SUSPICIOUS_THRESHOLDS['large_payload_size']) {
            $this->logger->logSecurityEvent(
                'large_payload_detected',
                '비정상적으로 큰 페이로드 감지',
                ['payload_size' => strlen($jsonString)]
            );
            return false;
        }
        
        return true;
    }
    
    /**
     * 의심스러운 활동 탐지
     */
    public function detectSuspiciousActivity(Request $request, ?User $user = null): array
    {
        $suspiciousActivities = [];
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        // 1. 빠른 연속 요청 탐지
        if ($this->detectRapidRequests($ip)) {
            $suspiciousActivities[] = 'rapid_requests';
            $this->logger->logSecurityEvent(
                'rapid_requests_detected',
                '빠른 연속 요청 감지',
                ['ip' => $ip, 'user_id' => $user?->id]
            );
        }
        
        // 2. 비정상적인 User-Agent 탐지
        if ($this->detectUnusualUserAgent($userAgent)) {
            $suspiciousActivities[] = 'unusual_user_agent';
            $this->logger->logSecurityEvent(
                'unusual_user_agent_detected',
                '비정상적인 User-Agent 감지',
                ['user_agent' => substr($userAgent, 0, 100), 'ip' => $ip]
            );
        }
        
        // 3. 사용자별 다중 IP 접근 탐지 (인증된 사용자만)
        if ($user && $this->detectMultipleIpsForUser($user->id, $ip)) {
            $suspiciousActivities[] = 'multiple_ips_per_user';
            $this->logger->logSecurityEvent(
                'multiple_ips_detected',
                '사용자 다중 IP 접근 감지',
                ['user_id' => $user->id, 'ip' => $ip]
            );
        }
        
        // 4. 인증 실패 패턴 탐지
        if ($this->detectAuthFailurePattern($ip)) {
            $suspiciousActivities[] = 'auth_failure_pattern';
            $this->logger->logSecurityEvent(
                'auth_failure_pattern_detected',
                '인증 실패 패턴 감지',
                ['ip' => $ip]
            );
        }
        
        return $suspiciousActivities;
    }
    
    /**
     * 빠른 연속 요청 탐지
     */
    private function detectRapidRequests(string $ip): bool
    {
        $cacheKey = "sse_rapid_requests:{$ip}";
        $requests = Cache::get($cacheKey, 0);
        
        Cache::put($cacheKey, $requests + 1, 60); // 1분간 유지
        
        return $requests > self::SUSPICIOUS_THRESHOLDS['rapid_requests'];
    }
    
    /**
     * 비정상적인 User-Agent 탐지
     */
    private function detectUnusualUserAgent(string $userAgent): bool
    {
        // 빈 User-Agent
        if (empty($userAgent)) {
            return true;
        }
        
        // 너무 짧은 User-Agent
        if (strlen($userAgent) < 10) {
            return true;
        }
        
        // 의심스러운 패턴
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/java/i',
            '/perl/i',
            '/ruby/i',
            '/php/i',
            '/test/i',
            '/scanner/i',
            '/exploit/i',
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }
        
        // 일반적이지 않은 브라우저 패턴
        $normalBrowsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera'];
        $hasNormalBrowser = false;
        
        foreach ($normalBrowsers as $browser) {
            if (str_contains($userAgent, $browser)) {
                $hasNormalBrowser = true;
                break;
            }
        }
        
        return !$hasNormalBrowser;
    }
    
    /**
     * 사용자별 다중 IP 접근 탐지
     */
    private function detectMultipleIpsForUser(int $userId, string $currentIp): bool
    {
        $cacheKey = "sse_user_ips:{$userId}";
        $userIps = Cache::get($cacheKey, []);
        
        if (!in_array($currentIp, $userIps)) {
            $userIps[] = $currentIp;
            Cache::put($cacheKey, $userIps, 3600); // 1시간 유지
        }
        
        return count($userIps) > self::SUSPICIOUS_THRESHOLDS['multiple_ips_per_user'];
    }
    
    /**
     * 인증 실패 패턴 탐지
     */
    private function detectAuthFailurePattern(string $ip): bool
    {
        $cacheKey = "sse_auth_failures:{$ip}";
        $failures = Cache::get($cacheKey, 0);
        
        return $failures >= self::SUSPICIOUS_THRESHOLDS['failed_auth_attempts'];
    }
    
    /**
     * 인증 실패 기록
     */
    public function recordAuthFailure(string $ip): void
    {
        $cacheKey = "sse_auth_failures:{$ip}";
        $failures = Cache::get($cacheKey, 0);
        
        Cache::put($cacheKey, $failures + 1, 3600); // 1시간 유지
        
        $this->logger->logAuthenticationFailure('session_invalid', [
            'ip' => $ip,
            'failure_count' => $failures + 1
        ]);
    }
    
    /**
     * IP 차단 여부 확인
     */
    public function isIpBlocked(string $ip): bool
    {
        $cacheKey = "sse_blocked_ip:{$ip}";
        return Cache::has($cacheKey);
    }
    
    /**
     * IP 차단
     */
    public function blockIp(string $ip, int $durationMinutes = 60): void
    {
        $cacheKey = "sse_blocked_ip:{$ip}";
        Cache::put($cacheKey, true, $durationMinutes * 60);
        
        $this->logger->logSecurityEvent(
            'ip_blocked',
            'IP 주소 차단',
            ['ip' => $ip, 'duration_minutes' => $durationMinutes]
        );
    }
    
    /**
     * 보안 헤더 검증
     */
    public function validateSecurityHeaders(Request $request): bool
    {
        // Origin 헤더 검증
        $origin = $request->header('Origin');
        if ($origin && !$this->isAllowedOrigin($origin)) {
            $this->logger->logSecurityEvent(
                'invalid_origin',
                '허용되지 않은 Origin 헤더',
                ['origin' => $origin, 'ip' => $request->ip()]
            );
            return false;
        }
        
        // Referer 헤더 검증
        $referer = $request->header('Referer');
        if ($referer && !$this->isAllowedReferer($referer)) {
            $this->logger->logSecurityEvent(
                'invalid_referer',
                '허용되지 않은 Referer 헤더',
                ['referer' => $referer, 'ip' => $request->ip()]
            );
            return false;
        }
        
        return true;
    }
    
    /**
     * 허용된 Origin인지 확인
     */
    private function isAllowedOrigin(string $origin): bool
    {
        $allowedOrigins = [
            config('app.url'),
            'http://localhost:3000',
            'http://localhost:8080',
            'tauri://localhost',
        ];
        
        return in_array($origin, $allowedOrigins);
    }
    
    /**
     * 허용된 Referer인지 확인
     */
    private function isAllowedReferer(string $referer): bool
    {
        $allowedDomains = [
            parse_url(config('app.url'), PHP_URL_HOST),
            'localhost',
        ];
        
        $refererHost = parse_url($referer, PHP_URL_HOST);
        
        return in_array($refererHost, $allowedDomains);
    }
    
    /**
     * 보안 이벤트 요약 생성
     */
    public function generateSecuritySummary(string $ip, ?int $userId = null): array
    {
        $summary = [
            'ip' => $ip,
            'user_id' => $userId,
            'is_blocked' => $this->isIpBlocked($ip),
            'auth_failures' => Cache::get("sse_auth_failures:{$ip}", 0),
            'rapid_requests' => Cache::get("sse_rapid_requests:{$ip}", 0),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        if ($userId) {
            $summary['user_ips'] = Cache::get("sse_user_ips:{$userId}", []);
        }
        
        return $summary;
    }
    
    /**
     * 보안 설정 초기화 (테스트용)
     */
    public function resetSecurityCounters(string $ip, ?int $userId = null): void
    {
        Cache::forget("sse_auth_failures:{$ip}");
        Cache::forget("sse_rapid_requests:{$ip}");
        Cache::forget("sse_blocked_ip:{$ip}");
        
        if ($userId) {
            Cache::forget("sse_user_ips:{$userId}");
        }
    }
}