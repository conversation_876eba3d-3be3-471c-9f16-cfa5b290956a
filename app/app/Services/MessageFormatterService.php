<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * SSE 메시지 포맷팅 및 직렬화 서비스
 * 
 * 다양한 타입의 메시지를 표준 형식으로 포맷팅하고
 * JSON 크기를 최적화하여 전송 효율성을 높입니다.
 */
class MessageFormatterService
{
    private const MAX_MESSAGE_SIZE = 65536; // 64KB
    private const COMPRESSION_THRESHOLD = 1024; // 1KB
    
    /**
     * 알림 메시지를 표준 형식으로 포맷팅합니다.
     *
     * @param array $data 알림 데이터
     * @param string $priority 우선순위 (low, normal, high, urgent)
     * @param string|null $actionUrl 액션 URL
     * @return array 포맷팅된 알림 메시지
     */
    public function formatNotificationMessage(
        array $data, 
        string $priority = 'normal', 
        ?string $actionUrl = null
    ): array {
        $message = [
            'type' => 'notification',
            'timestamp' => Carbon::now()->toISOString(),
            'data' => [
                'id' => $data['id'] ?? $this->generateMessageId(),
                'title' => $data['title'] ?? '알림',
                'message' => $data['message'] ?? '',
                'priority' => $this->validatePriority($priority),
                'category' => $data['category'] ?? 'general',
                'action_url' => $actionUrl,
                'expires_at' => $data['expires_at'] ?? null,
                'metadata' => $data['metadata'] ?? []
            ]
        ];

        return $this->optimizeMessageSize($message);
    }

    /**
     * 데이터 업데이트 메시지를 표준 형식으로 포맷팅합니다.
     *
     * @param string $model 모델명 (categories, users 등)
     * @param string $action 액션 (created, updated, deleted)
     * @param array $payload 업데이트된 데이터
     * @param array $metadata 추가 메타데이터
     * @return array 포맷팅된 데이터 업데이트 메시지
     */
    public function formatDataUpdateMessage(
        string $model, 
        string $action, 
        array $payload, 
        array $metadata = []
    ): array {
        $message = [
            'type' => 'data_update',
            'timestamp' => Carbon::now()->toISOString(),
            'data' => [
                'model' => $model,
                'action' => $this->validateAction($action),
                'payload' => $this->optimizePayload($payload),
                'affected_count' => $this->calculateAffectedCount($payload),
                'checksum' => $this->generateChecksum($payload),
                'metadata' => $metadata
            ]
        ];

        return $this->optimizeMessageSize($message);
    }

    /**
     * 시스템 상태 메시지를 표준 형식으로 포맷팅합니다.
     *
     * @param string $status 상태 (online, offline, maintenance)
     * @param array $data 상태 데이터
     * @return array 포맷팅된 시스템 상태 메시지
     */
    public function formatSystemStatusMessage(string $status, array $data = []): array
    {
        $message = [
            'type' => 'system_status',
            'timestamp' => Carbon::now()->toISOString(),
            'data' => [
                'status' => $this->validateSystemStatus($status),
                'message' => $data['message'] ?? '',
                'estimated_duration' => $data['estimated_duration'] ?? null,
                'affected_services' => $data['affected_services'] ?? [],
                'contact_info' => $data['contact_info'] ?? null
            ]
        ];

        return $this->optimizeMessageSize($message);
    }

    /**
     * 하트비트 메시지를 포맷팅합니다.
     *
     * @param array $serverInfo 서버 정보
     * @return array 포맷팅된 하트비트 메시지
     */
    public function formatHeartbeatMessage(array $serverInfo = []): array
    {
        return [
            'type' => 'heartbeat',
            'timestamp' => Carbon::now()->toISOString(),
            'data' => [
                'server_time' => Carbon::now()->toISOString(),
                'connection_count' => $serverInfo['connection_count'] ?? 0,
                'server_load' => $serverInfo['server_load'] ?? null
            ]
        ];
    }

    /**
     * 사용자 정의 메시지를 표준 형식으로 포맷팅합니다.
     *
     * @param string $type 메시지 타입
     * @param array $data 메시지 데이터
     * @param array $options 추가 옵션
     * @return array 포맷팅된 메시지
     */
    public function formatCustomMessage(string $type, array $data, array $options = []): array
    {
        $message = [
            'type' => $this->sanitizeMessageType($type),
            'timestamp' => Carbon::now()->toISOString(),
            'data' => $data
        ];

        // 옵션 적용
        if (isset($options['ttl'])) {
            $message['expires_at'] = Carbon::now()->addSeconds($options['ttl'])->toISOString();
        }

        if (isset($options['priority'])) {
            $message['priority'] = $this->validatePriority($options['priority']);
        }

        return $this->optimizeMessageSize($message);
    }

    /**
     * 메시지를 JSON 문자열로 직렬화합니다.
     *
     * @param array $message 메시지 배열
     * @param bool $compress 압축 여부
     * @return string JSON 문자열
     * @throws Exception 직렬화 실패 시
     */
    public function serializeMessage(array $message, bool $compress = false): string
    {
        try {
            // 메시지 유효성 검증
            $this->validateMessage($message);

            // JSON 직렬화
            $jsonString = json_encode($message, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            
            if ($jsonString === false) {
                throw new Exception('JSON 직렬화 실패: ' . json_last_error_msg());
            }

            // 크기 확인
            if (strlen($jsonString) > self::MAX_MESSAGE_SIZE) {
                Log::warning('메시지 크기가 제한을 초과합니다', [
                    'size' => strlen($jsonString),
                    'max_size' => self::MAX_MESSAGE_SIZE,
                    'message_type' => $message['type'] ?? 'unknown'
                ]);
                
                // 메시지 압축 시도
                $message = $this->compressMessage($message);
                $jsonString = json_encode($message, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }

            // 선택적 압축
            if ($compress && strlen($jsonString) > self::COMPRESSION_THRESHOLD) {
                return $this->compressJsonString($jsonString);
            }

            return $jsonString;

        } catch (Exception $e) {
            Log::error('메시지 직렬화 실패', [
                'message_type' => $message['type'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * JSON 문자열을 메시지 배열로 역직렬화합니다.
     *
     * @param string $jsonString JSON 문자열
     * @param bool $decompress 압축 해제 여부
     * @return array 메시지 배열
     * @throws Exception 역직렬화 실패 시
     */
    public function deserializeMessage(string $jsonString, bool $decompress = false): array
    {
        try {
            // 선택적 압축 해제
            if ($decompress) {
                $jsonString = $this->decompressJsonString($jsonString);
            }

            $message = json_decode($jsonString, true);
            
            if ($message === null) {
                throw new Exception('JSON 역직렬화 실패: ' . json_last_error_msg());
            }

            // 메시지 유효성 검증
            $this->validateMessage($message);

            return $message;

        } catch (Exception $e) {
            Log::error('메시지 역직렬화 실패', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 메시지 크기를 최적화합니다.
     *
     * @param array $message 원본 메시지
     * @return array 최적화된 메시지
     */
    private function optimizeMessageSize(array $message): array
    {
        // 불필요한 null 값 제거
        $message = $this->removeNullValues($message);

        // 중복 데이터 제거
        $message = $this->removeDuplicateData($message);

        // 긴 문자열 압축
        $message = $this->compressLongStrings($message);

        return $message;
    }

    /**
     * 페이로드 데이터를 최적화합니다.
     *
     * @param array $payload 원본 페이로드
     * @return array 최적화된 페이로드
     */
    private function optimizePayload(array $payload): array
    {
        // 불필요한 필드 제거
        $excludeFields = ['created_at', 'updated_at', 'deleted_at', 'password', 'remember_token'];
        
        return $this->removeFields($payload, $excludeFields);
    }

    /**
     * 배열에서 null 값을 재귀적으로 제거합니다.
     *
     * @param array $array 원본 배열
     * @return array null 값이 제거된 배열
     */
    private function removeNullValues(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            } elseif ($value === null) {
                unset($array[$key]);
            }
        }

        return $array;
    }

    /**
     * 배열에서 중복 데이터를 제거합니다.
     *
     * @param array $array 원본 배열
     * @return array 중복이 제거된 배열
     */
    private function removeDuplicateData(array $array): array
    {
        if (isset($array['data']['payload']) && is_array($array['data']['payload'])) {
            $array['data']['payload'] = array_unique($array['data']['payload'], SORT_REGULAR);
        }

        return $array;
    }

    /**
     * 긴 문자열을 압축합니다.
     *
     * @param array $array 원본 배열
     * @return array 문자열이 압축된 배열
     */
    private function compressLongStrings(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->compressLongStrings($value);
            } elseif (is_string($value) && strlen($value) > 1000) {
                // 1KB 이상의 문자열은 요약
                $array[$key] = substr($value, 0, 500) . '... [truncated]';
            }
        }

        return $array;
    }

    /**
     * 배열에서 지정된 필드들을 제거합니다.
     *
     * @param array $array 원본 배열
     * @param array $fields 제거할 필드명 배열
     * @return array 필드가 제거된 배열
     */
    private function removeFields(array $array, array $fields): array
    {
        foreach ($fields as $field) {
            unset($array[$field]);
        }

        if (is_array($array)) {
            foreach ($array as $key => $value) {
                if (is_array($value)) {
                    $array[$key] = $this->removeFields($value, $fields);
                }
            }
        }

        return $array;
    }

    /**
     * 메시지를 압축합니다.
     *
     * @param array $message 원본 메시지
     * @return array 압축된 메시지
     */
    private function compressMessage(array $message): array
    {
        // 데이터 필드만 압축
        if (isset($message['data'])) {
            $message['data'] = $this->optimizePayload($message['data']);
        }

        return $message;
    }

    /**
     * JSON 문자열을 압축합니다.
     *
     * @param string $jsonString 원본 JSON 문자열
     * @return string 압축된 문자열
     */
    private function compressJsonString(string $jsonString): string
    {
        return gzcompress($jsonString, 6);
    }

    /**
     * 압축된 JSON 문자열을 해제합니다.
     *
     * @param string $compressedString 압축된 문자열
     * @return string 해제된 JSON 문자열
     */
    private function decompressJsonString(string $compressedString): string
    {
        return gzuncompress($compressedString);
    }

    /**
     * 메시지 유효성을 검증합니다.
     *
     * @param array $message 검증할 메시지
     * @throws Exception 유효하지 않은 메시지인 경우
     */
    private function validateMessage(array $message): void
    {
        if (!isset($message['type'])) {
            throw new Exception('메시지 타입이 누락되었습니다');
        }

        if (!isset($message['timestamp'])) {
            throw new Exception('타임스탬프가 누락되었습니다');
        }

        if (!isset($message['data'])) {
            throw new Exception('메시지 데이터가 누락되었습니다');
        }
    }

    /**
     * 우선순위 값을 검증합니다.
     *
     * @param string $priority 우선순위
     * @return string 유효한 우선순위
     */
    private function validatePriority(string $priority): string
    {
        $validPriorities = ['low', 'normal', 'high', 'urgent'];
        return in_array($priority, $validPriorities) ? $priority : 'normal';
    }

    /**
     * 액션 값을 검증합니다.
     *
     * @param string $action 액션
     * @return string 유효한 액션
     */
    private function validateAction(string $action): string
    {
        $validActions = ['created', 'updated', 'deleted', 'restored'];
        return in_array($action, $validActions) ? $action : 'updated';
    }

    /**
     * 시스템 상태 값을 검증합니다.
     *
     * @param string $status 시스템 상태
     * @return string 유효한 시스템 상태
     */
    private function validateSystemStatus(string $status): string
    {
        $validStatuses = ['online', 'offline', 'maintenance', 'degraded'];
        return in_array($status, $validStatuses) ? $status : 'online';
    }

    /**
     * 메시지 타입을 정리합니다.
     *
     * @param string $type 메시지 타입
     * @return string 정리된 메시지 타입
     */
    private function sanitizeMessageType(string $type): string
    {
        // 알파벳, 숫자, 언더스코어만 허용
        return preg_replace('/[^a-zA-Z0-9_]/', '', $type);
    }

    /**
     * 고유한 메시지 ID를 생성합니다.
     *
     * @return string 메시지 ID
     */
    private function generateMessageId(): string
    {
        return 'msg_' . uniqid() . '_' . Carbon::now()->timestamp;
    }

    /**
     * 페이로드의 체크섬을 생성합니다.
     *
     * @param array $payload 페이로드 데이터
     * @return string 체크섬
     */
    private function generateChecksum(array $payload): string
    {
        return md5(json_encode($payload, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 페이로드에서 영향받은 항목 수를 계산합니다.
     *
     * @param array $payload 페이로드 데이터
     * @return int 영향받은 항목 수
     */
    private function calculateAffectedCount(array $payload): int
    {
        // 배열의 첫 번째 요소가 배열인 경우 (다중 항목)
        if (is_array($payload) && isset($payload[0]) && is_array($payload[0])) {
            return count($payload);
        }
        
        // 연관 배열에서 가장 큰 배열의 크기를 반환
        $maxCount = 0;
        foreach ($payload as $value) {
            if (is_array($value)) {
                $maxCount = max($maxCount, count($value));
            }
        }
        
        return $maxCount > 0 ? $maxCount : 1;
    }
}