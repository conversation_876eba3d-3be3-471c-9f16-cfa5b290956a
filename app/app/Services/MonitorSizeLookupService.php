<?php

namespace App\Services;

use App\Models\MonitorSizeLookup;
use App\Repositories\Interfaces\MonitorSizeLookupRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

readonly class MonitorSizeLookupService
{
    public function __construct(
        private MonitorSizeLookupRepositoryInterface $repository
    ) {}

    /**
     * 검색
     * - name, name_hash, brand, unit, min_size, max_size, pageSize
     * - Pagination 반환
     */
    public function search(array $filters): LengthAwarePaginator
    {
        return $this->repository->search($filters, (int) ($filters['pageSize'] ?? 20));
    }

    /**
     * 허용된 속성만 업데이트
     * - name, name_hash는 불변
     */
    public function updateAttributes(int $id, array $attributes): MonitorSizeLookup
    {
        // 보호: name, name_hash 필드는 금지
        if (array_key_exists('name', $attributes) || array_key_exists('name_hash', $attributes)) {
            throw ValidationException::withMessages([
                'name' => ['name은 수정할 수 없습니다.'],
                'name_hash' => ['name_hash는 수정할 수 없습니다.'],
            ]);
        }

        $allowed = Arr::only($attributes, ['brand', 'size', 'unit']);

        return $this->repository->updateById($id, $allowed);
    }
}


