<?php

namespace App\Services;

use App\Models\ProductLog;
use App\Models\QaidReprint;
use App\Models\WorkStatus;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Throwable;

class QaidService
{
    protected WorkStatusService $workStatusService;
    protected TelegramService $telegram;

    /**
     * @throws Exception
     */
    public function __construct(
        WorkStatusService $workStatusService,
        TelegramService $telegram,
    )
    {
        $this->workStatusService = $workStatusService;
        $this->telegram = $telegram;
    }

    /**
     * 현재 인증된 사용자 ID를 가져옵니다.
     *
     * @return int
     * @throws Exception
     */
    protected function getUserId(): int
    {
        $userId = auth()->id();
        if (!$userId) {
            throw new Exception("사용자 정보가 없습니다.\n\n로그인 후 이용해 주세요.");
        }

        return $userId;
    }

    /**
     * 현재 인증된 사용자 이름을 가져옵니다.
     *
     * @return string
     * @throws Exception
     */
    protected function getUserName(): string
    {
        $user = auth()->user();
        if (!$user) {
            throw new Exception("사용자 정보가 없습니다.\n\n로그인 후 이용해 주세요.");
        }

        return $user->name;
    }

    /**
     * QAID 리스트
     */
    public function getList(array $data): Builder
    {
        $query = QaidReprint::with([
            'user:id,name',
            'latestProduct:id,qaid,name',
        ])
        ->select(['qaid_reprints.*']);

        // 날짜 조건 추가
        $beginAt = !empty($data['beginAt']) ? $data['beginAt'] : now()->startOfMonth()->toDateString();
        $endAt = !empty($data['endAt']) ? $data['endAt'] : now()->toDateString();

        $query->whereBetween('date', [$beginAt, $endAt]);


        // 키워드 조건 추가
        if (!empty($data['keyword'])) {
            $keyword = $data['keyword'];
            $query->where(function ($subQuery) use ($keyword) {
                $subQuery->where('qaid', $keyword)
                    ->orWhereHas('user', function ($q) use ($keyword) {
                        $q->where('name', 'like', '%' . $keyword . '%');
                    });
            });
        }

        // 정렬 조건 추가
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * QAID 중복 검사
     *
     * @param string $qaid
     * @return array [중복 여부, 중복된 날짜] 배열 반환
     */
    public function isDuplicate(string $qaid): array
    {
        $response = [
            'is_duplicate' => false,
            'date' => '',
            'print_count' => 0,
        ];

        $duplicate = QaidReprint::where('qaid', $qaid)->first();
        if ($duplicate) {
            $response = [
                'is_duplicate' => true,
                'date' => $duplicate->date->format('Y-m-d'),
                'print_count' => $duplicate->print_count,
            ];
        }

        return $response;
    }

    /**
     * QAID 저장
     * @param  string  $qaid 저장할 qaid
     * @param  int  $id 상품 id(index)
     * @return void
     * @throws Throwable
     */
    public function storeQaid(string $qaid, int $id): void
    {
        DB::beginTransaction();
        try {
            $rePrint = QaidReprint::where('qaid', $qaid)->first();
            if ($rePrint) {
                throw new Exception("이미 존재하는 QAID입니다: $qaid");
            }

            $qaidReprint = QaidReprint::create([
                'date' => now()->format('Y-m-d'),
                'qaid' => $qaid,
                'user_id' => $this->getUserId(),
                'print_count' => 1,
            ]);

            $this->createProductLog($qaidReprint, $qaid, $id);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * QAID 재발행(2번째 발행 부터는 관리자에게 메시지를 보낸다.)
     * @param  string  $qaid
     * @param  int  $id
     * @return QaidReprint|\Eloquent
     * @throws Throwable
     */
    public function updateQaid(string $qaid, int $id): \Eloquent|QaidReprint
    {
        DB::beginTransaction();
        try {
            $rePrint = QaidReprint::where('qaid', $qaid)->first();
            if (!$rePrint) {
                throw new Exception("존재하지 않는 QAID입니다: $qaid");
            }

            $rePrint->print_count++;
            $rePrint->save();

            $this->createProductLog($rePrint, $qaid, $id);

            DB::commit();

            $this->telegram->sendMessageToTeam("QAID [$qaid] 재발행 {$rePrint->print_count}회차\n재발행한 사람: {$this->getUserName()}", 'admin');

            return $rePrint;
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * 상품 로그 생성
     * @param $model
     * @param $qaid
     * @param $id
     * @return void
     * @throws Exception
     */
    private function createProductLog($model, $qaid, $id): void
    {
        $statusIds = $this->workStatusService->getIds([
            WorkStatus::LINK_QAID_REPRINT, # QAID 재발행
        ]);

        $now = now();
        ProductLog::insert([
            'product_id' => $id,
            'model_type' => 'App\Models\QaidReprint',
            'model_id' => $model->id,
            'work_status_id' => $statusIds[WorkStatus::LINK_QAID_REPRINT],
            'user_id' => $this->getUserId(),
            'memo' => "QAID [$qaid] 재발행 {$model->print_count}회차",
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }
}
