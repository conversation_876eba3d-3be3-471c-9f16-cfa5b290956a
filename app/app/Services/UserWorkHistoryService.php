<?php

namespace App\Services;

use App\Helpers\PaginationHelper;
use App\Http\Resources\UserWorkLogResource;
use App\Models\ProductLog;
use App\Models\User;
use Illuminate\Support\Carbon;

class UserWorkHistoryService
{
    private const MODEL_TYPE = "App\\Models\\RepairProduct";

    /**
     * 사용자의 오늘 작업 내역을 조회합니다.
     *
     * @param ?User $user
     * @param string|null $date 날짜 (Y-m-d 형식, null이면 오늘)
     * @param int $pageSize 페이지당 항목 수
     * @return array
     */
    public function getTodayWorkHistory(?User $user, ?string $date = null, int $pageSize = 15): array
    {
        $targetDate = $date ? Carbon::parse($date) : Carbon::today();

        // 오늘 생성된 RepairProduct 관련 로그들 조회
        $query = ProductLog::with([
            'product:id,qaid,name',
            'product.repairProduct.repairGrade:id,name',
        ])
            ->where('model_type', self::MODEL_TYPE)
            ->where('user_id', $user->id)
            ->whereDate('created_at', $targetDate)
            ->orderBy('created_at', 'desc');

        // 페이징 처리
        $paginatedResult = $query->paginate($pageSize)->withQueryString();

        return [
            'work_logs' => UserWorkLogResource::collection($paginatedResult->items()),
            'pagination' => PaginationHelper::optimize($paginatedResult),
            'summary' => [
                'total_count' => $paginatedResult->total(),
                'today_date' => $targetDate->format('Y-m-d'),
            ]
        ];
    }

    /**
     * 사용자의 특정 기간 작업 내역을 조회합니다.
     *
     * @param User $user
     * @param string $startDate 시작 날짜 (Y-m-d 형식)
     * @param string $endDate 종료 날짜 (Y-m-d 형식)
     * @param int $pageSize 페이지당 항목 수
     * @return array
     */
    public function getWorkHistoryByDateRange(User $user, string $startDate, string $endDate, int $pageSize = 15): array
    {
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        $query = ProductLog::with([
            'product:id,qaid,name',
            'product.repairProduct.repairGrade:id,name',
        ])
            ->where('model_type', self::MODEL_TYPE)
            ->where('user_id', $user->id)
            ->whereBetween('created_at', [$start, $end])
            ->orderBy('created_at', 'desc');

        // 페이징 처리
        $paginatedResult = $query->paginate($pageSize)->withQueryString();

        return [
            'work_logs' => UserWorkLogResource::collection($paginatedResult->items()),
            'pagination' => PaginationHelper::optimize($paginatedResult),
            'summary' => [
                'total_count' => $paginatedResult->total(),
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]
        ];
    }

    /**
     * 사용자의 작업 통계를 조회합니다.
     *
     * @param User $user
     * @param int $days 조회할 일수 (기본값: 7일)
     * @return array
     */
    public function getWorkStatistics(User $user, int $days = 7): array
    {
        $startDate = Carbon::today()->subDays($days - 1);
        $endDate = Carbon::today();

        $workLogs = ProductLog::where('model_type', self::MODEL_TYPE)
            ->where('user_id', $user->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        // 일별 작업 수 집계
        $dailyStats = [];
        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dailyStats[$date->format('Y-m-d')] = 0;
        }

        foreach ($workLogs as $log) {
            $dateKey = $log->created_at->format('Y-m-d');
            if (isset($dailyStats[$dateKey])) {
                $dailyStats[$dateKey]++;
            }
        }

        return [
            'daily_stats' => $dailyStats,
            'total_work_count' => $workLogs->count(),
            'average_daily_work' => $days > 0 ? round($workLogs->count() / $days, 1) : 0,
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $days,
            ]
        ];
    }
}
