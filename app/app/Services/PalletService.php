<?php

namespace App\Services;

use App\Helpers\CacheHelper;
use App\Models\DeleteLog;
use App\Models\Location;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\Product;
use App\Models\User;
use App\Models\WorkStatus;
use App\Traits\Repair\GradeTrait;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Throwable;

class PalletService
{
    use GradeTrait;

    protected WorkStatusService $workStatusService;
    protected CountService $countService;
    protected ProductLogService $logger;

    public function __construct(
        WorkStatusService $workStatusService,
        CountService $countService,
        ProductLogService $logger
    )
    {
        $this->workStatusService = $workStatusService;
        $this->countService = $countService;
        $this->logger = $logger;
    }

    public function getPallet($id): Pallet|null
    {
        return Pallet::without('')->findOrFail($id);
    }

    /**
     * 팔레트 출고
     * @throws Exception|Throwable
     */
    public function updateExportPallets(array $palletIds, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_EXPORTED, # 팔레트 출고
                WorkStatus::LINK_PALLET_PRODUCT_EXPORTED, # 팔레트 상품 출고
            ]);

            $now = now();
            $pallets = Pallet::with(['palletProducts.product', 'location'])
                ->whereIn('id', $palletIds)
                ->get();
            foreach ($pallets as $pallet) {
                if ($pallet->status !== Pallet::STATUS_CLOSED) {
                    SimpleLogService::warning('pallet', '팔레트 출고 상태 오류', [
                        'pallet_id' => $pallet->id,
                        'current_status' => $pallet->status,
                        'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
                        'user_id' => $user->id,
                    ]);

                    throw new Exception("쿠팡RP팔레트는 출고할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.", 400);
                }

                $pallet->update([
                    'status' => Pallet::STATUS_EXPORTED,
                    'checked_at' => $now->copy()->format('Y-m-d H:i:s'),
                    'checked_user_id' => $user->id,
                ]);

                $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_EXPORTED], $user->id, "팔레트[{$pallet->location->name}]: 출고");

                $pallet->location->update(['enable' => 'N']);

                $counters = []; # 각종 카운터 저장용
                foreach ($pallet->palletProducts as $palletProduct) {
                    $palletProduct->update(['status' => PalletProduct::STATUS_EXPORTED]);

                    // 잠금이 되어 있는지 확인
                    $product = $palletProduct->product;
                    if ($product->isLocked()) {
                        throw new Exception("잠금된 상품입니다. ($product->memo) 관리자에게 문의하세요. (QAID: {$product->qaid})");
                    }
                    $product->update(['status' => Product::STATUS_EXPORTED]);

                    // 상태 변환/통계::팔레트 출고에 따른 상품 출고
                    $this->logger->addLog(
                        $product,
                        'App\Models\PalletProduct',
                        $palletProduct->id,
                        $statusIds[WorkStatus::LINK_PALLET_PRODUCT_EXPORTED],
                        $user->id,
                        "QAID[$product->qaid]: 출고"
                    );

                    // 카운터 데이터를 배열에 미리 저장
                    $counters = $this->countService->initCounter($product->req_id, $counters);
                    $counters[$product->req_id]['exporting'] -= 1;
                    $counters[$product->req_id]['completed'] += 1;
                }

                $this->countService->multipleUpdate($counters);
            }
            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '팔레트 출고 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 출고 취소
     * @throws Exception|Throwable
     */
    public function updateRollbackExportPallets(array $palletIds, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_CLOSED, # 팔레트 적재 마감(출고 대기)
                WorkStatus::LINK_PALLET_PRODUCT_REGISTERED, # 팔레트에 상품 적재
            ]);

            $pallets = Pallet::with(['palletProducts.product', 'location'])
                ->whereIn('id', $palletIds)
                ->get();

            foreach ($pallets as $pallet) {
                if ($pallet->status !== Pallet::STATUS_EXPORTED) {
                    SimpleLogService::warning('pallet', '팔레트 출고취소 상태 오류', [
                        'pallet_id' => $pallet->id,
                        'current_status' => $pallet->status,
                        'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
                        'user_id' => $user->id,
                    ]);

                    throw new Exception("쿠팡RP 팔레트는 출고 취소할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.", 400);
                }

                // 팔레트 상태 체크/업데이트 등
                $pallet->update([
                    'status' => Pallet::STATUS_CLOSED,
                    'checked_at' => null,
                    'checked_user_id' => null,
                    'exported_at' => null,
                    'exported_user_id' => null,
                ]);

                $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_CLOSED], $user->id, "팔레트[{$pallet->location->name}]: 출고 취소");

                // 위치 업데이트
                $pallet->location->update(['enable' => 'Y']);

                $counters = [];
                foreach ($pallet->palletProducts as $palletProduct) {
                    // 각 상품별로 반드시 개별 로그 기록
                    $palletProduct->update(['status' => PalletProduct::STATUS_REGISTERED]);

                    // 잠금이 되어 있는지 확인
                    $product = $palletProduct->product;
                    if ($product->isLocked()) {
                        throw new Exception("잠금된 상품입니다. ($product->memo) 관리자에게 문의하세요. (QAID: {$product->qaid})");
                    }
                    $product->update(['status' => Product::STATUS_CHECKED_ON_PALLET]);

                    // 상태 변환/통계::점검완료(적재중)
                    $this->logger->addLog(
                        $product,
                        'App\Models\PalletProduct',
                        $palletProduct->id,
                        $statusIds[WorkStatus::LINK_PALLET_PRODUCT_REGISTERED],
                        $user->id,
                        "QAID[$product->qaid]: 출고 취소"
                    );

                    // 카운터 집계
                    $counters = $this->countService->initCounter($product->req_id, $counters);
                    $counters[$product->req_id]['exporting'] += 1;
                    $counters[$product->req_id]['completed'] -= 1;
                }

                $this->countService->multipleUpdate($counters);
            }
            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '출고된 팔레트 취소 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 출고 날짜 저장
     * @throws Exception|Throwable
     */
    public function updatePalletExportDate(array $rows, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_EXPORTED_DATE_SAVE,
            ]);

            foreach ($rows as $row) {
                $pallet = Pallet::find($row['palletId']);

                if (!$pallet) {
                    SimpleLogService::error('pallet', '팔레트가 존재하지 않습니다.', ['data' => $row]);
                    throw new Exception("팔레트가 존재하지 않습니다.", 404);
                }

                if ($pallet->status !== Pallet::STATUS_EXPORTED) {
                    SimpleLogService::warning('pallet', '팔레트 출고날짜 저장 상태 오류', [
                        'pallet' => $pallet,
                        'user' => $user,
                    ]);
                    throw new Exception("쿠팡RP팔레트는 출고날짜를 저장할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.", 400);
                }

                $pallet->update([
                    'exported_at' => Carbon::createFromFormat('Y-m-d', $row['exportDate'])
                        ->setTimeFrom(now()),
                    'exported_user_id' => $user->id,
                ]);

                $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_EXPORTED_DATE_SAVE], $user->id, "팔레트[{$pallet->location->name}]: 출고날짜 저장");
            }

            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '팔레트 출고날짜 저장 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 출고검수
     * @param $data
     * @param  User  $user
     * @return Product
     * @throws Throwable
     */
    public function patchDeliveryInspection($data, User $user): Product
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_PRODUCT_INSPECT_PASS, # 팔레트 상품 검수 완료
            ]);

            $qaid = mb_strtoupper($data['qaid']);
            $product = Product::where('qaid', $qaid)
                ->where('status', Product::STATUS_CHECKED_ON_PALLET)
                ->first();

            if ($product === null) {
                SimpleLogService::warning('pallet', '출고검수 대상 상품 없음', [
                    'qaid' => $qaid,
                    'pallet_id' => $data['pallet_id'] ?? null,
                    'pallet_code' => $data['pallet_code'] ?? null,
                    'user_id' => $user->id,
                ]);
                throw new Exception("등록상품[ $qaid ]이 없습니다.", 400);
            }

            // 잠금이 되어 있는지 확인
            if ($product->isLocked()) {
                throw new Exception("잠금된 상품입니다. ($product->memo) 관리자에게 문의하세요. (QAID: {$product->qaid})");
            }

            // 팔레트상품
            $palletProduct = PalletProduct::where('product_id', $product->id)
                ->where('pallet_id', (int) $data['pallet_id'])
                ->where('status', PalletProduct::STATUS_REGISTERED)
                ->first();
            if ($palletProduct === null) {
                throw new Exception("해당 팔레트 [ {$data['pallet_code']} ]에 출고 미검수상태의 적재상품 [ $qaid ]이 없습니다.", 400);
            }

            if ($palletProduct->checked_status === PalletProduct::CHECK_STATUS_CHECKED) {
                throw new Exception("해당 팔레트 [ {$data['pallet_code']} ]에 적재상품 [ $qaid ]은 이미 검수 완료된 상태입니다.", 400);
            }

            $previousStatus = PalletProduct::$CHECK_STATUS_NAME[$palletProduct->checked_status];

            $now = now();
            $palletProduct->update([
                'checked_status' => PalletProduct::CHECK_STATUS_CHECKED,
                'checked_at' => $now->copy()->format('Y-m-d H:i:s'),
                'checked_user_id' => $user->id,
            ]);

            $currentStatus = PalletProduct::$CHECK_STATUS_NAME[$palletProduct->checked_status];

            // 상태 변환/통계::출고상품 미검수->검수완료
            $this->logger->addLog(
                $product,
                'App\Models\PalletProduct',
                $palletProduct->id,
                $statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT_PASS],
                $user->id,
                "팔레트 출고 검수 완료: [{$data['pallet_id']} - {$data['pallet_code']}]의 {$palletProduct->id} 상품 출고 상태: $previousStatus -> $currentStatus"
            );

            $this->logger->save();

            DB::commit();

            return $product;
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '팔레트 출고검수 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 출고대기(마감)
     * 팔레트의 마감(더 이상 적재하지 못하도록)만 하는 것 같음.
     * 따라서 출고검수와는 무관하다고 판단함.
     * 결론: 카운트는 적재중(점검완료)->출고대기(마감)으로 변경, 히스토리에 기록하지 않음
     *
     * @throws Exception|Throwable
     */
    public function updateClosePallet(array$data, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_CLOSED, #팔레트 적재 마감(출고 대기)
                WorkStatus::LINK_PALLET_PRODUCT_INSPECT_PASS, # 팔레트 상품 검수 완료
            ]);

            $pallet = Pallet::findOrFail((int)$data['pallet_id']);
            $pallet->load(['palletProducts.product']);
            if ($pallet->status > Pallet::STATUS_LOADED) {
                SimpleLogService::warning('pallet', '팔레트 마감 상태 오류', [
                    'pallet_id' => $pallet->id,
                    'current_status' => $pallet->status,
                    'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
                    'user_id' => $user->id,
                ]);
                throw new Exception("쿠팡RP팔레트는 마감할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.", 400);
            }

            $palletProducts = $pallet->palletProducts;

            $counters = [];
            foreach ($palletProducts as $palletProduct) {
                $product = $palletProduct->product; # 관계 객체가 아닌 실제 모델 인스턴스를 가져옴

                // 상태 변환/통계::출고상품 미검수->검수완료
                $this->logger->addLog($product, 'App\Models\PalletProduct', $palletProduct->id, $statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT_PASS], $user->id, "QAID[$product->qaid]: 출고상품 검수 완료(팔레트 마감)");

                // 카운터 기록
                // 적재중에서 출고대기로 넘어오기 때문에 팔레트안의 모든 상품에 대해
                // 적재중(점검완료)->출고대기(마감) 으로 변경해 줘야 함
                $counters = $this->countService->initCounter($product->req_id, $counters);
                $counters[$product->req_id]['checkout'] -= 1;
                $counters[$product->req_id]['exporting'] += 1;
            }

            // 카운팅 후 팔레트를 점검마감(출고대기)로 상태 변경
            $pallet->status = Pallet::STATUS_CLOSED;
            $pallet->update();

            // 상태 변환/통계::팔레트 적재마감(출고대기)
            $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_CLOSED], $user->id, "팔레트 적재 마감(출고 대기)");

            $this->logger->save();
            $this->countService->multipleUpdate($counters);

            Location::where('id', $pallet->location_id)->update(['enable' => 'N']);

            // 팔레트에 상품이 추가되면 관련 캐시 무효화
            CacheHelper::forgetCache('loaded_pallets_data');

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '팔레트 마감 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 팔레트의 마감을 취소(다시 적재할 수 있도록)만 하는 것 같음.
     * 따라서 출고검수와는 무관하다고 판단함.
     * 결론: 카운트는 출고대기(마감)->적재중(점검완료)으로 변경, 히스토리에 기록하지 않음
     *
     * @throws Exception|Throwable
     */
    public function updateOpenPallet(array $data, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_OPEN, #팔레트 적재중
                WorkStatus::LINK_PALLET_PRODUCT_INSPECT, # 팔레트 상품 미검수
            ]);

            $pallet = Pallet::findOrFail((int)$data['pallet_id']);
            $pallet->load(['palletProducts.product']);
            if ($pallet->status !== Pallet::STATUS_CLOSED) {
                SimpleLogService::warning('pallet', '팔레트 마감취소 상태 오류', [
                    'pallet_id' => $pallet->id,
                    'current_status' => $pallet->status,
                    'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
                    'user_id' => $user->id,
                ]);
                throw new Exception("쿠팡RP팔레트는 마감취소 할 수 없는 상태(" . Pallet::$STATUS_NAME[$pallet->status] . ")입니다.", 400);
            }

            $palletProducts = $pallet->palletProducts;

            $counters = [];
            foreach ($palletProducts as $palletProduct) {
                $product = $palletProduct->product;

                // 상태 변환/통계::출고상품 검수완료->미검수
                $this->logger->addLog($product, 'App\Models\PalletProduct', $palletProduct->id, $statusIds[WorkStatus::LINK_PALLET_PRODUCT_INSPECT], $user->id, "QAID[$product->qaid]: 출고상품 미검수(팔레트 마감 취소)");

                // 카운터 기록
                // 출고대기에서 다시 적재중으로 넘어가기 때문에 팔레트안의 모든 상품에 대해
                // 출고대기(마감)->적재중(점검완료) 으로 변경해 줘야 함
                $counters = $this->countService->initCounter($product->req_id, $counters);
                $counters[$product->req_id]['checkout'] += 1;
                $counters[$product->req_id]['exporting'] -= 1;
            }

            // 팔레트를 점검완료(적재중)로 상태 변경
            $pallet->status = Pallet::STATUS_LOADED;
            $pallet->update();

            // 상태 변환/통계::팔레트 적재중
            $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_OPEN], $user->id, "팔레트 적재중");

            $this->logger->save();
            $this->countService->multipleUpdate($counters);

            Location::where('id', $pallet->location_id)->update(['enable' => 'Y']);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('pallet', '팔레트 마감 취소 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 상품 점검 취소<br>
     * 점검이 취소되므로 상품은 팔레트에서 삭제해야 한다.
     * @throws Exception|Throwable
     */
    public function excludeFromPallet(array $productIds, User $user): void
    {
        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_PRODUCT_DELETE, #팔레트에서 상품 삭제
                WorkStatus::LINK_REPAIR_COMPLETE, # 점검/수리 완료
            ]);

            $counters = [];
            $deleteLogs = [];

            $palletProducts = PalletProduct::with('product')
                ->whereIn('product_id', $productIds)
                ->get();
            $now = now();
            foreach ($palletProducts as $palletProduct) {
                $product = $palletProduct->product;

                /**
                 * 점검완료(적재중) 카운팅 - checkout - 1
                 * 점검/수리 완료 : repaired + 1, 점검완료(적재중) : checkout - 1
                 */
                $counters = $this->countService->initCounter($product->req_id, $counters);
                $counters[$product->req_id]['repaired']++;
                $counters[$product->req_id]['checkout']--;

                // 상품 상태 업데이트(점검/수리 완료 상태로 변경) 실행
                $product->update([
                    'status' => Product::STATUS_REPAIRED
                ]);

                // 상태 변환/통계::팔레트에서 상품 삭제
                $this->logger->addLog($product, 'App\Models\PalletProduct', $palletProduct->id, $statusIds[WorkStatus::LINK_PALLET_PRODUCT_DELETE], $user->id, "QAID[$product->qaid]: 팔레트에서 상품 삭제");

                // 상태 변환/통계::(입고)검수완료
                $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusIds[WorkStatus::LINK_REPAIR_COMPLETE], $user->id, "QAID[$product->qaid]: 점검 취소로 인해 수리/점검 완료로 상태 변경 됨");

                $deleteLogs[] = [
                    'user_id' => $user->id,
                    'deletable_type' => 'App\Models\PalletProduct',
                    'deletable_id' => $palletProduct->id,
                    'content' => $palletProduct,
                    'ip' => request()->getClientIp(),
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            if (!empty($deleteLogs)) {
                DeleteLog::insert($deleteLogs);
            }
            $this->logger->save();
            $this->countService->multipleUpdate($counters);

            // 실제 팔레트에서 상품 삭제
            PalletProduct::whereIn('product_id', $productIds)->delete();

            // 출고상품 적재 시 팔레트 리스트 캐시 제거(LoadedService::CACHE_KEY)
            CacheHelper::forgetCache('loaded_pallets_data');

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            // 시스템 오류인 경우에만 error 레벨로 로깅
            SimpleLogService::error('pallet', '팔레트상품 점검 취소 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 팔레트 저장
     * @throws Exception|Throwable
     */
    public function store(Location $location, array $data, User $user): Model|Pallet
    {
        if (empty($data['grade_id'])) {
            throw new Exception("수리 등급이 누락되었습니다.", 400);
        }

        $now = now();
        $formattedNow = $now->format('Y-m-d H:i:s');
        $repairGradeId = $data['grade_id'];

        $pallet = Pallet::firstOrNew(['location_id' => $location->id]);
        if ($pallet->exists) {
            if ($pallet->status >= Pallet::STATUS_CLOSED) {
                SimpleLogService::warning('pallet', '팔레트 적재불가능 상태 오류', [
                    'pallet_id' => $pallet->id,
                    'current_status' => $pallet->status,
                    'status_name' => Pallet::$STATUS_NAME[$pallet->status] ?? '알 수 없음',
                    'location_id' => $location->id,
                    'user_id' => $user->id,
                ]);
                throw new Exception("팔래트 상태(적재 불가능)를 확인해 주시기 바랍니다.", 400);
            }

            // XL의 여러 등급(XL1, XL2, ...)은 같은 XL 등급으로 본다.
            $palletGrade = $this->getGradeCodeById($pallet->repair_grade_id);
            $repairGrade = $this->getGradeCodeById($repairGradeId);

            $palletGradeCode = $palletGrade?->code;
            $repairGradeCode = $repairGrade?->code;

            if ($palletGradeCode === null || $repairGradeCode === null) {
                throw new Exception("팔레트의 등급 또는 적재하려는 수리 등급이 존재하지 않습니다.", 404);
            }

            // repairGradeCode 정규화: ST_XL*, ST_XL → ST_XL
            $normalizedRepairCode = (is_string($repairGradeCode) && str_starts_with($repairGradeCode, 'ST_XL'))
                ? 'ST_XL'
                : $repairGradeCode;
            if ($normalizedRepairCode !== $palletGradeCode) {
                SimpleLogService::warning('pallet', '팔레트 등급 불일치 오류', [
                    'pallet_id' => $pallet->id,
                    'pallet_grade_id' => $pallet->repair_grade_id,
                    'product_grade_id' => $repairGradeId,
                    'location_id' => $location->id,
                    'user_id' => $user->id,
                ]);
                throw new Exception("팔레트의 등급과 적재하려는 수리 등급이 다릅니다.", 400);
            }

            $pallet->status = Pallet::STATUS_LOADED;
            $pallet->memo = (string)$pallet->memo . "\n[$formattedNow] 쿠팡PL 팔레트 적재";

            $pallet->save();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_OPEN, # 팔레트 적재중
            ]);

            $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_OPEN], $user->id, "팔레트 적재중");
        } else {
            $pallet->repair_grade_id = $repairGradeId;
            $pallet->registered_at = $now;
            $pallet->registered_user_id = $user->id ?? 0;
            $pallet->memo = "[$formattedNow] 쿠팡PL 팔레트 등록";
            $pallet->status = Pallet::STATUS_LOADED;

            $pallet->save();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PALLET_CREATE, # 팔레트 생성
                WorkStatus::LINK_PALLET_OPEN, # 팔레트 적재중
            ]);

            $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_CREATE], $user->id, "팔레트 생성");
            $this->logger->addLog(null, 'App\Models\Pallet', $pallet->id, $statusIds[WorkStatus::LINK_PALLET_OPEN], $user->id, "팔레트 적재중");
        }

        // 로그 저장은 호출자(storeProductOnPallet) 에처 처리 됨

        return $pallet;
    }
}
