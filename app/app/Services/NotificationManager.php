<?php

namespace App\Services;

use App\Models\SseOfflineNotification;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;
use Exception;

/**
 * SSE 알림 관리 서비스
 * 
 * 전체/개별 사용자에게 실시간 알림을 전송하고
 * 오프라인 사용자를 위한 알림 저장을 관리합니다.
 */
class NotificationManager
{
    private ConnectionManager $connectionManager;
    private MessageFormatterService $messageFormatter;
    private SseSecurityService $securityService;
    private SseLogger $logger;
    
    private const MESSAGE_CHANNEL = 'sse:messages';
    private const BATCH_SIZE = 100;
    private const MAX_OFFLINE_NOTIFICATIONS = 1000;

    public function __construct(
        ConnectionManager $connectionManager,
        MessageFormatterService $messageFormatter,
        SseSecurityService $securityService,
        SseLogger $logger
    ) {
        $this->connectionManager = $connectionManager;
        $this->messageFormatter = $messageFormatter;
        $this->securityService = $securityService;
        $this->logger = $logger;
    }

    /**
     * 모든 연결된 사용자에게 알림을 브로드캐스트합니다.
     *
     * @param array $data 알림 데이터
     * @param string $type 메시지 타입 (기본값: 'notification')
     * @return array 전송 결과 통계
     */
    public function sendToAll(array $data, string $type = 'notification'): array
    {
        try {
            // 1. 메시지 보안 검증
            if (!$this->securityService->validateMessageContent($data)) {
                $this->logger->logSecurityEvent('invalid_broadcast_message', '브로드캐스트 메시지 보안 검증 실패');
                return [
                    'total_connections' => 0,
                    'successful_sends' => 0,
                    'failed_sends' => 0,
                    'error' => 'Message validation failed'
                ];
            }
            
            // 2. 민감 정보 필터링
            $filteredData = $this->securityService->filterSensitiveData($data);
            
            $activeConnections = $this->connectionManager->getActiveConnections();
            
            if (empty($activeConnections)) {
                $this->logger->info('전체 알림 전송: 활성 연결이 없습니다');
                return [
                    'total_connections' => 0,
                    'successful_sends' => 0,
                    'failed_sends' => 0
                ];
            }

            $formattedMessage = $this->messageFormatter->formatNotificationMessage($filteredData);
            $message = $this->messageFormatter->serializeMessage($formattedMessage);
            $result = $this->sendToConnections($activeConnections, $message);

            $this->logger->logBroadcast($type, $filteredData, count($activeConnections));

            return $result;

        } catch (Exception $e) {
            $this->logger->logError('전체 알림 브로드캐스트 실패', $e, [
                'type' => $type
            ]);
            
            return [
                'total_connections' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 특정 사용자에게 개별 알림을 전송합니다.
     *
     * @param int $userId 대상 사용자 ID
     * @param array $data 알림 데이터
     * @param string $type 메시지 타입 (기본값: 'notification')
     * @return array 전송 결과
     */
    public function sendToUser(int $userId, array $data, string $type = 'notification'): array
    {
        try {
            $userConnections = $this->connectionManager->getUserConnections($userId);
            
            // 사용자가 온라인인 경우 실시간 전송
            if (!empty($userConnections)) {
                $formattedMessage = $this->messageFormatter->formatNotificationMessage($data);
                $message = $this->messageFormatter->serializeMessage($formattedMessage);
                $result = $this->sendToConnections($userConnections, $message);
                
                Log::info('개별 알림 전송 완료', [
                    'user_id' => $userId,
                    'type' => $type,
                    'connections' => count($userConnections),
                    'successful_sends' => $result['successful_sends']
                ]);

                return array_merge($result, ['delivered_online' => true]);
            }

            // 사용자가 오프라인인 경우 데이터베이스에 저장
            $this->storeOfflineNotification($userId, $data, $type);
            
            Log::info('오프라인 알림 저장 완료', [
                'user_id' => $userId,
                'type' => $type
            ]);

            return [
                'total_connections' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'delivered_online' => false,
                'stored_offline' => true
            ];

        } catch (Exception $e) {
            Log::error('개별 알림 전송 실패', [
                'user_id' => $userId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_connections' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'delivered_online' => false,
                'stored_offline' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 여러 사용자에게 개별 알림을 일괄 전송합니다.
     *
     * @param array $userIds 대상 사용자 ID 배열
     * @param array $data 알림 데이터
     * @param string $type 메시지 타입 (기본값: 'notification')
     * @return array 전송 결과 통계
     */
    public function sendToUsers(array $userIds, array $data, string $type = 'notification'): array
    {
        $totalResults = [
            'total_users' => count($userIds),
            'online_users' => 0,
            'offline_users' => 0,
            'successful_sends' => 0,
            'failed_sends' => 0,
            'stored_offline' => 0
        ];

        // 배치 단위로 처리
        $batches = array_chunk($userIds, self::BATCH_SIZE);
        
        foreach ($batches as $batch) {
            foreach ($batch as $userId) {
                $result = $this->sendToUser($userId, $data, $type);
                
                if ($result['delivered_online']) {
                    $totalResults['online_users']++;
                    $totalResults['successful_sends'] += $result['successful_sends'];
                    $totalResults['failed_sends'] += $result['failed_sends'];
                } else {
                    $totalResults['offline_users']++;
                    if ($result['stored_offline'] ?? false) {
                        $totalResults['stored_offline']++;
                    }
                }
            }
        }

        Log::info('다중 사용자 알림 전송 완료', [
            'type' => $type,
            'results' => $totalResults
        ]);

        return $totalResults;
    }

    /**
     * 지정된 연결들에게 메시지를 전송합니다.
     *
     * @param array $connectionIds 연결 ID 배열
     * @param string $message 전송할 메시지
     * @return array 전송 결과 통계
     */
    public function sendToConnections(array $connectionIds, string $message): array
    {
        $successfulSends = 0;
        $failedSends = 0;

        try {
            // Redis Pub/Sub을 통한 메시지 전송
            foreach ($connectionIds as $connectionId) {
                try {
                    $messageData = [
                        'connection_id' => $connectionId,
                        'message' => $message,
                        'timestamp' => Carbon::now()->toISOString()
                    ];

                    Redis::publish(self::MESSAGE_CHANNEL, json_encode($messageData));
                    $successfulSends++;

                } catch (Exception $e) {
                    $failedSends++;
                    Log::warning('개별 연결 메시지 전송 실패', [
                        'connection_id' => $connectionId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

        } catch (Exception $e) {
            Log::error('연결 메시지 전송 중 오류 발생', [
                'error' => $e->getMessage()
            ]);
            $failedSends = count($connectionIds);
        }

        return [
            'total_connections' => count($connectionIds),
            'successful_sends' => $successfulSends,
            'failed_sends' => $failedSends
        ];
    }

    /**
     * 오프라인 사용자를 위해 알림을 데이터베이스에 저장합니다.
     *
     * @param int $userId 사용자 ID
     * @param array $data 알림 데이터
     * @param string $type 알림 타입
     * @return bool 저장 성공 여부
     */
    public function storeOfflineNotification(int $userId, array $data, string $type = 'notification'): bool
    {
        try {
            // 사용자별 오프라인 알림 수 제한 확인
            $existingCount = SseOfflineNotification::forUser($userId)
                ->undelivered()
                ->count();

            if ($existingCount >= self::MAX_OFFLINE_NOTIFICATIONS) {
                // 가장 오래된 알림 삭제
                $oldestNotifications = SseOfflineNotification::forUser($userId)
                    ->undelivered()
                    ->orderBy('created_at')
                    ->limit($existingCount - self::MAX_OFFLINE_NOTIFICATIONS + 1)
                    ->get();

                foreach ($oldestNotifications as $notification) {
                    $notification->delete();
                }

                Log::warning('오프라인 알림 수 제한으로 인한 오래된 알림 삭제', [
                    'user_id' => $userId,
                    'deleted_count' => $oldestNotifications->count()
                ]);
            }

            // 새 알림 저장
            SseOfflineNotification::create([
                'user_id' => $userId,
                'type' => $type,
                'data' => $data,
                'created_at' => Carbon::now()
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('오프라인 알림 저장 실패', [
                'user_id' => $userId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 사용자의 미전달 오프라인 알림을 조회합니다.
     *
     * @param int $userId 사용자 ID
     * @param int $limit 조회할 최대 알림 수 (기본값: 100)
     * @return array 미전달 알림 배열
     */
    public function getOfflineNotifications(int $userId, int $limit = 100): array
    {
        try {
            $notifications = SseOfflineNotification::forUser($userId)
                ->undelivered()
                ->orderBy('created_at')
                ->limit($limit)
                ->get();

            return $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at->toISOString()
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error('오프라인 알림 조회 실패', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 사용자 로그인 시 미전달 알림을 전송합니다.
     *
     * @param int $userId 사용자 ID
     * @return array 전송 결과
     */
    public function deliverOfflineNotifications(int $userId): array
    {
        try {
            $notifications = $this->getOfflineNotifications($userId);
            
            if (empty($notifications)) {
                return [
                    'total_notifications' => 0,
                    'delivered_count' => 0,
                    'failed_count' => 0
                ];
            }

            $deliveredCount = 0;
            $failedCount = 0;
            $userConnections = $this->connectionManager->getUserConnections($userId);

            if (empty($userConnections)) {
                Log::warning('오프라인 알림 전송 시도했으나 사용자가 오프라인 상태', [
                    'user_id' => $userId
                ]);
                return [
                    'total_notifications' => count($notifications),
                    'delivered_count' => 0,
                    'failed_count' => 0,
                    'user_offline' => true
                ];
            }

            foreach ($notifications as $notificationData) {
                try {
                    $formattedMessage = $this->messageFormatter->formatNotificationMessage($notificationData['data']);
                    $message = $this->messageFormatter->serializeMessage($formattedMessage);
                    $result = $this->sendToConnections($userConnections, $message);

                    if ($result['successful_sends'] > 0) {
                        // 알림을 전달 완료로 표시
                        $notification = SseOfflineNotification::find($notificationData['id']);
                        if ($notification) {
                            $notification->markAsDelivered();
                        }
                        $deliveredCount++;
                    } else {
                        $failedCount++;
                    }

                } catch (Exception $e) {
                    $failedCount++;
                    Log::error('개별 오프라인 알림 전송 실패', [
                        'notification_id' => $notificationData['id'],
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('오프라인 알림 전송 완료', [
                'user_id' => $userId,
                'total_notifications' => count($notifications),
                'delivered_count' => $deliveredCount,
                'failed_count' => $failedCount
            ]);

            return [
                'total_notifications' => count($notifications),
                'delivered_count' => $deliveredCount,
                'failed_count' => $failedCount
            ];

        } catch (Exception $e) {
            Log::error('오프라인 알림 전송 중 오류 발생', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_notifications' => 0,
                'delivered_count' => 0,
                'failed_count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 오래된 전달 완료 알림을 정리합니다.
     *
     * @param int $daysOld 삭제할 알림의 최소 일수 (기본값: 7일)
     * @return int 삭제된 알림 수
     */
    public function cleanupDeliveredNotifications(int $daysOld = 7): int
    {
        try {
            $cutoffDate = Carbon::now()->subDays($daysOld);
            
            $deletedCount = SseOfflineNotification::whereNotNull('delivered_at')
                ->where('delivered_at', '<', $cutoffDate)
                ->delete();

            if ($deletedCount > 0) {
                Log::info('오래된 전달 완료 알림 정리 완료', [
                    'deleted_count' => $deletedCount,
                    'cutoff_date' => $cutoffDate->toISOString()
                ]);
            }

            return $deletedCount;

        } catch (Exception $e) {
            Log::error('전달 완료 알림 정리 실패', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 데이터 업데이트 메시지를 전송합니다.
     *
     * @param string $model 모델명
     * @param string $action 액션
     * @param array $payload 데이터
     * @return array 전송 결과
     */
    public function sendDataUpdate(string $model, string $action, array $payload): array
    {
        try {
            $formattedMessage = $this->messageFormatter->formatDataUpdateMessage($model, $action, $payload);
            $message = $this->messageFormatter->serializeMessage($formattedMessage);
            
            $activeConnections = $this->connectionManager->getActiveConnections();
            $result = $this->sendToConnections($activeConnections, $message);

            Log::info('데이터 업데이트 브로드캐스트 완료', [
                'model' => $model,
                'action' => $action,
                'connections' => count($activeConnections),
                'successful_sends' => $result['successful_sends']
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('데이터 업데이트 브로드캐스트 실패', [
                'model' => $model,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_connections' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 시스템 상태 메시지를 전송합니다.
     *
     * @param string $status 시스템 상태
     * @param array $data 상태 데이터
     * @return array 전송 결과
     */
    public function sendSystemStatus(string $status, array $data = []): array
    {
        try {
            $formattedMessage = $this->messageFormatter->formatSystemStatusMessage($status, $data);
            $message = $this->messageFormatter->serializeMessage($formattedMessage);
            
            $activeConnections = $this->connectionManager->getActiveConnections();
            $result = $this->sendToConnections($activeConnections, $message);

            Log::info('시스템 상태 브로드캐스트 완료', [
                'status' => $status,
                'connections' => count($activeConnections),
                'successful_sends' => $result['successful_sends']
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('시스템 상태 브로드캐스트 실패', [
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_connections' => 0,
                'successful_sends' => 0,
                'failed_sends' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}