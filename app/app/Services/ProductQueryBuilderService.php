<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Req;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Meilisearch\Endpoints\Indexes;

class ProductQueryBuilderService
{
    /**
     * 상품의 요청서 번호 검색
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addReqIdSearchQuery(Builder $builder, array $data): Builder
    {
        if ($data['reqId']) {
            $builder->where('products.req_id', $data['reqId']);
        }

        return $builder;
    }

    /**
     * 상품의 입고상태 검색<br>
     * 검수대기, 검수완료
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addProductCheckedStatusSearchQuery(Builder $builder, array $data): Builder
    {
        if ($data['checkedStatus']) {
            $builder->where('products.checked_status', $data['checkedStatus']);
        }

        return $builder;
    }

    /**
     * 상품의 상태 검색<br>
     * 수리대기중(창고), 반출중, 점검완료, 출고완료, 출고보류, 삭제
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addProductStatusSearchQuery(Builder $builder, array $data): Builder
    {
        $isNullStatus = [
            Product::STATUS_REGISTERED,
            Product::STATUS_WAITING,
            Product::STATUS_REPAIRED,
            Product::STATUS_CARRIED_OUT,
            Product::STATUS_HELD
        ];

        $isNotNullStatus = [
            Product::STATUS_REGISTERED,
            Product::STATUS_CHECKED_ON_PALLET,
            Product::STATUS_EXPORTED
        ];

        $productStatus = (int) $data['productStatus'] ?? null;

        if ($productStatus === Product::STATUS_DELETED) {
            $builder->where('products.status', Product::STATUS_DELETED);
        } else {
            if($productStatus) {
                // 창고 저장(STATUS_REGISTERED(10))인 경우 REGISTERED(10) 또는 CARRIED_OUT(50) 상태를 검색
                if ($productStatus === Product::STATUS_REGISTERED) {
                    $builder->whereIn('products.status', [
                        Product::STATUS_REGISTERED,
                        Product::STATUS_CARRIED_OUT
                    ]);
                } else {
                    $builder->where('products.status', $productStatus);
                }
            } else {
                $builder->where('products.status', '!=', Product::STATUS_DELETED);
            }

            $builder->where(function (Builder $query) use ($isNullStatus, $isNotNullStatus) {
                $query->where(function (Builder $query) use ($isNullStatus) {
                    $query->whereIn('products.status', $isNullStatus)
                        ->whereNull('pallet_products.id');
                })->orWhere(function (Builder $query) use ($isNotNullStatus) {
                    $query->whereIn('products.status', $isNotNullStatus)
                        ->whereNotNull('pallet_products.id');
                });
            });
        }

        if (in_array($productStatus, $isNullStatus)) {
            $builder->whereNull('pallet_products.id');
        }

        return $builder;
    }

    /**
     * 팔레트 상태 검색<br>
     * 등록, 적재중, 적재마감(출고대기), 출고완료, 삭제
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addPalletStatusSearchQuery(Builder $builder, array $data): Builder
    {
        if ($data['palletStatus']) {
            $builder->where('pallets.status', $data['palletStatus']);
        }

        return $builder;
    }

    /**
     * 점검된 상품의 점검코드 검색<br>
     * CB(베스트), CG(굿), CN(노멀), RP(리페어), XL(폐기)
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addProcessSearchQuery(Builder $builder, array $data): Builder
    {
        if ($data['processCd']) {
//            $builder->where('processes.code', $data['processCd']);
            // 팔레트에 적재된 경우 pallet_products의 repair_grade_id를 우선 사용
            // 적재되지 않은 경우 repair_products의 repair_grade_id 사용
            $builder->where(function(Builder $query) use ($data) {
                $query->where('repair_grades.code', $data['processCd']);
            });
        }

        return $builder;
    }

    /**
     * 카테고리 검색
     *
     * @param Builder $builder
     * @param array $data
     *
     * @return Builder
     */
    protected function addCategorySearchQuery (Builder $builder, array $data): Builder
    {
        if ($data['cate4'] && $data['cate5']) {
            // cate4=2는 cate5 조건을 무시하고 cate4만으로 검색
            // cate5가 cate4와 동일한 경우 쿼리 속도 저하가 있음(cate4 하위에 cate5가 한 개만 있을 경우)
            // 가사가전->핸디/스틱 청소기 같은 경우도 5초가 걸린다. 이런 건 어떻게 해야 할까?
            if ($data['cate4'] == 2) {
                $builder->where('products.cate4_id', $data['cate4']);
            } else {
                // 다른 카테고리는 복합 인덱스 사용
                $builder->from(DB::raw('products USE INDEX (products_cate4_id_cate5_id_index)'))
                    ->where('products.cate4_id', $data['cate4'])
                    ->where('products.cate5_id', $data['cate5']);
            }
        } elseif ($data['cate4']) {
            $builder->where('products.cate4_id', $data['cate4']);
        }

        return $builder;
    }

    /**
     * 키워드가 있을 경우 검색컬럼에 맞게 검색
     *
     * @param Builder $builder
     * @param array $data
     *
     * @return Builder
     */
    protected function addKeywordSearchQuery(Builder $builder, array $data): Builder
    {
        if ($data['searchType'] && $data['keyword']) {
            if ($data['searchType'] === 'qaid') {
                $builder->where(function (Builder $query) use ($data) {
                    $query->where('products.qaid', $data['keyword'])
                        ->orWhere('product_lots.name', $data['keyword'])
                        ->orWhere('products.barcode', $data['keyword'])
                        ->orWhere('products.name', 'like', '%'.$data['keyword'].'%');
                });
            } elseif ($data['searchType'] === 'user') {
                $builder->where('users.name', 'like', '%'.$data['keyword'].'%');
            }
        }

        return $builder;
    }

    /**
     * 쿠팡 로켓상품(RG) 검색
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addCoupangRgSearchQuery(Builder $builder, array $data): Builder
    {
        if (isset($data['isRg']) && $data['isRg'] === 'Y') {
            $builder->where('rg', 'Y');
        }

        return $builder;
    }

    /**
     * Aging(입고처리상태가 창고에 있는 것 중 오래된 상품만 찾아 보여주는 것)
     * RG상품: 30일 이상 지난 것
     * 일반상품: 60일 이상 지난 것
     *
     * @param  Builder  $builder
     * @param  array  $data
     *
     * @return Builder
     */
    protected function addCoupangAgSearchQuery(Builder $builder, array $data): Builder
    {
        if (isset($data['isAg']) && $data['isAg'] === 'Y') {
            $day = Product::AGING['normal'];

            if (isset($data['isRg']) && $data['isRg'] === 'Y') {
                $day = Product::AGING['rg'];
            }
            $builder->whereRaw('reqs.req_at <= DATE_SUB(CURRENT_DATE, INTERVAL ? DAY)', [$day]);
        }

        return $builder;
    }

    /**
     * MeiliSearch를 사용하여 Product를 검색합니다.
     *
     * @param string $query 검색어
     * @param string $filter 필터 문자열
     * @param array $attributesToSearchOn 검색 대상 속성
     * @param array $sortOptions 정렬 옵션
     * @return Builder|\Laravel\Scout\Builder
     */
    private function executeProductSearch(string $query, string $filter, array $attributesToSearchOn, array $sortOptions): Builder|\Laravel\Scout\Builder
    {
        return Product::search($query, function (Indexes $meilisearch, string $currentQuery, array $options) use ($filter, $attributesToSearchOn, $sortOptions) {
            $options['filter'] = $filter;
            $options['attributesToSearchOn'] = $attributesToSearchOn;
            $options['sort'] = $sortOptions;
            return $meilisearch->search($currentQuery, $options);
        });
    }


    /**
     * MeiliSearch 사용
     *
     * 직원일 경우 - QAID, 바코드, 상품명, 로트번호에서 키워드로 검색<br>
     * 외부 업체일 경우 - QAID로만 검색 가능
     *
     * @param  array  $data
     * @return Builder|\Laravel\Scout\Builder
     */
    public function searchPage(array $data): Builder|\Laravel\Scout\Builder
    {
        $keyword1 = $data['keyword1'] ?? null;
        $keyword2 = $data['keyword2'] ?? null;
        $searchType = $data['searchType'] ?? null;

        // 1. 검색 파라미터 초기화
        $query = ''; // Full-text 검색어 (기본값: 없음)
        $filters = []; // 필터 조건 배열
        $attributesToSearchOn = []; // 검색 대상 속성 제한
        $sortOptions = ['req_at:desc', 'id:desc'];

        // 2. 필터 조건 구성
        // 항상 삭제된 상품은 제외
        $filters[] = 'status != ' . Product::STATUS_DELETED;

        // keyword1은 ID, 코드 등 정확히 일치하는 값을 필터링하는 데 사용
        if (!empty($keyword1)) {
            if ($searchType === 'qaid') {
                // 외부 업체용 QAID 검색: 특정 상태값과 QAID로 필터링
                $filters[] = 'status = ' . Product::STATUS_CARRIED_OUT;
                $filters[] = sprintf('qaid = "%s"', $keyword1);
            } else {
                // 내부 직원용 검색: QAID, LOT, Barcode 중 하나와 일치하는 경우 필터링
                $filters[] = sprintf('(qaid = "%1$s" OR lot = "%1$s" OR barcode = "%1$s")', $keyword1);
            }
        }

        // keyword2는 상품명과 같이 Full-text 검색이 필요한 경우에 사용
        if (!empty($keyword2)) {
            $query = $keyword2; // Full-text 검색어로 설정
            $attributesToSearchOn = ['name']; // 검색을 'name' 필드로 제한하여 정확도 향상
        }

        // 3. 검색 조건이 하나도 없으면 빈 결과를 반환
        if (empty($keyword1) && empty($keyword2)) {
            return Product::query()->whereRaw('1 = 0');
        }

        // 4. 최종 필터 문자열 생성 및 검색 실행
        $finalFilter = implode(' AND ', $filters);
        $builder = $this->executeProductSearch($query, $finalFilter, $attributesToSearchOn, $sortOptions);

        return $builder->within('product_index');
    }

    /**
     * 쿼리문 가져오기 - 작업목록, RG 상품에 사용
     */
    public function taskPage(array $data): Builder|Product
    {
        // 1. 내부 쿼리 빌더 (모든 필터링과 조인을 여기에 적용)
        $innerQuery = Product::query();

        // DISTINCT와 SELECT는 내부 쿼리에 적용
        $innerQuery->distinct()->select('products.*');

        // 기본 조인: pallet_products는 항상 필요할 수 있으므로 먼저 조인
        $innerQuery->leftJoin('reqs', 'products.req_id', '=', 'reqs.id');

        // reqId 또는 req_at 조건
        $innerQuery->when(!empty($data['reqId']), function (Builder $query) use ($data) {
            return $query->where('reqs.id', $data['reqId']);
        }, function (Builder $query) use ($data) {
            // $data['beginAt'] 와 $data['endAt'] 가 항상 존재한다고 가정.
            // 만약 없을 수 있다면, 해당 키 존재 여부 확인 필요
            if (isset($data['beginAt']) && isset($data['endAt'])) {
                return $query->whereBetween('reqs.req_at', [$data['beginAt'], $data['endAt']]);
            }
            return $query;
        });

        // reqType 조건
        if (empty($data['reqType'])) {
            // Req 모델의 상수를 사용 (네임스페이스 확인 필요)
            $innerQuery->whereNotIn('reqs.req_type', [Req::TYPE_GHOST, Req::TYPE_UNLINKED]);
        } else {
            $innerQuery->where('reqs.req_type', $data['reqType']);
        }

        // 조건부 조인: 아래 검색 조건들에서 필요한 테이블들을 미리 조인합니다.
        // palletStatus, processCd, productStatus 검색 시 필요한 조인
        $productStatus = isset($data['productStatus']) && $data['productStatus'] !== '' ? (int) $data['productStatus'] : null;
        $palletStatusVal = isset($data['palletStatus']) && $data['palletStatus'] !== '' ? (int) $data['palletStatus'] : null;

        // pallets 테이블은 palletStatus가 있을 때만 필요 (pallet_products는 이미 기본 조인됨)
        $innerQuery->leftJoin('pallet_products', 'products.id', '=', 'pallet_products.product_id');
        if ($palletStatusVal) {
            $innerQuery->leftJoin('pallets', 'pallet_products.pallet_id', '=', 'pallets.id');
        }

        if (!empty($data['processCd'])) {
            // 수리 제품 정보 조인 (팔레트 적재 여부와 관계없이)
            $innerQuery->leftJoin('repair_products', 'products.id', '=', 'repair_products.product_id')
                ->leftJoin('repair_grades', function($join) {
                    $join->on('repair_products.repair_grade_id', '=', 'repair_grades.id')
                         ->orOn('pallet_products.repair_grade_id', '=', 'repair_grades.id');
                });
        }

        // 키워드 검색을 위한 조인 (조건부)
        if (!empty($data['searchType']) && !empty($data['keyword'])) {
            // addKeywordSearchQuery 메서드 내에서 필요한 테이블(product_lots, users)을 참조하므로,
            // 해당 메서드 호출 전에 조인하거나, 메서드 내에서 조인하도록 수정이 필요할 수 있습니다.
            // 현재 구조상으로는 addKeywordSearchQuery가 호출될 때 이 조인들이 이미 되어 있어야 합니다.
            // 또는, 각 검색 메서드 내에서 필요에 따라 조인하도록 변경하는 것도 방법입니다.
            // 여기서는 일단 원본 코드의 순서를 따라 필요한 경우 조인합니다.
            if ($data['searchType'] === 'qaid') {
                $innerQuery->leftJoin('product_lots', 'products.product_lot_id', '=', 'product_lots.id');
            }
            if ($data['searchType'] === 'user') {
                $innerQuery->leftJoin('users', 'products.user_id', '=', 'users.id');
            }
        }

        // 검색 조건 추가 메서드 호출 (조인이 필요한 메서드보다 먼저 호출되거나, 메서드 내에서 조인 처리)
        // 순서가 중요할 수 있습니다. 예를 들어 addPalletStatusSearchQuery는 pallets 테이블을 사용합니다.
        $innerQuery = $this->addCoupangRgSearchQuery($innerQuery, $data); // products.rg 사용
        $innerQuery = $this->addCoupangAgSearchQuery($innerQuery, $data);   // reqs.req_at 사용 (이미 조인됨)
        $innerQuery = $this->addCategorySearchQuery($innerQuery, $data);
        $innerQuery = $this->addProductCheckedStatusSearchQuery($innerQuery, $data);
        $innerQuery = $this->addProcessSearchQuery($innerQuery, $data);   // processes.code 사용
        $innerQuery = $this->addKeywordSearchQuery($innerQuery, $data);   // product_lots.name, users.name 사용
        $innerQuery = $this->addProductStatusSearchQuery($innerQuery, $data); // products.status, pallet_products.id 사용 (이미 조인됨)
        $innerQuery = $this->addPalletStatusSearchQuery($innerQuery, $data); // pallets.status 사용


        // 내부 쿼리에서는 ORDER BY를 적용하지 않습니다.

        // 2. 외부 쿼리 빌더 (서브쿼리를 사용하여 내부 쿼리 결과를 가져옴)
        // Product::fromSub()을 사용하여 Eloquent 모델 인스턴스를 계속 다룰 수 있도록 합니다.
        // 정렬은 외부 쿼리에서 적용
        return Product::fromSub($innerQuery, 'p_outer')
            ->select('p_outer.*') // 서브쿼리 (p_outer)의 모든 컬럼을 선택
            ->orderBy('p_outer.updated_at', 'desc');
    }

    /**
     * 쿼리문 가져오기 - 검수대기 상품
     *
     * @param array $data 검색 파라미터
     *
     * @return Product|Builder
     */
    public function inspectPage(array $data = []): Builder|Product
    {
        // 요청서 인덱스가 있다면 해당 요청서의 검수대기 상품을 가져오고
        $builder = Product::select('products.*')
            ->where('products.checked_status', Product::CHECKED_STATUS_UNCHECKED)
            ->where('products.status', Product::STATUS_REGISTERED);
        if ($data['reqId']) {
            $builder->where('products.req_id', $data['reqId']);
        } else {
            $builder->orderBy('products.req_id');
        }

        if ($data['cate4'] || $data['cate5']) {
            $builder->leftJoin('cate4', 'products.cate4_id', '=', 'cate4.id')
                ->leftJoin('cate5', 'products.cate5_id', '=', 'cate5.id');
        }

        if ($data['searchType'] && $data['keyword']) {
            $builder->leftJoin('product_lots', 'products.product_lot_id', '=', 'product_lots.id')
                ->leftJoin('users', 'products.user_id', '=', 'users.id');
        }

        $builder = $this->addCategorySearchQuery($builder, $data);
        $builder = $this->addCoupangRgSearchQuery($builder, $data);
        $builder = $this->addKeywordSearchQuery($builder, $data);

        if (isset($data['isAg']) && $data['isAg'] === 'Y') {
            $day = Product::AGING['normal'];

            if (isset($data['isRg']) && $data['isRg'] === 'Y') {
                $day = Product::AGING['rg'];
            }
            $builder->leftJoin('reqs', 'products.req_id', '=', 'reqs.id')
                ->where('reqs.req_at', '<=', DB::raw("DATE_SUB(CURRENT_DATE, INTERVAL $day DAY)"));
        }

        $builder->orderBy('products.updated_at', 'desc');

        return $builder;
    }

    /**
     * 쿼리문 가져오기 - 미등록 상품
     *
     * @param array $data
     *
     * @return Builder
     */
    public function unlinkedPage(array $data): Builder
    {
        $builder = Product::distinct()->select('products.*')
            ->leftJoin('reqs', 'products.req_id', '=', 'reqs.id')
            ->leftJoin('product_lots', 'products.product_lot_id', '=', 'product_lots.id')
            ->leftJoin('pallet_products', 'products.id', '=', 'pallet_products.product_id')
            ->leftJoin('repair_products', 'products.id', '=', 'repair_products.product_id')
            ->leftJoin('repair_grades', function($join) {
                $join->on('repair_products.repair_grade_id', '=', 'repair_grades.id')
                     ->orOn('pallet_products.repair_grade_id', '=', 'repair_grades.id');
            })
            ->leftJoin('users', 'products.user_id', '=', 'users.id')
            ->where('products.req_id', Req::UNLINKED_ID)
            ->where('reqs.req_type', Req::TYPE_UNLINKED);

        $builder = $this->addProcessSearchQuery($builder, $data);
        $builder = $this->addCategorySearchQuery($builder, $data);
        $builder = $this->addKeywordSearchQuery($builder, $data);
        $builder = $this->addProductStatusSearchQuery($builder, $data);

        $builder->orderBy('products.updated_at', 'desc');

        return $builder;
    }

    /**
     * 쿼리문 가져오기 - 중복상품
     *
     * @param array $data
     *
     * @return Builder
     */
    public function duplicatedPage(array $data): Builder
    {
        $builder = Product::select('products.*')
            ->leftJoin('reqs', 'products.req_id', '=', 'reqs.id')
            ->leftJoin('product_lots', 'products.product_lot_id', '=', 'product_lots.id')
            ->leftJoin('pallet_products', 'products.id', '=', 'pallet_products.product_id')
            ->leftJoin('repair_products', 'products.id', '=', 'repair_products.product_id')
            ->leftJoin('repair_grades', function($join) {
                $join->on('repair_products.repair_grade_id', '=', 'repair_grades.id')
                     ->orOn('pallet_products.repair_grade_id', '=', 'repair_grades.id');
            })
            ->leftJoin('users', 'products.user_id', '=', 'users.id')
            ->where('products.duplicated', 'Y');

        if (empty($data['reqType'])) {
            $builder->whereNotIN('reqs.req_type', [Req::TYPE_GHOST, Req::TYPE_UNLINKED]);
        } else {
            $builder->where('reqs.req_type', $data['reqType']);
        }

        $builder = $this->addReqIdSearchQuery($builder, $data);
        $builder = $this->addProcessSearchQuery($builder, $data);
        $builder = $this->addCategorySearchQuery($builder, $data);
        $builder = $this->addKeywordSearchQuery($builder, $data);

        $productStatus = (int) $data['productStatus'] ?? null;
        if ($productStatus === Product::STATUS_DELETED) {
            $builder->where('products.status', Product::STATUS_DELETED);
        } else {
            if($productStatus) {
                $builder->where('products.status', $data['productStatus']);
            } else {
                $builder->where('products.status', '!=', Product::STATUS_DELETED);
            }
        }

        $builder->orderBy('reqs.req_at', 'desc')
            ->orderBy('products.name');

        return $builder;
    }
}
