<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

/**
 * 데이터 직렬화 서비스
 * 
 * 모델 데이터를 클라이언트 전송용으로 최적화하여 직렬화합니다.
 * 기존 캐시 시스템을 활용하여 성능을 최적화합니다.
 */
class DataSerializationService
{
    private CategoryService $categoryService;
    
    /**
     * 직렬화에서 제외할 필드들
     */
    private const EXCLUDED_FIELDS = [
        'created_at',
        'updated_at',
        'deleted_at',
        'pivot'
    ];

    /**
     * 데이터 크기 최적화를 위한 최대 문자열 길이
     */
    private const MAX_STRING_LENGTH = 1000;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * 카테고리 데이터를 직렬화합니다.
     * 기존 CategoryService의 캐시된 데이터를 활용합니다.
     *
     * @param bool $useCache 캐시 사용 여부 (기본값: true)
     * @return array 직렬화된 카테고리 데이터
     */
    public function serializeCategoryData(bool $useCache = true): array
    {
        try {
            Log::debug('카테고리 데이터 직렬화 시작', [
                'use_cache' => $useCache
            ]);

            // CategoryService의 캐시된 데이터 활용
            $categories = $this->categoryService->getAllCategories($useCache);
            
            // Resource Collection을 배열로 변환
            $categoryArray = $categories->toArray(request());
            
            // 데이터 최적화 적용
            $optimizedData = $this->optimizeDataSize($categoryArray);
            
            // 타임스탬프 추가
            $result = $this->addTimestamp($optimizedData);

            Log::debug('카테고리 데이터 직렬화 완료', [
                'original_size' => strlen(json_encode($categoryArray)),
                'optimized_size' => strlen(json_encode($result['data'])),
                'category_count' => count($result['data'])
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('카테고리 데이터 직렬화 실패', [
                'error' => $e->getMessage()
            ]);

            // 실패 시 빈 데이터 반환
            return $this->addTimestamp([]);
        }
    }

    /**
     * 사용자 데이터를 직렬화합니다.
     *
     * @param User $user 사용자 모델
     * @param array $includeFields 포함할 필드 목록 (선택사항)
     * @return array 직렬화된 사용자 데이터
     */
    public function serializeUserData(User $user, array $includeFields = []): array
    {
        try {
            // 기본 포함 필드
            $defaultFields = ['id', 'name', 'email', 'role'];
            $fields = empty($includeFields) ? $defaultFields : $includeFields;

            $userData = [];
            foreach ($fields as $field) {
                if (isset($user->$field)) {
                    $userData[$field] = $user->$field;
                }
            }

            // 데이터 최적화 적용
            $optimizedData = $this->optimizeDataSize($userData);
            
            // 타임스탬프 추가
            $result = $this->addTimestamp($optimizedData);

            Log::debug('사용자 데이터 직렬화 완료', [
                'user_id' => $user->id,
                'included_fields' => $fields
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('사용자 데이터 직렬화 실패', [
                'user_id' => $user->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            return $this->addTimestamp([]);
        }
    }

    /**
     * 알림 데이터를 직렬화합니다.
     *
     * @param array $notification 알림 데이터
     * @return array 직렬화된 알림 데이터
     */
    public function serializeNotificationData(array $notification): array
    {
        try {
            // 필수 필드 확인
            $requiredFields = ['title', 'message'];
            foreach ($requiredFields as $field) {
                if (!isset($notification[$field])) {
                    throw new Exception("필수 필드 누락: {$field}");
                }
            }

            // 기본 구조 생성
            $serializedData = [
                'id' => $notification['id'] ?? uniqid('notification_'),
                'title' => $this->truncateString($notification['title']),
                'message' => $this->truncateString($notification['message']),
                'priority' => $notification['priority'] ?? 'normal',
                'type' => $notification['type'] ?? 'info'
            ];

            // 선택적 필드 추가
            $optionalFields = ['action_url', 'icon', 'duration', 'data'];
            foreach ($optionalFields as $field) {
                if (isset($notification[$field])) {
                    $serializedData[$field] = $notification[$field];
                }
            }

            // 데이터 최적화 적용
            $optimizedData = $this->optimizeDataSize($serializedData);
            
            // 타임스탬프 추가
            $result = $this->addTimestamp($optimizedData);

            Log::debug('알림 데이터 직렬화 완료', [
                'notification_id' => $serializedData['id'],
                'title' => $serializedData['title']
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('알림 데이터 직렬화 실패', [
                'notification' => $notification,
                'error' => $e->getMessage()
            ]);

            // 실패 시 기본 알림 데이터 반환
            return $this->addTimestamp([
                'id' => uniqid('error_notification_'),
                'title' => '알림 오류',
                'message' => '알림을 처리하는 중 오류가 발생했습니다.',
                'priority' => 'high',
                'type' => 'error'
            ]);
        }
    }

    /**
     * 일반적인 모델 데이터를 직렬화합니다.
     *
     * @param mixed $model 모델 인스턴스 또는 데이터 배열
     * @param array $includeFields 포함할 필드 목록 (선택사항)
     * @param array $excludeFields 제외할 필드 목록 (선택사항)
     * @return array 직렬화된 데이터
     */
    public function serializeModelData($model, array $includeFields = [], array $excludeFields = []): array
    {
        try {
            $data = [];

            // 모델 인스턴스인 경우 배열로 변환
            if (is_object($model)) {
                if (method_exists($model, 'toArray')) {
                    $data = $model->toArray();
                } else {
                    $data = (array) $model;
                }
            } elseif (is_array($model)) {
                $data = $model;
            } else {
                throw new Exception('지원하지 않는 데이터 타입');
            }

            // 필드 필터링
            $data = $this->filterFields($data, $includeFields, $excludeFields);
            
            // 데이터 최적화 적용
            $optimizedData = $this->optimizeDataSize($data);
            
            // 타임스탬프 추가
            $result = $this->addTimestamp($optimizedData);

            Log::debug('모델 데이터 직렬화 완료', [
                'model_type' => is_object($model) ? get_class($model) : 'array',
                'field_count' => count($result['data'])
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('모델 데이터 직렬화 실패', [
                'model_type' => is_object($model) ? get_class($model) : gettype($model),
                'error' => $e->getMessage()
            ]);

            return $this->addTimestamp([]);
        }
    }

    /**
     * 데이터 크기를 최적화합니다.
     *
     * @param array $data 원본 데이터
     * @return array 최적화된 데이터
     */
    private function optimizeDataSize(array $data): array
    {
        try {
            // 재귀적으로 배열 처리
            foreach ($data as $key => $value) {
                if (is_array($value)) {
                    $data[$key] = $this->optimizeDataSize($value);
                } elseif (is_string($value)) {
                    $data[$key] = $this->truncateString($value);
                } elseif (is_null($value)) {
                    // null 값 제거로 크기 최적화
                    unset($data[$key]);
                }
            }

            // 제외 필드 제거
            foreach (self::EXCLUDED_FIELDS as $excludeField) {
                unset($data[$excludeField]);
            }

            return $data;

        } catch (Exception $e) {
            Log::warning('데이터 크기 최적화 중 오류 발생', [
                'error' => $e->getMessage()
            ]);
            return $data;
        }
    }

    /**
     * 문자열을 최대 길이로 제한합니다.
     *
     * @param string $string 원본 문자열
     * @param int $maxLength 최대 길이 (기본값: MAX_STRING_LENGTH)
     * @return string 제한된 문자열
     */
    private function truncateString(string $string, int $maxLength = self::MAX_STRING_LENGTH): string
    {
        if (mb_strlen($string) <= $maxLength) {
            return $string;
        }

        return mb_substr($string, 0, $maxLength - 3) . '...';
    }

    /**
     * 데이터에 타임스탬프를 추가합니다.
     *
     * @param array $data 원본 데이터
     * @return array 타임스탬프가 추가된 데이터
     */
    private function addTimestamp(array $data): array
    {
        return [
            'data' => $data,
            'timestamp' => Carbon::now()->toISOString(),
            'serialized_at' => Carbon::now()->format('Y-m-d H:i:s')
        ];
    }

    /**
     * 필드를 필터링합니다.
     *
     * @param array $data 원본 데이터
     * @param array $includeFields 포함할 필드 목록
     * @param array $excludeFields 제외할 필드 목록
     * @return array 필터링된 데이터
     */
    private function filterFields(array $data, array $includeFields = [], array $excludeFields = []): array
    {
        // 포함할 필드가 지정된 경우
        if (!empty($includeFields)) {
            $filteredData = [];
            foreach ($includeFields as $field) {
                if (array_key_exists($field, $data)) {
                    $filteredData[$field] = $data[$field];
                }
            }
            $data = $filteredData;
        }

        // 제외할 필드가 지정된 경우
        if (!empty($excludeFields)) {
            foreach ($excludeFields as $field) {
                unset($data[$field]);
            }
        }

        return $data;
    }

    /**
     * 배치 데이터를 직렬화합니다.
     *
     * @param array $items 직렬화할 아이템 배열
     * @param string $dataType 데이터 타입
     * @param int $batchSize 배치 크기 (기본값: 100)
     * @return array 배치별로 직렬화된 데이터
     */
    public function serializeBatchData(array $items, string $dataType, int $batchSize = 100): array
    {
        try {
            $batches = array_chunk($items, $batchSize);
            $serializedBatches = [];

            foreach ($batches as $index => $batch) {
                $serializedBatch = [];
                
                foreach ($batch as $item) {
                    $serializedItem = $this->serializeModelData($item);
                    $serializedBatch[] = $serializedItem['data'];
                }

                $serializedBatches[] = [
                    'batch_index' => $index,
                    'batch_size' => count($batch),
                    'data_type' => $dataType,
                    'data' => $serializedBatch,
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }

            Log::info('배치 데이터 직렬화 완료', [
                'data_type' => $dataType,
                'total_items' => count($items),
                'batch_count' => count($serializedBatches),
                'batch_size' => $batchSize
            ]);

            return $serializedBatches;

        } catch (Exception $e) {
            Log::error('배치 데이터 직렬화 실패', [
                'data_type' => $dataType,
                'item_count' => count($items),
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * 직렬화 통계를 반환합니다.
     *
     * @param array $originalData 원본 데이터
     * @param array $serializedData 직렬화된 데이터
     * @return array 직렬화 통계
     */
    public function getSerializationStats(array $originalData, array $serializedData): array
    {
        $originalSize = strlen(json_encode($originalData));
        $serializedSize = strlen(json_encode($serializedData));
        $compressionRatio = $originalSize > 0 ? ($originalSize - $serializedSize) / $originalSize * 100 : 0;

        return [
            'original_size' => $originalSize,
            'serialized_size' => $serializedSize,
            'size_reduction' => $originalSize - $serializedSize,
            'compression_ratio' => round($compressionRatio, 2),
            'original_field_count' => $this->countFields($originalData),
            'serialized_field_count' => $this->countFields($serializedData),
            'timestamp' => Carbon::now()->toISOString()
        ];
    }

    /**
     * 배열의 필드 수를 재귀적으로 계산합니다.
     *
     * @param array $data 데이터 배열
     * @return int 총 필드 수
     */
    private function countFields(array $data): int
    {
        $count = 0;
        
        foreach ($data as $value) {
            if (is_array($value)) {
                $count += $this->countFields($value);
            } else {
                $count++;
            }
        }

        return $count;
    }

    /**
     * 메모리 사용량을 최적화하기 위해 대용량 데이터를 스트림 방식으로 직렬화합니다.
     *
     * @param array $data 대용량 데이터
     * @param int $chunkSize 청크 크기 (기본값: 1000)
     * @return \Generator 직렬화된 데이터 청크
     */
    public function serializeStreamData(array $data, int $chunkSize = 1000): \Generator
    {
        try {
            $chunks = array_chunk($data, $chunkSize);
            
            foreach ($chunks as $index => $chunk) {
                $serializedChunk = $this->optimizeDataSize($chunk);
                
                yield [
                    'chunk_index' => $index,
                    'chunk_size' => count($chunk),
                    'data' => $serializedChunk,
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }

            Log::info('스트림 데이터 직렬화 완료', [
                'total_items' => count($data),
                'chunk_count' => count($chunks),
                'chunk_size' => $chunkSize
            ]);

        } catch (Exception $e) {
            Log::error('스트림 데이터 직렬화 실패', [
                'item_count' => count($data),
                'error' => $e->getMessage()
            ]);
        }
    }
}