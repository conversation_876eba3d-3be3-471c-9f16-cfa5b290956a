<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;
use Exception;

/**
 * SSE 성능 최적화 서비스
 * 
 * 메시지 배치 처리, JSON 구조 최적화, 크기 최소화를 통해
 * SSE 시스템의 성능을 최적화합니다.
 */
class PerformanceOptimizer
{
    /**
     * 기본 배치 크기 설정
     */
    private const DEFAULT_BATCH_SIZE = 50;
    private const MIN_BATCH_SIZE = 10;
    private const MAX_BATCH_SIZE = 500;
    
    /**
     * 메시지 크기 제한 (바이트)
     */
    private const MAX_MESSAGE_SIZE = 65536; // 64KB
    private const OPTIMAL_MESSAGE_SIZE = 32768; // 32KB
    
    /**
     * 압축 임계값
     */
    private const COMPRESSION_THRESHOLD = 1024; // 1KB
    
    /**
     * 성능 메트릭 키
     */
    private const METRICS_KEY = 'sse:performance_metrics';
    private const BATCH_STATS_KEY = 'sse:batch_stats';

    /**
     * 메시지들을 배치로 처리합니다.
     *
     * @param array $messages 처리할 메시지 배열
     * @param int|null $batchSize 배치 크기 (null인 경우 자동 계산)
     * @return array 배치 처리된 메시지 그룹
     */
    public function batchMessages(array $messages, ?int $batchSize = null): array
    {
        try {
            if (empty($messages)) {
                return [];
            }

            // 최적 배치 크기 계산
            $optimalBatchSize = $batchSize ?? $this->calculateOptimalBatchSize($messages);
            
            Log::debug('메시지 배치 처리 시작', [
                'total_messages' => count($messages),
                'batch_size' => $optimalBatchSize
            ]);

            // 메시지를 배치로 분할
            $batches = array_chunk($messages, $optimalBatchSize);
            $processedBatches = [];

            foreach ($batches as $index => $batch) {
                $processedBatch = $this->processBatch($batch, $index);
                
                if ($processedBatch) {
                    $processedBatches[] = $processedBatch;
                }
            }

            // 배치 통계 업데이트
            $this->updateBatchStats(count($messages), count($processedBatches), $optimalBatchSize);

            Log::info('메시지 배치 처리 완료', [
                'total_messages' => count($messages),
                'batch_count' => count($processedBatches),
                'batch_size' => $optimalBatchSize
            ]);

            return $processedBatches;

        } catch (Exception $e) {
            Log::error('메시지 배치 처리 실패', [
                'message_count' => count($messages),
                'error' => $e->getMessage()
            ]);
            
            // 실패 시 개별 메시지로 폴백
            return array_map(function ($message, $index) {
                return [
                    'batch_id' => uniqid('fallback_batch_'),
                    'batch_index' => $index,
                    'messages' => [$message],
                    'message_count' => 1,
                    'total_size' => strlen(json_encode($message)),
                    'created_at' => Carbon::now()->toISOString()
                ];
            }, $messages, array_keys($messages));
        }
    }

    /**
     * JSON 구조를 최적화하고 크기를 최소화합니다.
     * 실제 압축은 Nginx의 Brotli/Gzip을 통해 처리됩니다.
     *
     * @param array $data 최적화할 데이터
     * @param bool $aggressive 적극적 최적화 여부
     * @return array 최적화된 데이터
     */
    public function optimizeMessage(array $data, bool $aggressive = false): array
    {
        try {
            $originalSize = strlen(json_encode($data));
            
            Log::debug('메시지 구조 최적화 시작', [
                'original_size' => $originalSize,
                'aggressive' => $aggressive
            ]);

            // 1단계: 기본 최적화 (불필요한 필드 제거, null 값 정리)
            $optimizedData = $this->applyBasicOptimization($data);
            
            // 2단계: 적극적 최적화 (필요시)
            if ($aggressive || $this->shouldApplyAggressiveOptimization($optimizedData)) {
                $optimizedData = $this->applyAggressiveOptimization($optimizedData);
            }
            
            // 3단계: JSON 구조 최적화 (중복 제거, 구조 개선)
            $optimizedData = $this->optimizeJsonStructure($optimizedData);
            
            // 4단계: 메시지 크기 검증
            $optimizedData = $this->ensureMessageSize($optimizedData);

            $finalSize = strlen(json_encode($optimizedData));
            $sizeReduction = $originalSize - $finalSize;

            // 성능 메트릭 업데이트 (구조 최적화 효과만 측정)
            $this->updateOptimizationMetrics($originalSize, $finalSize, $sizeReduction);

            Log::debug('메시지 구조 최적화 완료 (실제 압축은 Nginx에서 처리)', [
                'original_size' => $originalSize,
                'optimized_size' => $finalSize,
                'size_reduction' => $sizeReduction,
                'note' => 'Nginx Brotli/Gzip 압축이 추가로 적용됩니다'
            ]);

            return $optimizedData;

        } catch (Exception $e) {
            Log::error('메시지 구조 최적화 실패', [
                'error' => $e->getMessage()
            ]);
            
            // 실패 시 원본 데이터 반환
            return $data;
        }
    }

    /**
     * 최적 배치 크기를 계산합니다.
     *
     * @param array $messages 메시지 배열
     * @return int 최적 배치 크기
     */
    public function calculateOptimalBatchSize(array $messages = []): int
    {
        try {
            // 메시지가 없는 경우 기본값 반환
            if (empty($messages)) {
                return self::DEFAULT_BATCH_SIZE;
            }

            // 평균 메시지 크기 계산
            $totalSize = 0;
            $sampleSize = min(10, count($messages)); // 최대 10개 샘플링
            
            for ($i = 0; $i < $sampleSize; $i++) {
                $messageSize = strlen(json_encode($messages[$i]));
                $totalSize += $messageSize;
            }
            
            $averageMessageSize = $totalSize / $sampleSize;
            
            // 최적 배치 크기 계산
            $optimalBatchSize = intval(self::OPTIMAL_MESSAGE_SIZE / $averageMessageSize);
            
            // 최소/최대 범위 내로 제한
            $optimalBatchSize = max(self::MIN_BATCH_SIZE, $optimalBatchSize);
            $optimalBatchSize = min(self::MAX_BATCH_SIZE, $optimalBatchSize);

            Log::debug('최적 배치 크기 계산 완료', [
                'message_count' => count($messages),
                'average_message_size' => round($averageMessageSize),
                'optimal_batch_size' => $optimalBatchSize
            ]);

            return $optimalBatchSize;

        } catch (Exception $e) {
            Log::error('최적 배치 크기 계산 실패', [
                'error' => $e->getMessage()
            ]);
            
            return self::DEFAULT_BATCH_SIZE;
        }
    }

    /**
     * JSON 구조를 최적화합니다.
     * 실제 압축은 Nginx Brotli/Gzip에서 처리되므로 구조적 최적화에 집중합니다.
     *
     * @param array $data 원본 데이터
     * @return array 구조가 최적화된 데이터
     */
    public function minimizeJsonSize(array $data): array
    {
        try {
            Log::debug('JSON 구조 최적화 시작 (압축은 Nginx에서 처리)');

            // 1. 불필요한 필드 제거
            $optimizedData = $this->removeUnnecessaryFields($data);
            
            // 2. 문자열 정리 (공백 제거 등)
            $optimizedData = $this->compressStrings($optimizedData);
            
            // 3. 숫자 최적화
            $optimizedData = $this->optimizeNumbers($optimizedData);
            
            // 4. 배열 구조 최적화
            $optimizedData = $this->optimizeArrayStructure($optimizedData);
            
            // 5. 키 이름 단축 (극단적인 경우에만)
            if ($this->shouldShortenKeys($optimizedData)) {
                $optimizedData = $this->shortenKeys($optimizedData);
            }

            $originalSize = strlen(json_encode($data));
            $optimizedSize = strlen(json_encode($optimizedData));
            $reduction = $originalSize - $optimizedSize;

            Log::debug('JSON 구조 최적화 완료', [
                'original_size' => $originalSize,
                'optimized_size' => $optimizedSize,
                'structure_reduction' => $reduction,
                'reduction_percentage' => $originalSize > 0 ? round(($reduction / $originalSize) * 100, 2) : 0,
                'note' => 'Nginx Brotli 압축으로 추가 50-80% 압축 예상'
            ]);

            return $optimizedData;

        } catch (Exception $e) {
            Log::error('JSON 구조 최적화 실패', [
                'error' => $e->getMessage()
            ]);
            
            return $data;
        }
    }

    /**
     * 배치를 처리합니다.
     *
     * @param array $messages 배치 내 메시지들
     * @param int $batchIndex 배치 인덱스
     * @return array|null 처리된 배치 데이터
     */
    private function processBatch(array $messages, int $batchIndex): ?array
    {
        try {
            $batchId = uniqid('batch_' . $batchIndex . '_');
            $optimizedMessages = [];
            $totalSize = 0;

            foreach ($messages as $message) {
                $optimizedMessage = $this->optimizeMessage($message);
                $optimizedMessages[] = $optimizedMessage;
                $totalSize += strlen(json_encode($optimizedMessage));
            }

            // 배치 크기가 너무 큰 경우 분할
            if ($totalSize > self::MAX_MESSAGE_SIZE) {
                return $this->splitOversizedBatch($messages, $batchIndex);
            }

            return [
                'batch_id' => $batchId,
                'batch_index' => $batchIndex,
                'messages' => $optimizedMessages,
                'message_count' => count($optimizedMessages),
                'total_size' => $totalSize,
                'created_at' => Carbon::now()->toISOString()
            ];

        } catch (Exception $e) {
            Log::error('배치 처리 실패', [
                'batch_index' => $batchIndex,
                'message_count' => count($messages),
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * 기본 최적화를 적용합니다.
     *
     * @param array $data 원본 데이터
     * @return array 기본 최적화된 데이터
     */
    private function applyBasicOptimization(array $data): array
    {
        // 1단계: 불필요한 필드 제거
        $data = $this->removeUnnecessaryFields($data);
        
        // 2단계: null 값 제거
        $data = array_filter($data, function ($value) {
            return $value !== null;
        });

        // 3단계: 빈 배열 제거
        $data = array_filter($data, function ($value) {
            return !is_array($value) || !empty($value);
        });

        // 4단계: 재귀적으로 하위 배열 처리
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->applyBasicOptimization($value);
            }
        }

        return $data;
    }

    /**
     * 적극적 최적화를 적용합니다.
     *
     * @param array $data 원본 데이터
     * @return array 적극적으로 최적화된 데이터
     */
    private function applyAggressiveOptimization(array $data): array
    {
        // 긴 문자열 자르기
        foreach ($data as $key => $value) {
            if (is_string($value) && strlen($value) > 500) {
                $data[$key] = substr($value, 0, 497) . '...';
            } elseif (is_array($value)) {
                $data[$key] = $this->applyAggressiveOptimization($value);
            }
        }

        // 불필요한 메타데이터 제거
        $unnecessaryKeys = ['created_at', 'updated_at', 'deleted_at', 'pivot', 'laravel_through_key'];
        foreach ($unnecessaryKeys as $key) {
            unset($data[$key]);
        }

        return $data;
    }

    /**
     * JSON 구조를 최적화합니다.
     *
     * @param array $data 원본 데이터
     * @return array 구조가 최적화된 데이터
     */
    private function optimizeJsonStructure(array $data): array
    {
        // 중복 데이터 제거
        if (isset($data['data']) && is_array($data['data'])) {
            $data['data'] = array_unique($data['data'], SORT_REGULAR);
        }

        // 인덱스 배열을 연관 배열로 변환 (필요시)
        foreach ($data as $key => $value) {
            if (is_array($value) && $this->shouldConvertToAssociative($value)) {
                $data[$key] = $this->convertToAssociativeArray($value);
            }
        }

        return $data;
    }

    /**
     * 메시지 크기를 확인하고 필요시 추가 압축을 적용합니다.
     *
     * @param array $data 데이터
     * @return array 크기가 조정된 데이터
     */
    private function ensureMessageSize(array $data): array
    {
        $currentSize = strlen(json_encode($data));
        
        if ($currentSize <= self::MAX_MESSAGE_SIZE) {
            return $data;
        }

        Log::warning('메시지 크기가 제한을 초과하여 추가 압축 적용', [
            'current_size' => $currentSize,
            'max_size' => self::MAX_MESSAGE_SIZE
        ]);

        // 추가 압축 적용
        return $this->applyEmergencyCompression($data);
    }

    /**
     * 적극적 최적화 적용 여부를 판단합니다.
     *
     * @param array $data 데이터
     * @return bool 적극적 최적화 필요 여부
     */
    private function shouldApplyAggressiveOptimization(array $data): bool
    {
        $size = strlen(json_encode($data));
        return $size > self::COMPRESSION_THRESHOLD;
    }

    /**
     * 불필요한 필드를 제거합니다.
     *
     * @param array $data 원본 데이터
     * @return array 필드가 제거된 데이터
     */
    private function removeUnnecessaryFields(array $data): array
    {
        $unnecessaryFields = [
            'created_at', 'updated_at', 'deleted_at', 'pivot',
            'laravel_through_key', 'password', 'remember_token'
        ];

        foreach ($unnecessaryFields as $field) {
            unset($data[$field]);
        }

        // 재귀적으로 하위 배열 처리
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->removeUnnecessaryFields($value);
            }
        }

        return $data;
    }

    /**
     * 문자열을 압축합니다.
     *
     * @param array $data 원본 데이터
     * @return array 문자열이 압축된 데이터
     */
    private function compressStrings(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // 반복되는 공백 제거
                $value = preg_replace('/\s+/', ' ', $value);
                // 앞뒤 공백 제거
                $value = trim($value);
                $data[$key] = $value;
            } elseif (is_array($value)) {
                $data[$key] = $this->compressStrings($value);
            }
        }

        return $data;
    }

    /**
     * 숫자를 최적화합니다.
     *
     * @param array $data 원본 데이터
     * @return array 숫자가 최적화된 데이터
     */
    private function optimizeNumbers(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_numeric($value)) {
                // 불필요한 소수점 제거
                if (is_float($value) && $value == intval($value)) {
                    $data[$key] = intval($value);
                }
            } elseif (is_array($value)) {
                $data[$key] = $this->optimizeNumbers($value);
            }
        }

        return $data;
    }

    /**
     * 배열 구조를 최적화합니다.
     *
     * @param array $data 원본 데이터
     * @return array 구조가 최적화된 데이터
     */
    private function optimizeArrayStructure(array $data): array
    {
        // 빈 배열 제거
        $data = array_filter($data, function ($value) {
            return !is_array($value) || !empty($value);
        });

        // 단일 요소 배열을 값으로 변환 (적절한 경우)
        foreach ($data as $key => $value) {
            if (is_array($value) && count($value) === 1 && isset($value[0]) && !is_array($value[0])) {
                $data[$key] = $value[0];
            } elseif (is_array($value)) {
                $data[$key] = $this->optimizeArrayStructure($value);
            }
        }

        return $data;
    }

    /**
     * 키 이름 단축 여부를 판단합니다.
     *
     * @param array $data 데이터
     * @return bool 키 단축 필요 여부
     */
    private function shouldShortenKeys(array $data): bool
    {
        $size = strlen(json_encode($data));
        return $size > self::OPTIMAL_MESSAGE_SIZE;
    }

    /**
     * 키 이름을 단축합니다.
     *
     * @param array $data 원본 데이터
     * @return array 키가 단축된 데이터
     */
    private function shortenKeys(array $data): array
    {
        $keyMap = [
            'connection_id' => 'cid',
            'user_id' => 'uid',
            'message' => 'msg',
            'timestamp' => 'ts',
            'created_at' => 'ca',
            'updated_at' => 'ua',
            'notification' => 'notif',
            'data' => 'd'
        ];

        $shortenedData = [];
        foreach ($data as $key => $value) {
            $shortKey = $keyMap[$key] ?? $key;
            
            if (is_array($value)) {
                $shortenedData[$shortKey] = $this->shortenKeys($value);
            } else {
                $shortenedData[$shortKey] = $value;
            }
        }

        return $shortenedData;
    }

    /**
     * 연관 배열로 변환해야 하는지 판단합니다.
     *
     * @param array $array 배열
     * @return bool 변환 필요 여부
     */
    private function shouldConvertToAssociative(array $array): bool
    {
        // 이미 연관 배열인 경우
        if (array_keys($array) !== range(0, count($array) - 1)) {
            return false;
        }

        // 중복 값이 많은 경우 연관 배열로 변환하면 효율적
        $uniqueValues = array_unique($array);
        return count($uniqueValues) < count($array) * 0.7;
    }

    /**
     * 인덱스 배열을 연관 배열로 변환합니다.
     *
     * @param array $array 인덱스 배열
     * @return array 연관 배열
     */
    private function convertToAssociativeArray(array $array): array
    {
        $counts = array_count_values($array);
        return $counts;
    }

    /**
     * 크기 초과 배치를 분할합니다.
     *
     * @param array $messages 메시지들
     * @param int $batchIndex 배치 인덱스
     * @return array 분할된 배치
     */
    private function splitOversizedBatch(array $messages, int $batchIndex): array
    {
        $halfSize = intval(count($messages) / 2);
        $firstHalf = array_slice($messages, 0, $halfSize);
        $secondHalf = array_slice($messages, $halfSize);

        return [
            'batch_id' => uniqid('split_batch_' . $batchIndex . '_'),
            'batch_index' => $batchIndex,
            'is_split' => true,
            'sub_batches' => [
                $this->processBatch($firstHalf, $batchIndex . '_1'),
                $this->processBatch($secondHalf, $batchIndex . '_2')
            ],
            'message_count' => count($messages),
            'created_at' => Carbon::now()->toISOString()
        ];
    }

    /**
     * 긴급 압축을 적용합니다.
     *
     * @param array $data 데이터
     * @return array 긴급 압축된 데이터
     */
    private function applyEmergencyCompression(array $data): array
    {
        // 가장 적극적인 압축 적용
        $data = $this->applyAggressiveOptimization($data);
        $data = $this->shortenKeys($data);
        
        // 여전히 크기가 큰 경우 데이터 잘라내기
        $currentSize = strlen(json_encode($data));
        if ($currentSize > self::MAX_MESSAGE_SIZE) {
            $data = $this->truncateData($data, self::MAX_MESSAGE_SIZE * 0.9);
        }

        return $data;
    }

    /**
     * 데이터를 지정된 크기로 잘라냅니다.
     *
     * @param array $data 원본 데이터
     * @param int $maxSize 최대 크기
     * @return array 잘라낸 데이터
     */
    private function truncateData(array $data, int $maxSize): array
    {
        $truncatedData = [];
        $currentSize = 0;

        foreach ($data as $key => $value) {
            $itemSize = strlen(json_encode([$key => $value]));
            
            if ($currentSize + $itemSize > $maxSize) {
                break;
            }

            $truncatedData[$key] = $value;
            $currentSize += $itemSize;
        }

        $truncatedData['_truncated'] = true;
        $truncatedData['_original_size'] = count($data);
        $truncatedData['_truncated_size'] = count($truncatedData) - 2;

        return $truncatedData;
    }

    /**
     * 배치 통계를 업데이트합니다.
     *
     * @param int $totalMessages 총 메시지 수
     * @param int $batchCount 배치 수
     * @param int $batchSize 배치 크기
     * @return void
     */
    private function updateBatchStats(int $totalMessages, int $batchCount, int $batchSize): void
    {
        try {
            $stats = [
                'timestamp' => Carbon::now()->toISOString(),
                'total_messages' => $totalMessages,
                'batch_count' => $batchCount,
                'batch_size' => $batchSize,
                'efficiency' => $batchCount > 0 ? $totalMessages / $batchCount : 0
            ];

            Redis::lpush(self::BATCH_STATS_KEY, json_encode($stats));
            Redis::ltrim(self::BATCH_STATS_KEY, 0, 99); // 최근 100개 통계만 유지

        } catch (Exception $e) {
            Log::error('배치 통계 업데이트 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 최적화 메트릭을 업데이트합니다.
     *
     * @param int $originalSize 원본 크기
     * @param int $optimizedSize 최적화된 크기
     * @param float $compressionRatio 압축률
     * @return void
     */
    private function updateOptimizationMetrics(int $originalSize, int $optimizedSize, float $compressionRatio): void
    {
        try {
            $metrics = [
                'timestamp' => Carbon::now()->toISOString(),
                'original_size' => $originalSize,
                'optimized_size' => $optimizedSize,
                'size_reduction' => $originalSize - $optimizedSize,
                'compression_ratio' => $compressionRatio
            ];

            Redis::lpush(self::METRICS_KEY, json_encode($metrics));
            Redis::ltrim(self::METRICS_KEY, 0, 99); // 최근 100개 메트릭만 유지

        } catch (Exception $e) {
            Log::error('최적화 메트릭 업데이트 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 성능 통계를 반환합니다.
     *
     * @return array 성능 통계
     */
    public function getPerformanceStats(): array
    {
        try {
            $batchStats = Redis::lrange(self::BATCH_STATS_KEY, 0, 9); // 최근 10개
            $optimizationMetrics = Redis::lrange(self::METRICS_KEY, 0, 9); // 최근 10개

            $decodedBatchStats = array_map(function($item) {
                return json_decode($item, true);
            }, $batchStats ?: []);
            
            $decodedMetrics = array_map(function($item) {
                return json_decode($item, true);
            }, $optimizationMetrics ?: []);

            return [
                'batch_statistics' => $decodedBatchStats,
                'optimization_metrics' => $decodedMetrics,
                'summary' => $this->calculatePerformanceSummary($decodedBatchStats, $decodedMetrics),
                'generated_at' => Carbon::now()->toISOString()
            ];

        } catch (Exception $e) {
            Log::error('성능 통계 조회 실패', [
                'error' => $e->getMessage()
            ]);

            return [
                'batch_statistics' => [],
                'optimization_metrics' => [],
                'summary' => [],
                'error' => $e->getMessage(),
                'generated_at' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * 성능 요약을 계산합니다.
     *
     * @param array $batchStats 배치 통계
     * @param array $metrics 최적화 메트릭
     * @return array 성능 요약
     */
    private function calculatePerformanceSummary(array $batchStats, array $metrics): array
    {
        if (empty($batchStats) && empty($metrics)) {
            return [];
        }

        $summary = [
            'batch_summary' => [],
            'optimization_summary' => []
        ];

        // 배치 통계 요약
        if (!empty($batchStats)) {
            $totalMessages = array_sum(array_column($batchStats, 'total_messages'));
            $totalBatches = array_sum(array_column($batchStats, 'batch_count'));
            $avgBatchSize = $totalBatches > 0 ? $totalMessages / $totalBatches : 0;

            $summary['batch_summary'] = [
                'total_messages_processed' => $totalMessages,
                'total_batches_created' => $totalBatches,
                'average_batch_size' => round($avgBatchSize, 2),
                'batch_efficiency' => $totalBatches > 0 ? round($totalMessages / $totalBatches, 2) : 0
            ];
        }

        // 최적화 메트릭 요약
        if (!empty($metrics)) {
            $totalOriginalSize = array_sum(array_column($metrics, 'original_size'));
            $totalOptimizedSize = array_sum(array_column($metrics, 'optimized_size'));
            $avgCompressionRatio = count($metrics) > 0 ? 
                array_sum(array_column($metrics, 'compression_ratio')) / count($metrics) : 0;

            $summary['optimization_summary'] = [
                'total_original_size' => $totalOriginalSize,
                'total_optimized_size' => $totalOptimizedSize,
                'total_size_reduction' => $totalOriginalSize - $totalOptimizedSize,
                'average_compression_ratio' => round($avgCompressionRatio, 2)
            ];
        }

        return $summary;
    }

    /**
     * 성능 메트릭을 초기화합니다.
     *
     * @return bool 초기화 성공 여부
     */
    public function resetPerformanceMetrics(): bool
    {
        try {
            Redis::del(self::BATCH_STATS_KEY);
            Redis::del(self::METRICS_KEY);

            Log::info('성능 메트릭이 초기화되었습니다');
            return true;

        } catch (Exception $e) {
            Log::error('성능 메트릭 초기화 실패', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}