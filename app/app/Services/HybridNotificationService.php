<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Pusher\Pusher;
use Exception;

/**
 * 하이브리드 알림 서비스
 * 
 * SSE와 Pusher를 병행 운영하여 점진적 마이그레이션을 지원합니다.
 * 클라이언트의 SSE 지원 여부에 따라 적절한 전송 방식을 선택합니다.
 */
class HybridNotificationService
{
    private NotificationManager $notificationManager;
    private ?Pusher $pusher = null;
    private bool $hybridModeEnabled;
    private bool $pusherFallbackEnabled;
    private int $migrationPercentage;

    public function __construct(NotificationManager $notificationManager)
    {
        $this->notificationManager = $notificationManager;
        $this->hybridModeEnabled = config('sse.migration.hybrid_mode', false);
        $this->pusherFallbackEnabled = config('sse.migration.pusher_fallback_enabled', false);
        $this->migrationPercentage = config('sse.migration.migration_percentage', 0);
        
        $this->initializePusher();
    }

    /**
     * Pusher 클라이언트 초기화
     */
    private function initializePusher(): void
    {
        if (!$this->pusherFallbackEnabled && !$this->hybridModeEnabled) {
            return;
        }

        try {
            $this->pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                config('broadcasting.connections.pusher.options', [])
            );
        } catch (Exception $e) {
            Log::error('Pusher 초기화 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 전체 사용자에게 알림 전송
     */
    public function sendToAll(array $data, string $type = 'notification'): void
    {
        $messageId = $this->generateMessageId();
        
        // 메시지 중복 방지를 위한 캐시 키 설정
        $cacheKey = "hybrid_message:{$messageId}";
        
        if (Cache::has($cacheKey)) {
            Log::warning('중복 메시지 전송 시도 차단', ['message_id' => $messageId]);
            return;
        }
        
        Cache::put($cacheKey, true, 300); // 5분간 중복 방지
        
        $this->logMessageSend('broadcast', $data, $type);
        
        // SSE로 전송
        if ($this->shouldUseSse()) {
            try {
                $this->notificationManager->sendToAll($data, $type);
                $this->recordDeliveryMethod('sse', 'broadcast');
            } catch (Exception $e) {
                Log::error('SSE 전체 알림 전송 실패', [
                    'error' => $e->getMessage(),
                    'data' => $data
                ]);
                
                // SSE 실패 시 Pusher 폴백
                if ($this->pusherFallbackEnabled) {
                    $this->sendViaPusher('global-notifications', $data, $type);
                }
            }
        }
        
        // 하이브리드 모드에서 Pusher도 함께 전송
        if ($this->hybridModeEnabled && $this->pusher) {
            $this->sendViaPusher('global-notifications', $data, $type);
        }
    }

    /**
     * 특정 사용자에게 알림 전송
     */
    public function sendToUser(int $userId, array $data, string $type = 'notification'): void
    {
        $messageId = $this->generateMessageId();
        $cacheKey = "hybrid_user_message:{$userId}:{$messageId}";
        
        if (Cache::has($cacheKey)) {
            Log::warning('중복 사용자 메시지 전송 시도 차단', [
                'user_id' => $userId,
                'message_id' => $messageId
            ]);
            return;
        }
        
        Cache::put($cacheKey, true, 300);
        
        $this->logMessageSend('user', $data, $type, $userId);
        
        // 사용자의 SSE 지원 여부 확인
        $userSupportsSSE = $this->checkUserSSESupport($userId);
        
        if ($userSupportsSSE && $this->shouldUseSse()) {
            try {
                $this->notificationManager->sendToUser($userId, $data, $type);
                $this->recordDeliveryMethod('sse', 'user', $userId);
            } catch (Exception $e) {
                Log::error('SSE 사용자 알림 전송 실패', [
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'data' => $data
                ]);
                
                // SSE 실패 시 Pusher 폴백
                if ($this->pusherFallbackEnabled) {
                    $this->sendViaPusher("user.{$userId}", $data, $type);
                }
            }
        } else {
            // SSE 미지원 사용자는 Pusher로 전송
            if ($this->pusher) {
                $this->sendViaPusher("user.{$userId}", $data, $type);
                $this->recordDeliveryMethod('pusher', 'user', $userId);
            }
        }
        
        // 하이브리드 모드에서는 양쪽 모두 전송
        if ($this->hybridModeEnabled && $this->pusher && $userSupportsSSE) {
            $this->sendViaPusher("user.{$userId}", $data, $type);
        }
    }

    /**
     * Pusher를 통한 메시지 전송
     */
    private function sendViaPusher(string $channel, array $data, string $type): void
    {
        if (!$this->pusher) {
            return;
        }

        try {
            $this->pusher->trigger($channel, $type, [
                'data' => $data,
                'timestamp' => now()->toISOString(),
                'source' => 'pusher'
            ]);
            
            $this->recordDeliveryMethod('pusher', 'channel', null, $channel);
        } catch (Exception $e) {
            Log::error('Pusher 메시지 전송 실패', [
                'channel' => $channel,
                'type' => $type,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
        }
    }

    /**
     * SSE 사용 여부 결정
     */
    private function shouldUseSse(): bool
    {
        // 마이그레이션 비율에 따른 확률적 선택
        if ($this->migrationPercentage > 0 && $this->migrationPercentage < 100) {
            $random = rand(1, 100);
            return $random <= $this->migrationPercentage;
        }
        
        return true;
    }

    /**
     * 사용자의 SSE 지원 여부 확인
     */
    private function checkUserSSESupport(int $userId): bool
    {
        $cacheKey = "user_sse_support:{$userId}";
        
        // 캐시에서 먼저 확인
        $cached = Cache::get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // 사용자의 최근 연결 기록 확인
        $hasSSEConnection = Cache::has("sse:user_connections:{$userId}");
        
        // 기본값은 SSE 지원으로 가정 (점진적 마이그레이션)
        $supportsSSE = $hasSSEConnection || ($this->migrationPercentage > 50);
        
        // 1시간 동안 캐시
        Cache::put($cacheKey, $supportsSSE, 3600);
        
        return $supportsSSE;
    }

    /**
     * 클라이언트 SSE 지원 여부 업데이트
     */
    public function updateUserSSESupport(int $userId, bool $supportsSSE): void
    {
        $cacheKey = "user_sse_support:{$userId}";
        Cache::put($cacheKey, $supportsSSE, 3600);
        
        Log::info('사용자 SSE 지원 상태 업데이트', [
            'user_id' => $userId,
            'supports_sse' => $supportsSSE
        ]);
    }

    /**
     * 메시지 ID 생성
     */
    private function generateMessageId(): string
    {
        return uniqid('msg_', true);
    }

    /**
     * 메시지 전송 로그 기록
     */
    private function logMessageSend(string $target, array $data, string $type, ?int $userId = null): void
    {
        Log::info('하이브리드 알림 전송', [
            'target' => $target,
            'type' => $type,
            'user_id' => $userId,
            'data_size' => strlen(json_encode($data)),
            'hybrid_mode' => $this->hybridModeEnabled,
            'pusher_fallback' => $this->pusherFallbackEnabled,
            'migration_percentage' => $this->migrationPercentage
        ]);
    }

    /**
     * 전송 방식 통계 기록
     */
    private function recordDeliveryMethod(string $method, string $type, ?int $userId = null, ?string $channel = null): void
    {
        $key = "delivery_stats:{$method}:" . date('Y-m-d-H');
        Cache::increment($key, 1);
        Cache::put($key, Cache::get($key, 0), 86400); // 24시간 보관
        
        // 상세 로그
        Log::debug('메시지 전송 방식 기록', [
            'method' => $method,
            'type' => $type,
            'user_id' => $userId,
            'channel' => $channel,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * 전송 통계 조회
     */
    public function getDeliveryStats(int $hours = 24): array
    {
        $stats = [
            'sse' => 0,
            'pusher' => 0,
            'total' => 0
        ];
        
        for ($i = 0; $i < $hours; $i++) {
            $hour = now()->subHours($i)->format('Y-m-d-H');
            $sseKey = "delivery_stats:sse:{$hour}";
            $pusherKey = "delivery_stats:pusher:{$hour}";
            
            $stats['sse'] += Cache::get($sseKey, 0);
            $stats['pusher'] += Cache::get($pusherKey, 0);
        }
        
        $stats['total'] = $stats['sse'] + $stats['pusher'];
        
        return $stats;
    }

    /**
     * 하이브리드 모드 설정 업데이트
     */
    public function updateHybridSettings(array $settings): void
    {
        if (isset($settings['hybrid_mode'])) {
            $this->hybridModeEnabled = $settings['hybrid_mode'];
            Config::set('sse.migration.hybrid_mode', $settings['hybrid_mode']);
        }
        
        if (isset($settings['pusher_fallback_enabled'])) {
            $this->pusherFallbackEnabled = $settings['pusher_fallback_enabled'];
            Config::set('sse.migration.pusher_fallback_enabled', $settings['pusher_fallback_enabled']);
        }
        
        if (isset($settings['migration_percentage'])) {
            $this->migrationPercentage = max(0, min(100, $settings['migration_percentage']));
            Config::set('sse.migration.migration_percentage', $this->migrationPercentage);
        }
        
        Log::info('하이브리드 설정 업데이트', $settings);
    }

    /**
     * 마이그레이션 진행률 조회
     */
    public function getMigrationProgress(): array
    {
        $stats = $this->getDeliveryStats();
        $ssePercentage = $stats['total'] > 0 ? round(($stats['sse'] / $stats['total']) * 100, 2) : 0;
        
        return [
            'migration_percentage' => config('sse.migration.migration_percentage', $this->migrationPercentage),
            'actual_sse_usage' => $ssePercentage,
            'hybrid_mode' => config('sse.migration.hybrid_mode', $this->hybridModeEnabled),
            'pusher_fallback' => config('sse.migration.pusher_fallback_enabled', $this->pusherFallbackEnabled),
            'delivery_stats' => $stats
        ];
    }
}