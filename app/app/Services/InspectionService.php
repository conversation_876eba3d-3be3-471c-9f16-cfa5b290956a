<?php

namespace App\Services;

use App\Exports\UndeliveredQaidExport;
use App\Models\Product;
use App\Models\ProductLog;
use App\Models\Req;
use App\Models\User;
use App\Models\WorkStatus;
use Illuminate\Support\Facades\DB;
use Throwable;

class InspectionService
{
    protected WorkStatusService $workStatusService;

    public function __construct(WorkStatusService $workStatusService)
    {
        $this->workStatusService = $workStatusService;
    }

    /**
     * 검수 통과(미입고 처리)
     * - 미입고 상품이 있다면 상품 데이터를 엑셀로 만들어서 텔레그램으로 전송
     * @param Req $req
     * @param User $user
     * @return void
     * @throws Throwable
     */
    public function processInspectionPass(Req $req, User $user): void
    {
        $subject = str_repeat("=", 100) . "\n[$req->id]검수 통과 및 미입고 처리 작업 시작(" .  date("Y-m-d H:i:s") . ")";
        SimpleLogService::info('req', $subject);

        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_INSPECTION_PASS, # 검수통과
                WorkStatus::LINK_UNDELIVERED, # 미입고
            ]);

            $now = now();
            ProductLog::insert([
                'product_id' => null,
                'model_type' => 'App\Models\Req',
                'model_id' => $req->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_INSPECTION_PASS],
                'user_id' => $user->id,
                'memo' => "[$req->req_at]검수 통과",
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            // 요청서에 남아 있는 상품의 상태를 미입고 상태로 만든다.
            $products = $req->products()
                ->where(function ($query) {
                    $query->where('checked_status', Product::CHECKED_STATUS_UNCHECKED)
                        ->orWhere(function ($subQuery) {
                            $subQuery->where('checked_status', Product::CHECKED_STATUS_CHECKED)
                                ->whereNull('checked_user_id')
                                ->whereNull('checked_at');
                        });
                })
                ->get();

            $productLogs = [];
            foreach ($products as $product) {
                $product->update([
                    'checked_status' => Product::CHECKED_STATUS_UNDELIVERED,
                    'checked_user_id' => $user->id,
                ]);

                // 상태 변환/통계::미입고
                // [일괄 검수통과]검수 대기 -> 미입고 처리
                $productLogs[] = [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\Product',
                    'model_id' => $product->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_UNDELIVERED],
                    'user_id' => $user->id,
                    'memo' => "[$req->req_at]미입고 상품 QAID: [$product->qaid]",
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            if (!empty($productLogs)) {
                ProductLog::insert($productLogs);
            }

            // 요청서 업데이트
            $req->status = Req::STATUS_CHECKED;
            $req->checked_user_id = $user->id;
            $req->checked_at = $now;

            $countService = new CountService();
            $countUndelivered = $countService->getUndeliveredCount($req->id);
            $countChecked = $countService->getCheckedCount($req->id);

            // 요청서 카운터 업데이트
            $reqCount = $req->reqCount;
            $reqCount->update([
                'unchecked' => 0,
                'undelivered' => $countUndelivered,
                'checked' => $countChecked,
            ]);

            $req->save();

            DB::commit();

            // 미입고 내역이 존재 한다면 엑셀 파일 생성 및 텔레그램으로 보내기
            if (!empty($products)) {
                $export = new UndeliveredQaidExport($req, $user, $products);
                $export->export();
            }

            SimpleLogService::info('req', '일괄 검수통과 처리 성공', [
                'req_id' => $req->id,
                'checked_at' => $now->format('Y-m-d H:i:s'),
                '검수통과(checked)' => $countChecked,
                '미입고(undelivered)' => $countUndelivered,
            ]);
        } catch (Throwable $e) {
            DB::rollBack();

            $message = "[{$req->id}] 요청서 검수 통과 및 미입고 처리 오류 발생: ";

            $telegram = new TelegramService();
            $telegram->sendMessageToTeam($message . $e->getMessage());

            SimpleLogService::error('req', $message . "(ReqService::processInspectionPass)", [], $e);

            throw $e;
        }
    }
}
