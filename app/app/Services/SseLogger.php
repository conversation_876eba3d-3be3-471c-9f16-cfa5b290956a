<?php

namespace App\Services;

use App\Helpers\HelperLibrary;
use App\Models\ToSlack;
use App\Models\User;
use App\Notifications\SlackNotification;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * SSE 시스템 전용 로깅 서비스
 * 
 * 연결, 메시지, 오류 로그를 구조화하여 관리하고
 * 민감 정보 자동 마스킹 및 로그 레벨 관리를 제공합니다.
 */
class SseLogger
{
    private const CHANNEL = 'sse';
    private const MAX_MESSAGE_SIZE = 2048; // 2KB
    private const MAX_TRACE_SIZE = 1500;
    
    /**
     * 민감 정보 마스킹 패턴
     */
    private const SENSITIVE_PATTERNS = [
        'password' => '***',
        'token' => '***',
        'secret' => '***',
        'key' => '***',
        'authorization' => '***',
        'cookie' => '***',
        'session' => '***',
    ];
    
    /**
     * SSE 연결 로그 기록
     */
    public function logConnection(string $connectionId, ?User $user = null, array $context = []): void
    {
        $logData = [
            'connection_id' => $connectionId,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'ip_address' => request()->ip(),
            'user_agent' => $this->maskSensitiveData(request()->userAgent()),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('info', 'SSE 연결 설정', $mergedContext);
    }
    
    /**
     * SSE 연결 해제 로그 기록
     */
    public function logDisconnection(string $connectionId, ?User $user = null, string $reason = 'normal', array $context = []): void
    {
        $logData = [
            'connection_id' => $connectionId,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'disconnect_reason' => $reason,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $level = $reason === 'error' ? 'warning' : 'info';
        $this->writeLog($level, 'SSE 연결 해제', $mergedContext);
    }
    
    /**
     * 메시지 전송 로그 기록
     */
    public function logMessage(string $type, array $data, array $recipients, bool $success = true, array $context = []): void
    {
        $logData = [
            'message_type' => $type,
            'message_data' => $this->maskSensitiveData($this->truncateData($data)),
            'recipient_count' => count($recipients),
            'recipients' => $this->truncateArray($recipients, 10),
            'success' => $success,
            'message_size' => strlen(json_encode($data)),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $level = $success ? 'info' : 'warning';
        $message = $success ? 'SSE 메시지 전송 성공' : 'SSE 메시지 전송 실패';
        
        $this->writeLog($level, $message, $mergedContext);
    }
    
    /**
     * 브로드캐스트 메시지 로그 기록
     */
    public function logBroadcast(string $type, array $data, int $connectionCount, array $context = []): void
    {
        $logData = [
            'broadcast_type' => $type,
            'message_data' => $this->maskSensitiveData($this->truncateData($data)),
            'connection_count' => $connectionCount,
            'message_size' => strlen(json_encode($data)),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('info', 'SSE 브로드캐스트 전송', $mergedContext);
    }
    
    /**
     * 오프라인 알림 저장 로그 기록
     */
    public function logOfflineNotification(int $userId, array $data, array $context = []): void
    {
        $logData = [
            'user_id' => $userId,
            'notification_data' => $this->maskSensitiveData($this->truncateData($data)),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('info', 'SSE 오프라인 알림 저장', $mergedContext);
    }
    
    /**
     * 하트비트 실패 로그 기록
     */
    public function logHeartbeatFailure(string $connectionId, array $context = []): void
    {
        $logData = [
            'connection_id' => $connectionId,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('warning', 'SSE 하트비트 실패', $mergedContext);
    }
    
    /**
     * 인증 실패 로그 기록
     */
    public function logAuthenticationFailure(string $reason, array $context = []): void
    {
        $logData = [
            'auth_failure_reason' => $reason,
            'ip_address' => request()->ip(),
            'user_agent' => $this->maskSensitiveData(request()->userAgent()),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('warning', 'SSE 인증 실패', $mergedContext);
    }
    
    /**
     * Rate Limiting 로그 기록
     */
    public function logRateLimitExceeded(string $ip, int $attemptCount, array $context = []): void
    {
        $logData = [
            'ip_address' => $ip,
            'attempt_count' => $attemptCount,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('warning', 'SSE Rate Limit 초과', $mergedContext);
    }
    
    /**
     * 시스템 오류 로그 기록
     */
    public function logError(string $message, ?Throwable $exception = null, array $context = []): void
    {
        $logData = [
            'error_message' => $message,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        if ($exception) {
            $logData['exception'] = [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'class' => get_class($exception),
                'file' => $exception->getFile() . ':' . $exception->getLine(),
                'trace' => $this->formatTrace($exception),
            ];
        }
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('error', 'SSE 시스템 오류', $mergedContext);
    }
    
    /**
     * 크리티컬 오류 로그 기록
     */
    public function logCriticalError(string $message, ?Throwable $exception = null, array $context = []): void
    {
        $logData = [
            'critical_error_message' => $message,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        if ($exception) {
            $logData['exception'] = [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'class' => get_class($exception),
                'file' => $exception->getFile() . ':' . $exception->getLine(),
                'trace' => $this->formatTrace($exception),
            ];
        }
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('critical', 'SSE 크리티컬 오류', $mergedContext);
    }
    
    /**
     * 성능 메트릭 로그 기록
     */
    public function logPerformanceMetrics(array $metrics, array $context = []): void
    {
        $logData = [
            'metrics' => $metrics,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('info', 'SSE 성능 메트릭', $mergedContext);
    }
    
    /**
     * 보안 이벤트 로그 기록
     */
    public function logSecurityEvent(string $eventType, string $description, array $context = []): void
    {
        $logData = [
            'security_event_type' => $eventType,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => $this->maskSensitiveData(request()->userAgent()),
            'timestamp' => now()->toDateTimeString(),
        ];
        
        $mergedContext = array_merge($context, $logData);
        $this->writeLog('warning', 'SSE 보안 이벤트', $mergedContext);
    }
    
    /**
     * 실제 로그 작성
     */
    private function writeLog(string $level, string $message, array $context = []): void
    {
        // 기본 컨텍스트 정보 추가
        $user = auth()->user();
        $baseContext = [
            'service' => 'SSE',
            'user' => [
                'id' => $user?->id,
                'name' => $user?->name,
                'username' => $user?->username,
            ],
            'request' => [
                'ip' => request()->ip(),
                'method' => request()->method(),
                'url' => request()->fullUrl(),
                'path' => request()->path(),
            ],
        ];
        
        // 컨텍스트 병합, 데이터 크기 제한, 민감 정보 마스킹 순서로 처리
        $mergedContext = array_merge($context, $baseContext);
        $truncatedContext = $this->truncateContextData($mergedContext);
        $fullContext = $this->maskSensitiveData($truncatedContext);
        
        // UTF-8 인코딩 문제 방지
        $cleanedMessage = HelperLibrary::sanitizeUtf8($message);
        
        // 파일 로그에 기록
        Log::channel(self::CHANNEL)->$level("🔗 " . $cleanedMessage, $fullContext);
        
        // 중요 에러는 Slack 알림
        if (in_array($level, ['error', 'critical', 'alert', 'emergency'])) {
            $this->sendSlackNotification($level, $cleanedMessage, $fullContext);
        }
    }
    
    /**
     * 컨텍스트 데이터 크기 제한
     */
    private function truncateContextData(array $context): array
    {
        $truncated = [];
        
        foreach ($context as $key => $value) {
            $truncated[$key] = $this->truncateData($value);
        }
        
        return $truncated;
    }
    
    /**
     * Slack 알림 전송
     */
    private function sendSlackNotification(string $level, string $message, array $context): void
    {
        try {
            $toSlack = new ToSlack(self::CHANNEL);
            $toSlack->notify(new SlackNotification($level, "🔗 " . $message, $context));
        } catch (Throwable $slackError) {
            // Slack 전송 실패 시 파일 로그에만 기록
            $errorMessage = "Slack 알림 전송 실패: " . $slackError->getMessage();
            
            if (str_contains($slackError->getMessage(), 'Malformed UTF-8')) {
                $errorMessage = "Slack 알림 전송 실패: UTF-8 인코딩 문제";
            }
            
            Log::channel(self::CHANNEL)->error($errorMessage, [
                'original_error' => $message,
                'slack_error' => $slackError->getMessage(),
                'error_type' => get_class($slackError),
            ]);
        }
    }
    
    /**
     * 민감 정보 마스킹
     */
    private function maskSensitiveData($data): mixed
    {
        if (is_string($data)) {
            return $this->maskSensitiveString($data);
        }
        
        if (is_array($data)) {
            return $this->maskSensitiveArray($data);
        }
        
        return $data;
    }
    
    /**
     * 문자열에서 민감 정보 마스킹
     */
    private function maskSensitiveString(string $data): string
    {
        // 문자열 길이가 너무 긴 경우 먼저 잘라냄
        if (strlen($data) > 100) {
            foreach (self::SENSITIVE_PATTERNS as $pattern => $replacement) {
                if (stripos($data, $pattern) !== false) {
                    return substr($data, 0, 20) . '...[마스킹됨]';
                }
            }
        } else {
            foreach (self::SENSITIVE_PATTERNS as $pattern => $replacement) {
                if (stripos($data, $pattern) !== false) {
                    return substr($data, 0, min(20, strlen($data))) . '...[마스킹됨]';
                }
            }
        }
        
        return $data;
    }
    
    /**
     * 배열에서 민감 정보 마스킹
     */
    private function maskSensitiveArray(array $data): array
    {
        $masked = [];
        
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);
            
            // 키가 민감 정보인 경우
            if (array_key_exists($lowerKey, self::SENSITIVE_PATTERNS)) {
                $masked[$key] = self::SENSITIVE_PATTERNS[$lowerKey];
            } else {
                $masked[$key] = $this->maskSensitiveData($value);
            }
        }
        
        return $masked;
    }
    
    /**
     * 데이터 크기 제한
     */
    private function truncateData($data): mixed
    {
        if (is_string($data)) {
            return strlen($data) > self::MAX_MESSAGE_SIZE 
                ? substr($data, 0, self::MAX_MESSAGE_SIZE) . '...[잘림]'
                : $data;
        }
        
        if (is_array($data)) {
            $json = json_encode($data);
            if (strlen($json) > self::MAX_MESSAGE_SIZE) {
                return ['message' => '데이터가 너무 큽니다. 크기: ' . strlen($json) . ' bytes'];
            }
            return $data;
        }
        
        return $data;
    }
    
    /**
     * 배열 크기 제한
     */
    private function truncateArray(array $data, int $maxItems): array
    {
        if (count($data) <= $maxItems) {
            return $data;
        }
        
        $truncated = array_slice($data, 0, $maxItems);
        $truncated[] = '...[' . (count($data) - $maxItems) . '개 항목 더 있음]';
        
        return $truncated;
    }
    
    /**
     * 스택 트레이스 포맷팅
     */
    private function formatTrace(Throwable $exception): string
    {
        $trace = $exception->getTraceAsString();
        
        if (strlen($trace) > self::MAX_TRACE_SIZE) {
            $trace = substr($trace, 0, self::MAX_TRACE_SIZE) . '...[잘림]';
        }
        
        return $trace;
    }
    
    /**
     * 로그 레벨 검증
     */
    private function isValidLogLevel(string $level): bool
    {
        return in_array($level, [
            'debug', 'info', 'notice', 'warning', 
            'error', 'critical', 'alert', 'emergency'
        ]);
    }
    
    /**
     * 디버그 로그 (개발 환경에서만)
     */
    public function debug(string $message, array $context = []): void
    {
        if (app()->environment('local', 'development')) {
            $this->writeLog('debug', $message, $context);
        }
    }
    
    /**
     * 정보 로그
     */
    public function info(string $message, array $context = []): void
    {
        $this->writeLog('info', $message, $context);
    }
    
    /**
     * 경고 로그
     */
    public function warning(string $message, array $context = []): void
    {
        $this->writeLog('warning', $message, $context);
    }
    
    /**
     * 에러 로그
     */
    public function error(string $message, array $context = []): void
    {
        $this->writeLog('error', $message, $context);
    }
}