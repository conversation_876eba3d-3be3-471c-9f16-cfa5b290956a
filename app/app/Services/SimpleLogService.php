<?php

namespace App\Services;

use App\Helpers\HelperLibrary;
use App\Models\ToSlack;
use App\Notifications\SlackNotification;
use Illuminate\Support\Facades\Log;
use Throwable;

class SimpleLogService
{
    /**
     * 간단한 로그 기록 메서드
     *
     * @param string $channel 로그 채널
     * @param string $title 로그 메시지
     * @param string $level 로그 레벨 (debug, info, notice, warning, error, critical, alert, emergency)
     * @param array $context 추가 정보
     * @param Throwable|null $exception 예외 객체
     * @return void
     */
    public static function log(
        string     $channel,
        string     $title,
        string     $level = 'info',
        array      $context = [],
        ?Throwable $exception = null
    ): void {
        // 기본 컨텍스트 정보 추가
        $user = auth()->user();
        $baseContext = [
            'timestamp' => now()->toDateTimeString(),
            'user' => [
                'id' => $user->id ?? null,
                'name' => $user->name ?? null,
                'username' => $user->username ?? null,
            ],
            'ip' => request()->ip(),
            'method' => request()->method(),
            'url' => request()->fullUrl(),
            'path' => request()->path(),
            'user_agent' => request()->userAgent(),
        ];

        // 예외 정보 추가
        if ($exception) {
            $baseContext['exception'] = [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'class' => get_class($exception),
                'file' => $exception->getFile() . ':' . $exception->getLine(),
                'trace' => self::formatTrace($exception),
            ];
        }

        // 사용자 컨텍스트와 병합
        $fullContext = array_merge($context, $baseContext);

        // UTF-8 인코딩 문제 방지를 위해 데이터 정리
        $cleanedTitle = HelperLibrary::sanitizeUtf8($title);

        // 파일 로그에 기록
        Log::channel($channel)->$level("🚨".$cleanedTitle, $fullContext);

        // 중요 에러는 Slack/Telegram에도 알림
        if (in_array($level, ['error', 'critical', 'alert', 'emergency'])) {
            try {
                $toSlack = new ToSlack($channel);
                $toSlack->notify(new SlackNotification($level, "🚨".$cleanedTitle, $fullContext));
            } catch (Throwable $slackError) {
                // Slack 전송 실패 시 파일 로그에만 기록
                $slackErrorMessage = "Slack 알림 전송 실패: " . $slackError->getMessage();

                // UTF-8 인코딩 문제인 경우 더 안전한 메시지 사용
                if (str_contains($slackError->getMessage(), 'Malformed UTF-8')) {
                    $slackErrorMessage = "Slack 알림 전송 실패: UTF-8 인코딩 문제";
                }

                Log::channel($channel)->error($slackErrorMessage, [
                    'original_error' => $cleanedTitle,
                    'slack_error' => $slackError->getMessage(),
                    'error_type' => get_class($slackError),
                ]);
            }
        }
    }

    /**
     * 스택 트레이스를 간결하게 포맷팅
     */
    private static function formatTrace(Throwable $exception): string
    {
        $trace = $exception->getTraceAsString();
        // 너무 긴 트레이스는 줄임
        if (strlen($trace) > 1500) {
            $trace = substr($trace, 0, 1500) . '...';
        }
        return $trace;
    }

    /**
     * 에러 로깅 단축 메서드
     */
    public static function error(string $channel, string $title, array $context = [], ?Throwable $exception = null): void
    {
        self::log($channel, $title, 'error', $context, $exception);
    }

    /**
     * 크리티컬 로깅 단축 메서드
     */
    public static function critical(string $channel, string $title, array $context = [], ?Throwable $exception = null): void
    {
        self::log($channel, $title, 'critical', $context, $exception);
    }

    /**
     * 긴급(emergency) 로깅 단축 메서드
     */
    public static function emergency(string $channel, string $title, array $context = [], ?Throwable $exception = null): void
    {
        self::log($channel, $title, 'emergency', $context, $exception);
    }

    /**
     * 경고 로깅 단축 메서드
     */
    public static function alert(string $channel, string $title, array $context = [], ?Throwable $exception = null): void
    {
        self::log($channel, $title, 'alert', $context, $exception);
    }

    /**
     * 경고 로깅 단축 메서드
     */
    public static function warning(string $channel, string $title, array $context = [], ?Throwable $exception = null): void
    {
        self::log($channel, $title, 'warning', $context, $exception);
    }

    /**
     * 정보 로깅 단축 메서드
     */
    public static function info(string $channel, string $title, array $context = []): void
    {
        self::log($channel, $title, 'info', $context, null);
    }

    /**
     * 정보 로깅 단축 메서드
     */
    public static function notice(string $channel, string $title, array $context = []): void
    {
        self::log($channel, $title, 'notice', $context, null);
    }

    /**
     * 디버그 로깅 단축 메서드
     */
    public static function debug(string $channel, string $title, array $context = []): void
    {
        self::log($channel, $title, 'debug', $context);
    }
}
