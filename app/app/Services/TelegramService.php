<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;
use InvalidArgumentException;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Telegram\Bot\FileUpload\InputFile;
use Telegram\Bot\Laravel\Facades\Telegram;

class TelegramService
{
    /**
     * 지원하는 팀 목록
     */
    private const SUPPORTED_TEAMS = [
        'dev', 'admin', 'req',
    ];

    /**
     * 팀 설정이 유효한지 확인
     */
    private function isValidTeam(string $team): bool
    {
        return in_array($team, self::SUPPORTED_TEAMS);
    }

    /**
     * 팀별 chat ID 반환
     */
    private function getChatId(string $team): string
    {
        if (!$this->isValidTeam($team)) {
            throw new InvalidArgumentException("지원하지 않는 팀입니다: {$team}");
        }

        $teamConfig = config("services.telegram.teams.{$team}");

        if (!$teamConfig || !isset($teamConfig['chat_id'])) {
            // 팀별 설정이 없으면 기본 설정 사용
            return config('services.telegram.teams.dev.chat_id');
        }

        return $teamConfig['chat_id'];
    }

    /**
     * 특정 팀으로 메시지 전송
     */
    public function sendMessageToTeam(string $message, string $team = 'dev'): void
    {
        try {
            $chatId = $this->getChatId($team);

            // 팀명을 메시지에 추가
            $formattedMessage = "[{$team}] " . $message;

            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => $formattedMessage,
                'parse_mode' => 'HTML'
            ]);
        } catch (TelegramSDKException $e) {
            // Telegram API 관련 예외 처리
            SimpleLogService::error('telegram', 'Telegram 메시지 전송 실패', [
                'team' => $team,
                'chat_id' => $this->getChatId($team)
            ], $e);
        } catch (Exception $e) {
            SimpleLogService::error('telegram', '메시지 전송 중 예상치 못한 오류 발생', [
                'team' => $team,
                'chat_id' => $this->getChatId($team)
            ], $e);
        }
    }

    /**
     * 여러 팀에 동시에 메시지 전송
     */
    public function sendMessageToMultipleTeams(string $message, array $teams): void
    {
        foreach ($teams as $team) {
            if ($this->isValidTeam($team)) {
                $this->sendMessageToTeam($message, $team);
            } else {
                SimpleLogService::error('telegram', '지원하지 않는 팀으로 메시지 전송 시도', [
                    'invalid_team' => $team,
                    'supported_teams' => self::SUPPORTED_TEAMS
                ]);
            }
        }
    }

    /**
     * 모든 팀에 메시지 전송
     */
    public function sendMessageToAllTeams(string $message): void
    {
        $this->sendMessageToMultipleTeams($message, self::SUPPORTED_TEAMS);
    }

    /**
     * 특정 팀으로 문서 전송
     * @param  string  $path storage 안의 경로와 파일명이 필요함 ex: temp/test.xlsx
     * @param  string  $filename
     * @param  string  $caption
     * @param  string  $team
     * @return void
     */
    public function sendDocumentToTeam(string $path, string $filename, string $caption = '', string $team = 'dev'): void
    {
        try {
            $chatId = $this->getChatId($team);

            // 팀명을 캡션에 추가
            $formattedCaption = "[{$team}] " . $caption;

            Telegram::sendDocument([
                'chat_id' => $chatId,
                'document' => InputFile::createFromContents(Storage::get($path), $filename),
                'caption' => $formattedCaption
            ]);
        } catch (TelegramSDKException $e) {
            // Telegram API 관련 예외 처리
            SimpleLogService::error('telegram', 'Telegram 문서 전송 실패', [
                'team' => $team,
                'chat_id' => $this->getChatId($team),
                'filename' => $filename
            ], $e);
        } catch (Exception $e) {
            SimpleLogService::error('telegram', '문서 전송 중 예상치 못한 오류 발생', [
                'team' => $team,
                'chat_id' => $this->getChatId($team),
                'filename' => $filename
            ], $e);
        }
    }

    /**
     * 여러 팀에 동시에 메시지 전송
     */
    public function sendDocumentToMultipleTeams(string $path, string $filename, string $caption = '', array $teams = []): void
    {
        foreach ($teams as $team) {
            if ($this->isValidTeam($team)) {
                $this->sendDocumentToTeam($path, $filename, $caption, $team);
            } else {
                SimpleLogService::error('telegram', '지원하지 않는 팀으로 메시지 전송 시도', [
                    'invalid_team' => $team,
                    'supported_teams' => self::SUPPORTED_TEAMS
                ]);
            }
        }
    }

    /**
     * 지원하는 팀 목록 반환
     */
    public function getSupportedTeams(): array
    {
        return self::SUPPORTED_TEAMS;
    }

    /**
     * 팀 설정 상태 확인
     */
    public function getTeamStatus(): array
    {
        $status = [];

        foreach (self::SUPPORTED_TEAMS as $team) {
            $teamConfig = config("services.telegram.teams.{$team}");
            $status[$team] = [
                'configured' => !empty($teamConfig) && isset($teamConfig['chat_id']),
                'chat_id' => $teamConfig['chat_id'] ?? null,
            ];
        }

        return $status;
    }
}
