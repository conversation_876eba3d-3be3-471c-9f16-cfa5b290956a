<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Exception;
use Throwable;

/**
 * SSE 오류 복구 서비스
 * 
 * 연결 오류, Redis 연결 실패, 메시지 전송 오류 등을 처리하고
 * 시스템의 안정성을 보장하기 위한 복구 메커니즘을 제공합니다.
 */
class ErrorRecoveryService
{
    /**
     * 메모리 기반 임시 저장소
     */
    private static array $memoryStorage = [];
    
    /**
     * 재시도 설정
     */
    private const MAX_RETRY_ATTEMPTS = 3;
    private const BASE_RETRY_DELAY = 1; // 초
    private const MAX_RETRY_DELAY = 30; // 초
    
    /**
     * 폴백 저장소 키
     */
    private const FALLBACK_CONNECTIONS_KEY = 'fallback_connections';
    private const FALLBACK_MESSAGES_KEY = 'fallback_messages';
    private const FALLBACK_STATS_KEY = 'fallback_stats';
    
    /**
     * 오류 임계값
     */
    private const ERROR_THRESHOLD = 10; // 10회 연속 오류 시 폴백 모드
    private const RECOVERY_CHECK_INTERVAL = 60; // 60초마다 복구 확인
    
    /**
     * 오류 카운터
     */
    private static array $errorCounters = [
        'redis' => 0,
        'connection' => 0,
        'message' => 0
    ];

    /**
     * 연결 오류를 처리하고 복구를 시도합니다.
     *
     * @param string $connectionId 연결 ID
     * @param Exception $exception 발생한 예외
     * @return bool 복구 성공 여부
     */
    public function handleConnectionError(string $connectionId, Exception $exception): bool
    {
        try {
            Log::warning('SSE 연결 오류 발생', [
                'connection_id' => $connectionId,
                'error' => $exception->getMessage(),
                'error_type' => get_class($exception)
            ]);

            // 오류 카운터 증가
            self::$errorCounters['connection']++;

            // 연결 정리 시도
            $this->cleanupFailedConnection($connectionId);

            // 재시도 메커니즘 적용
            $recovered = $this->retryWithBackoff(function() use ($connectionId) {
                return $this->attemptConnectionRecovery($connectionId);
            }, 'connection_recovery');

            if ($recovered) {
                // 복구 성공 시 오류 카운터 리셋
                self::$errorCounters['connection'] = 0;
                
                Log::info('SSE 연결 복구 성공', [
                    'connection_id' => $connectionId
                ]);
                
                return true;
            }

            // 복구 실패 시 폴백 처리
            $this->handleConnectionFallback($connectionId);
            
            return false;

        } catch (Throwable $e) {
            Log::error('연결 오류 처리 중 예외 발생', [
                'connection_id' => $connectionId,
                'original_error' => $exception->getMessage(),
                'recovery_error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 메시지 전송 오류를 처리합니다.
     *
     * @param array $message 전송 실패한 메시지
     * @param Exception $exception 발생한 예외
     * @return bool 복구 성공 여부
     */
    public function handleMessageSendError(array $message, Exception $exception): bool
    {
        try {
            Log::warning('SSE 메시지 전송 오류 발생', [
                'message_type' => $message['type'] ?? 'unknown',
                'error' => $exception->getMessage(),
                'error_type' => get_class($exception)
            ]);

            // 오류 카운터 증가
            self::$errorCounters['message']++;

            // 메시지를 임시 저장소에 보관
            $this->storeFailedMessage($message);

            // 재시도 메커니즘 적용
            $recovered = $this->retryWithBackoff(function() use ($message) {
                return $this->attemptMessageResend($message);
            }, 'message_resend');

            if ($recovered) {
                // 복구 성공 시 오류 카운터 리셋
                self::$errorCounters['message'] = 0;
                
                Log::info('SSE 메시지 재전송 성공', [
                    'message_type' => $message['type'] ?? 'unknown'
                ]);
                
                return true;
            }

            // 복구 실패 시 메시지를 영구 저장소에 보관
            $this->archiveFailedMessage($message);
            
            return false;

        } catch (Throwable $e) {
            Log::error('메시지 전송 오류 처리 중 예외 발생', [
                'message' => $message,
                'original_error' => $exception->getMessage(),
                'recovery_error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Redis 연결 오류를 처리합니다.
     *
     * @param Exception $exception 발생한 예외
     * @return bool 복구 성공 여부
     */
    public function handleRedisError(Exception $exception): bool
    {
        try {
            Log::error('Redis 연결 오류 발생', [
                'error' => $exception->getMessage(),
                'error_type' => get_class($exception)
            ]);

            // 오류 카운터 증가
            self::$errorCounters['redis']++;

            // 임계값 초과 시 메모리 기반 폴백 모드 활성화
            if (self::$errorCounters['redis'] >= self::ERROR_THRESHOLD) {
                $this->activateFallbackMode();
            }

            // Redis 연결 복구 시도
            $recovered = $this->retryWithBackoff(function() {
                return $this->attemptRedisRecovery();
            }, 'redis_recovery');

            if ($recovered) {
                // 복구 성공 시 폴백 모드 해제 및 데이터 동기화
                $this->deactivateFallbackMode();
                self::$errorCounters['redis'] = 0;
                
                Log::info('Redis 연결 복구 성공');
                
                return true;
            }

            return false;

        } catch (Throwable $e) {
            Log::error('Redis 오류 처리 중 예외 발생', [
                'original_error' => $exception->getMessage(),
                'recovery_error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 메모리 기반 폴백 모드를 활성화합니다.
     *
     * @return void
     */
    public function activateFallbackMode(): void
    {
        try {
            Log::warning('메모리 기반 폴백 모드 활성화');

            // 폴백 모드 플래그 설정
            Cache::put('sse_fallback_mode', true, now()->addHours(1));

            // 메모리 저장소 초기화
            if (!isset(self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY])) {
                self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY] = [];
            }
            
            if (!isset(self::$memoryStorage[self::FALLBACK_MESSAGES_KEY])) {
                self::$memoryStorage[self::FALLBACK_MESSAGES_KEY] = [];
            }
            
            if (!isset(self::$memoryStorage[self::FALLBACK_STATS_KEY])) {
                self::$memoryStorage[self::FALLBACK_STATS_KEY] = [];
            }

            Log::info('폴백 모드 활성화 완료');

        } catch (Throwable $e) {
            Log::error('폴백 모드 활성화 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 폴백 모드를 해제하고 데이터를 동기화합니다.
     *
     * @return void
     */
    public function deactivateFallbackMode(): void
    {
        try {
            if (!$this->isFallbackModeActive()) {
                return;
            }

            Log::info('폴백 모드 해제 및 데이터 동기화 시작');

            // 메모리 저장소의 데이터를 Redis로 동기화
            $this->syncFallbackDataToRedis();

            // 폴백 모드 플래그 해제
            Cache::forget('sse_fallback_mode');

            // 메모리 저장소 정리
            self::$memoryStorage = [];

            Log::info('폴백 모드 해제 완료');

        } catch (Throwable $e) {
            Log::error('폴백 모드 해제 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 폴백 모드 활성화 여부를 확인합니다.
     *
     * @return bool 폴백 모드 활성화 여부
     */
    public function isFallbackModeActive(): bool
    {
        return Cache::get('sse_fallback_mode', false);
    }

    /**
     * 메모리 기반 저장소에 연결 정보를 저장합니다.
     *
     * @param string $connectionId 연결 ID
     * @param array $data 연결 데이터
     * @return bool 저장 성공 여부
     */
    public function storeConnectionInMemory(string $connectionId, array $data): bool
    {
        try {
            if (!$this->isFallbackModeActive()) {
                return false;
            }

            self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY][$connectionId] = array_merge($data, [
                'stored_at' => Carbon::now()->toISOString()
            ]);

            Log::debug('연결 정보를 메모리 저장소에 저장', [
                'connection_id' => $connectionId
            ]);

            return true;

        } catch (Throwable $e) {
            Log::error('메모리 저장소 연결 정보 저장 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 메모리 기반 저장소에서 연결 정보를 조회합니다.
     *
     * @param string $connectionId 연결 ID
     * @return array|null 연결 데이터
     */
    public function getConnectionFromMemory(string $connectionId): ?array
    {
        try {
            if (!$this->isFallbackModeActive()) {
                return null;
            }

            return self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY][$connectionId] ?? null;

        } catch (Throwable $e) {
            Log::error('메모리 저장소 연결 정보 조회 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * 시스템 복구 상태를 확인합니다.
     *
     * @return array 복구 상태 정보
     */
    public function getRecoveryStatus(): array
    {
        try {
            return [
                'fallback_mode_active' => $this->isFallbackModeActive(),
                'error_counters' => self::$errorCounters,
                'memory_storage_size' => [
                    'connections' => count(self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY] ?? []),
                    'messages' => count(self::$memoryStorage[self::FALLBACK_MESSAGES_KEY] ?? []),
                    'stats' => count(self::$memoryStorage[self::FALLBACK_STATS_KEY] ?? [])
                ],
                'redis_status' => $this->checkRedisStatus(),
                'last_recovery_check' => Cache::get('last_recovery_check'),
                'generated_at' => Carbon::now()->toISOString()
            ];

        } catch (Throwable $e) {
            Log::error('복구 상태 조회 실패', [
                'error' => $e->getMessage()
            ]);

            return [
                'fallback_mode_active' => false,
                'error_counters' => [],
                'memory_storage_size' => [],
                'redis_status' => 'error',
                'error' => $e->getMessage(),
                'generated_at' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * 실패한 연결을 정리합니다.
     *
     * @param string $connectionId 연결 ID
     * @return void
     */
    private function cleanupFailedConnection(string $connectionId): void
    {
        try {
            // Redis에서 연결 정보 제거 시도
            try {
                Redis::del("sse:connections:{$connectionId}");
                Redis::srem('sse:active_connections', $connectionId);
            } catch (Exception $e) {
                Log::debug('Redis 연결 정리 실패 (예상된 동작)', [
                    'connection_id' => $connectionId,
                    'error' => $e->getMessage()
                ]);
            }

            // 메모리 저장소에서도 제거
            if (isset(self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY][$connectionId])) {
                unset(self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY][$connectionId]);
            }

        } catch (Throwable $e) {
            Log::error('실패한 연결 정리 중 오류', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 연결 복구를 시도합니다.
     *
     * @param string $connectionId 연결 ID
     * @return bool 복구 성공 여부
     */
    private function attemptConnectionRecovery(string $connectionId): bool
    {
        try {
            // 연결 상태 확인
            $connectionExists = false;
            
            try {
                $connectionData = Redis::get("sse:connections:{$connectionId}");
                $connectionExists = !empty($connectionData);
            } catch (Exception $e) {
                // Redis 오류 시 메모리 저장소 확인
                $connectionExists = isset(self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY][$connectionId]);
            }

            return $connectionExists;

        } catch (Throwable $e) {
            Log::error('연결 복구 시도 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 메시지 재전송을 시도합니다.
     *
     * @param array $message 메시지 데이터
     * @return bool 재전송 성공 여부
     */
    private function attemptMessageResend(array $message): bool
    {
        try {
            // 간단한 메시지 유효성 검사
            if (empty($message) || !isset($message['type'])) {
                return false;
            }

            // 실제 재전송 로직은 NotificationManager에 위임
            // 여기서는 메시지가 유효한지만 확인
            return true;

        } catch (Throwable $e) {
            Log::error('메시지 재전송 시도 실패', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Redis 연결 복구를 시도합니다.
     *
     * @return bool 복구 성공 여부
     */
    private function attemptRedisRecovery(): bool
    {
        try {
            // Redis 연결 테스트
            $response = Redis::ping();
            return $response === 'PONG';

        } catch (Exception $e) {
            Log::debug('Redis 복구 시도 실패', [
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 지수 백오프를 적용한 재시도 메커니즘입니다.
     *
     * @param callable $operation 실행할 작업
     * @param string $operationType 작업 타입
     * @return bool 작업 성공 여부
     */
    private function retryWithBackoff(callable $operation, string $operationType): bool
    {
        $attempt = 0;
        
        while ($attempt < self::MAX_RETRY_ATTEMPTS) {
            try {
                if ($operation()) {
                    return true;
                }
            } catch (Throwable $e) {
                Log::debug('재시도 작업 실패', [
                    'operation_type' => $operationType,
                    'attempt' => $attempt + 1,
                    'error' => $e->getMessage()
                ]);
            }

            $attempt++;
            
            if ($attempt < self::MAX_RETRY_ATTEMPTS) {
                // 지수 백오프 계산
                $delay = min(
                    self::BASE_RETRY_DELAY * pow(2, $attempt - 1),
                    self::MAX_RETRY_DELAY
                );
                
                Log::debug('재시도 대기', [
                    'operation_type' => $operationType,
                    'attempt' => $attempt,
                    'delay_seconds' => $delay
                ]);
                
                sleep($delay);
            }
        }

        Log::warning('재시도 한계 도달', [
            'operation_type' => $operationType,
            'max_attempts' => self::MAX_RETRY_ATTEMPTS
        ]);

        return false;
    }

    /**
     * 실패한 메시지를 임시 저장소에 보관합니다.
     *
     * @param array $message 메시지 데이터
     * @return void
     */
    private function storeFailedMessage(array $message): void
    {
        try {
            $messageId = uniqid('failed_msg_');
            $messageData = array_merge($message, [
                'failed_at' => Carbon::now()->toISOString(),
                'message_id' => $messageId
            ]);

            self::$memoryStorage[self::FALLBACK_MESSAGES_KEY][$messageId] = $messageData;

            Log::debug('실패한 메시지를 임시 저장소에 보관', [
                'message_id' => $messageId,
                'message_type' => $message['type'] ?? 'unknown'
            ]);

        } catch (Throwable $e) {
            Log::error('실패한 메시지 저장 실패', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 실패한 메시지를 영구 저장소에 보관합니다.
     *
     * @param array $message 메시지 데이터
     * @return void
     */
    private function archiveFailedMessage(array $message): void
    {
        try {
            // 파일 기반 아카이브 (간단한 구현)
            $archiveData = [
                'message' => $message,
                'archived_at' => Carbon::now()->toISOString(),
                'archive_id' => uniqid('archive_')
            ];

            $archiveFile = storage_path('logs/sse_failed_messages_' . date('Y-m-d') . '.log');
            file_put_contents($archiveFile, json_encode($archiveData) . PHP_EOL, FILE_APPEND | LOCK_EX);

            Log::info('실패한 메시지를 아카이브에 저장', [
                'message_type' => $message['type'] ?? 'unknown',
                'archive_file' => $archiveFile
            ]);

        } catch (Throwable $e) {
            Log::error('실패한 메시지 아카이브 실패', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 연결 폴백 처리를 수행합니다.
     *
     * @param string $connectionId 연결 ID
     * @return void
     */
    private function handleConnectionFallback(string $connectionId): void
    {
        try {
            Log::info('연결 폴백 처리 시작', [
                'connection_id' => $connectionId
            ]);

            // 연결을 비활성 상태로 표시
            if ($this->isFallbackModeActive()) {
                $connectionData = $this->getConnectionFromMemory($connectionId);
                if ($connectionData) {
                    $connectionData['status'] = 'failed';
                    $connectionData['failed_at'] = Carbon::now()->toISOString();
                    self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY][$connectionId] = $connectionData;
                }
            }

        } catch (Throwable $e) {
            Log::error('연결 폴백 처리 실패', [
                'connection_id' => $connectionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 폴백 데이터를 Redis로 동기화합니다.
     *
     * @return void
     */
    private function syncFallbackDataToRedis(): void
    {
        try {
            // 연결 데이터 동기화
            $connections = self::$memoryStorage[self::FALLBACK_CONNECTIONS_KEY] ?? [];
            foreach ($connections as $connectionId => $data) {
                if (isset($data['status']) && $data['status'] !== 'failed') {
                    try {
                        Redis::setex("sse:connections:{$connectionId}", 3600, json_encode($data));
                        Redis::sadd('sse:active_connections', $connectionId);
                    } catch (Exception $e) {
                        Log::warning('연결 데이터 동기화 실패', [
                            'connection_id' => $connectionId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }

            // 메시지 데이터 동기화 (재시도 대상)
            $messages = self::$memoryStorage[self::FALLBACK_MESSAGES_KEY] ?? [];
            foreach ($messages as $messageId => $messageData) {
                // 메시지 재전송 시도
                Log::info('폴백 메시지 재전송 시도', [
                    'message_id' => $messageId,
                    'message_type' => $messageData['type'] ?? 'unknown'
                ]);
            }

            Log::info('폴백 데이터 동기화 완료', [
                'synced_connections' => count($connections),
                'synced_messages' => count($messages)
            ]);

        } catch (Throwable $e) {
            Log::error('폴백 데이터 동기화 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Redis 상태를 확인합니다.
     *
     * @return string Redis 상태
     */
    private function checkRedisStatus(): string
    {
        try {
            $response = Redis::ping();
            return $response === 'PONG' ? 'connected' : 'disconnected';
        } catch (Exception $e) {
            return 'error';
        }
    }

    /**
     * 오류 카운터를 리셋합니다.
     *
     * @param string|null $type 리셋할 오류 타입 (null인 경우 전체 리셋)
     * @return void
     */
    public function resetErrorCounters(?string $type = null): void
    {
        if ($type && isset(self::$errorCounters[$type])) {
            self::$errorCounters[$type] = 0;
            Log::info('오류 카운터 리셋', ['type' => $type]);
        } elseif ($type === null) {
            self::$errorCounters = array_fill_keys(array_keys(self::$errorCounters), 0);
            Log::info('모든 오류 카운터 리셋');
        }
    }

    /**
     * 메모리 저장소를 정리합니다.
     *
     * @return void
     */
    public function clearMemoryStorage(): void
    {
        self::$memoryStorage = [];
        Log::info('메모리 저장소 정리 완료');
    }
}