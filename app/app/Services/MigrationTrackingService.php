<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 마이그레이션 추적 서비스
 * 
 * SSE 전환율 추적, 리포팅 및 마이그레이션 진행 상황을 모니터링합니다.
 */
class MigrationTrackingService
{
    private const TRACKING_CACHE_TTL = 86400; // 24시간
    private const REPORT_CACHE_TTL = 3600; // 1시간
    private const METRICS_RETENTION_DAYS = 30; // 30일간 메트릭 보관

    /**
     * SSE 전환율 기록
     */
    public function recordSSEConversion(int $userId, bool $successful = true): void
    {
        $date = now()->format('Y-m-d');
        $hour = now()->format('H');
        
        // 일별 전환 통계
        $dailyKey = "sse_conversion_daily:{$date}";
        $dailyStats = Cache::get($dailyKey, [
            'total_attempts' => 0,
            'successful_conversions' => 0,
            'failed_conversions' => 0,
            'unique_users' => []
        ]);
        
        $dailyStats['total_attempts']++;
        if ($successful) {
            $dailyStats['successful_conversions']++;
        } else {
            $dailyStats['failed_conversions']++;
        }
        
        // 고유 사용자 추적
        if (!in_array($userId, $dailyStats['unique_users'])) {
            $dailyStats['unique_users'][] = $userId;
        }
        
        Cache::put($dailyKey, $dailyStats, self::TRACKING_CACHE_TTL);
        
        // 시간별 전환 통계
        $hourlyKey = "sse_conversion_hourly:{$date}:{$hour}";
        $hourlyStats = Cache::get($hourlyKey, [
            'total_attempts' => 0,
            'successful_conversions' => 0,
            'failed_conversions' => 0
        ]);
        
        $hourlyStats['total_attempts']++;
        if ($successful) {
            $hourlyStats['successful_conversions']++;
        } else {
            $hourlyStats['failed_conversions']++;
        }
        
        Cache::put($hourlyKey, $hourlyStats, self::TRACKING_CACHE_TTL);
        
        // 사용자별 전환 기록
        $userKey = "user_sse_conversion:{$userId}";
        $userStats = Cache::get($userKey, [
            'first_attempt' => null,
            'last_attempt' => null,
            'total_attempts' => 0,
            'successful_attempts' => 0,
            'conversion_status' => 'pending'
        ]);
        
        if (!$userStats['first_attempt']) {
            $userStats['first_attempt'] = now()->toISOString();
        }
        $userStats['last_attempt'] = now()->toISOString();
        $userStats['total_attempts']++;
        
        if ($successful) {
            $userStats['successful_attempts']++;
            $userStats['conversion_status'] = 'converted';
        }
        
        Cache::put($userKey, $userStats, self::TRACKING_CACHE_TTL * 7); // 7일간 보관
        
        Log::info('SSE 전환율 기록', [
            'user_id' => $userId,
            'successful' => $successful,
            'date' => $date,
            'hour' => $hour
        ]);
    }

    /**
     * 마이그레이션 진행률 계산
     */
    public function calculateMigrationProgress(int $days = 7): array
    {
        $cacheKey = "migration_progress_report:{$days}";
        $cached = Cache::get($cacheKey);
        
        if ($cached) {
            return $cached;
        }
        
        $progress = [
            'period' => [
                'days' => $days,
                'start_date' => now()->subDays($days - 1)->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d')
            ],
            'conversion_stats' => [
                'total_attempts' => 0,
                'successful_conversions' => 0,
                'failed_conversions' => 0,
                'conversion_rate' => 0,
                'unique_users' => 0
            ],
            'daily_breakdown' => [],
            'user_adoption' => [
                'new_sse_users' => 0,
                'returning_users' => 0,
                'churned_users' => 0
            ],
            'performance_metrics' => [
                'avg_conversion_time' => 0,
                'success_rate_trend' => []
            ]
        ];
        
        $allUniqueUsers = [];
        
        // 일별 데이터 수집
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_conversion_daily:{$date}";
            $dailyData = Cache::get($dailyKey, [
                'total_attempts' => 0,
                'successful_conversions' => 0,
                'failed_conversions' => 0,
                'unique_users' => []
            ]);
            
            $progress['conversion_stats']['total_attempts'] += $dailyData['total_attempts'];
            $progress['conversion_stats']['successful_conversions'] += $dailyData['successful_conversions'];
            $progress['conversion_stats']['failed_conversions'] += $dailyData['failed_conversions'];
            
            // 고유 사용자 병합
            $allUniqueUsers = array_merge($allUniqueUsers, $dailyData['unique_users']);
            
            // 일별 성공률 계산
            $dailySuccessRate = $dailyData['total_attempts'] > 0 
                ? round(($dailyData['successful_conversions'] / $dailyData['total_attempts']) * 100, 2)
                : 0;
            
            $progress['daily_breakdown'][$date] = [
                'total_attempts' => $dailyData['total_attempts'],
                'successful_conversions' => $dailyData['successful_conversions'],
                'failed_conversions' => $dailyData['failed_conversions'],
                'success_rate' => $dailySuccessRate,
                'unique_users' => count($dailyData['unique_users'])
            ];
            
            $progress['performance_metrics']['success_rate_trend'][] = [
                'date' => $date,
                'success_rate' => $dailySuccessRate
            ];
        }
        
        // 전체 통계 계산
        $progress['conversion_stats']['unique_users'] = count(array_unique($allUniqueUsers));
        
        if ($progress['conversion_stats']['total_attempts'] > 0) {
            $progress['conversion_stats']['conversion_rate'] = round(
                ($progress['conversion_stats']['successful_conversions'] / $progress['conversion_stats']['total_attempts']) * 100, 
                2
            );
        }
        
        // 사용자 채택 분석
        $progress['user_adoption'] = $this->analyzeUserAdoption($days);
        
        Cache::put($cacheKey, $progress, self::REPORT_CACHE_TTL);
        
        return $progress;
    }

    /**
     * 사용자 채택 분석
     */
    private function analyzeUserAdoption(int $days): array
    {
        $adoption = [
            'new_sse_users' => 0,
            'returning_users' => 0,
            'churned_users' => 0,
            'active_users' => 0
        ];
        
        $currentPeriodUsers = [];
        $previousPeriodUsers = [];
        
        // 현재 기간 사용자
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_conversion_daily:{$date}";
            $dailyData = Cache::get($dailyKey, ['unique_users' => []]);
            $currentPeriodUsers = array_merge($currentPeriodUsers, $dailyData['unique_users']);
        }
        
        // 이전 기간 사용자
        for ($i = $days; $i < $days * 2; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_conversion_daily:{$date}";
            $dailyData = Cache::get($dailyKey, ['unique_users' => []]);
            $previousPeriodUsers = array_merge($previousPeriodUsers, $dailyData['unique_users']);
        }
        
        $currentPeriodUsers = array_unique($currentPeriodUsers);
        $previousPeriodUsers = array_unique($previousPeriodUsers);
        
        $adoption['active_users'] = count($currentPeriodUsers);
        $adoption['new_sse_users'] = count(array_diff($currentPeriodUsers, $previousPeriodUsers));
        $adoption['returning_users'] = count(array_intersect($currentPeriodUsers, $previousPeriodUsers));
        $adoption['churned_users'] = count(array_diff($previousPeriodUsers, $currentPeriodUsers));
        
        return $adoption;
    }

    /**
     * 마이그레이션 리포트 생성
     */
    public function generateMigrationReport(array $options = []): array
    {
        $days = $options['days'] ?? 7;
        $includeDetails = $options['include_details'] ?? true;
        $includeRecommendations = $options['include_recommendations'] ?? true;
        
        $report = [
            'generated_at' => now()->toISOString(),
            'report_period' => $days,
            'summary' => $this->calculateMigrationProgress($days),
            'system_health' => $this->getSystemHealthMetrics(),
            'performance_analysis' => $this->analyzePerformanceMetrics($days)
        ];
        
        if ($includeDetails) {
            $report['detailed_metrics'] = $this->getDetailedMetrics($days);
        }
        
        if ($includeRecommendations) {
            $report['recommendations'] = $this->generateRecommendations($report['summary']);
        }
        
        return $report;
    }

    /**
     * 시스템 건강 상태 메트릭
     */
    private function getSystemHealthMetrics(): array
    {
        return [
            'sse_service_status' => $this->checkSSEServiceHealth(),
            'pusher_service_status' => $this->checkPusherServiceHealth(),
            'redis_status' => $this->checkRedisHealth(),
            'error_rates' => $this->getErrorRates(),
            'response_times' => $this->getResponseTimeMetrics()
        ];
    }

    /**
     * SSE 서비스 건강 상태 확인
     */
    private function checkSSEServiceHealth(): array
    {
        $healthKey = 'sse_health_check';
        $lastCheck = Cache::get($healthKey, ['status' => 'unknown', 'last_check' => null]);
        
        // 5분마다 건강 상태 확인
        if (!$lastCheck['last_check'] || Carbon::parse($lastCheck['last_check'])->addMinutes(5)->isPast()) {
            try {
                // SSE 연결 테스트 (실제 구현에서는 더 정교한 테스트 필요)
                $activeConnections = Cache::get('sse:active_connections', 0);
                $status = $activeConnections >= 0 ? 'healthy' : 'unhealthy';
                
                $healthData = [
                    'status' => $status,
                    'last_check' => now()->toISOString(),
                    'active_connections' => $activeConnections,
                    'last_error' => null
                ];
                
                Cache::put($healthKey, $healthData, 300); // 5분 캐시
                return $healthData;
                
            } catch (\Exception $e) {
                $healthData = [
                    'status' => 'unhealthy',
                    'last_check' => now()->toISOString(),
                    'active_connections' => 0,
                    'last_error' => $e->getMessage()
                ];
                
                Cache::put($healthKey, $healthData, 300);
                return $healthData;
            }
        }
        
        return $lastCheck;
    }

    /**
     * Pusher 서비스 건강 상태 확인
     */
    private function checkPusherServiceHealth(): array
    {
        $healthKey = 'pusher_health_check';
        $lastCheck = Cache::get($healthKey, ['status' => 'unknown', 'last_check' => null]);
        
        if (!$lastCheck['last_check'] || Carbon::parse($lastCheck['last_check'])->addMinutes(5)->isPast()) {
            try {
                // Pusher 설정 확인
                $pusherEnabled = config('broadcasting.connections.pusher.key') !== null;
                $status = $pusherEnabled ? 'healthy' : 'disabled';
                
                $healthData = [
                    'status' => $status,
                    'last_check' => now()->toISOString(),
                    'enabled' => $pusherEnabled,
                    'last_error' => null
                ];
                
                Cache::put($healthKey, $healthData, 300);
                return $healthData;
                
            } catch (\Exception $e) {
                $healthData = [
                    'status' => 'unhealthy',
                    'last_check' => now()->toISOString(),
                    'enabled' => false,
                    'last_error' => $e->getMessage()
                ];
                
                Cache::put($healthKey, $healthData, 300);
                return $healthData;
            }
        }
        
        return $lastCheck;
    }

    /**
     * Redis 건강 상태 확인
     */
    private function checkRedisHealth(): array
    {
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return [
                'status' => 'healthy',
                'last_check' => now()->toISOString(),
                'response_time' => 0 // 실제로는 ping 응답 시간 측정
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'last_check' => now()->toISOString(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 오류율 조회
     */
    private function getErrorRates(): array
    {
        $hour = now()->format('Y-m-d-H');
        $errorKey = "error_rates:{$hour}";
        
        return Cache::get($errorKey, [
            'sse_errors' => 0,
            'pusher_errors' => 0,
            'sync_errors' => 0,
            'total_requests' => 0,
            'error_rate' => 0
        ]);
    }

    /**
     * 응답 시간 메트릭
     */
    private function getResponseTimeMetrics(): array
    {
        $hour = now()->format('Y-m-d-H');
        $responseKey = "response_times:{$hour}";
        
        return Cache::get($responseKey, [
            'avg_sse_response_time' => 0,
            'avg_pusher_response_time' => 0,
            'p95_response_time' => 0,
            'p99_response_time' => 0
        ]);
    }

    /**
     * 성능 분석
     */
    private function analyzePerformanceMetrics(int $days): array
    {
        $analysis = [
            'throughput_trend' => [],
            'latency_trend' => [],
            'error_trend' => [],
            'capacity_utilization' => []
        ];
        
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            
            // 처리량 추세
            $dailyKey = "sse_conversion_daily:{$date}";
            $dailyData = Cache::get($dailyKey, ['total_attempts' => 0]);
            
            $analysis['throughput_trend'][] = [
                'date' => $date,
                'total_requests' => $dailyData['total_attempts']
            ];
        }
        
        return $analysis;
    }

    /**
     * 상세 메트릭 조회
     */
    private function getDetailedMetrics(int $days): array
    {
        return [
            'hourly_breakdown' => $this->getHourlyBreakdown($days),
            'user_segments' => $this->getUserSegmentAnalysis(),
            'geographic_distribution' => $this->getGeographicDistribution(),
            'device_breakdown' => $this->getDeviceBreakdown()
        ];
    }

    /**
     * 시간별 분석
     */
    private function getHourlyBreakdown(int $days): array
    {
        $breakdown = [];
        
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dayData = [];
            
            for ($hour = 0; $hour < 24; $hour++) {
                $hourlyKey = "sse_conversion_hourly:{$date}:" . str_pad($hour, 2, '0', STR_PAD_LEFT);
                $hourlyData = Cache::get($hourlyKey, [
                    'total_attempts' => 0,
                    'successful_conversions' => 0,
                    'failed_conversions' => 0
                ]);
                
                $dayData[$hour] = $hourlyData;
            }
            
            $breakdown[$date] = $dayData;
        }
        
        return $breakdown;
    }

    /**
     * 사용자 세그먼트 분석
     */
    private function getUserSegmentAnalysis(): array
    {
        // 실제 구현에서는 사용자 데이터베이스와 연동
        return [
            'new_users' => ['count' => 0, 'conversion_rate' => 0],
            'existing_users' => ['count' => 0, 'conversion_rate' => 0],
            'power_users' => ['count' => 0, 'conversion_rate' => 0]
        ];
    }

    /**
     * 지리적 분포 분석
     */
    private function getGeographicDistribution(): array
    {
        // 실제 구현에서는 IP 기반 지역 분석
        return [
            'regions' => [],
            'countries' => [],
            'cities' => []
        ];
    }

    /**
     * 디바이스 분석
     */
    private function getDeviceBreakdown(): array
    {
        // 실제 구현에서는 User-Agent 분석
        return [
            'browsers' => [],
            'operating_systems' => [],
            'device_types' => []
        ];
    }

    /**
     * 권장사항 생성
     */
    private function generateRecommendations(array $summary): array
    {
        $recommendations = [];
        
        $conversionRate = $summary['conversion_stats']['conversion_rate'];
        
        if ($conversionRate < 50) {
            $recommendations[] = [
                'type' => 'warning',
                'title' => '낮은 SSE 전환율',
                'description' => "현재 SSE 전환율이 {$conversionRate}%로 낮습니다. 클라이언트 호환성을 확인하고 폴백 메커니즘을 강화하는 것을 권장합니다.",
                'priority' => 'high',
                'actions' => [
                    '브라우저 호환성 분석 수행',
                    'Pusher 폴백 활성화 검토',
                    '사용자 교육 및 안내 강화'
                ]
            ];
        } elseif ($conversionRate > 80) {
            $recommendations[] = [
                'type' => 'success',
                'title' => '높은 SSE 전환율',
                'description' => "SSE 전환율이 {$conversionRate}%로 매우 높습니다. Pusher 의존성 제거를 고려할 수 있습니다.",
                'priority' => 'medium',
                'actions' => [
                    'Pusher 사용량 모니터링',
                    '점진적 Pusher 비활성화 계획 수립',
                    'SSE 전용 모드 테스트'
                ]
            ];
        }
        
        // 오류율 기반 권장사항
        $systemHealth = $this->getSystemHealthMetrics();
        if (isset($systemHealth['error_rates']['error_rate']) && $systemHealth['error_rates']['error_rate'] > 5) {
            $recommendations[] = [
                'type' => 'error',
                'title' => '높은 오류율 감지',
                'description' => '시스템 오류율이 5%를 초과했습니다. 즉시 조치가 필요합니다.',
                'priority' => 'critical',
                'actions' => [
                    '로그 분석 및 오류 원인 파악',
                    '시스템 리소스 확인',
                    '긴급 롤백 계획 준비'
                ]
            ];
        }
        
        return $recommendations;
    }

    /**
     * 마이그레이션 메트릭 정리
     */
    public function cleanupOldMetrics(): int
    {
        $cleaned = 0;
        $cutoffDate = now()->subDays(self::METRICS_RETENTION_DAYS);
        
        try {
            // 오래된 일별 데이터 정리
            for ($i = self::METRICS_RETENTION_DAYS; $i < self::METRICS_RETENTION_DAYS + 7; $i++) {
                $date = now()->subDays($i)->format('Y-m-d');
                $keys = [
                    "sse_conversion_daily:{$date}",
                    "migration_progress_report:*:{$date}",
                    "sse_health_check:{$date}"
                ];
                
                foreach ($keys as $key) {
                    if (Cache::forget($key)) {
                        $cleaned++;
                    }
                }
            }
            
            Log::info('마이그레이션 메트릭 정리 완료', [
                'cleaned_count' => $cleaned,
                'cutoff_date' => $cutoffDate->format('Y-m-d')
            ]);
            
        } catch (\Exception $e) {
            Log::error('마이그레이션 메트릭 정리 중 오류', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return $cleaned;
    }
}