<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Artisan;
use Exception;

/**
 * Pusher 의존성 제거 서비스
 * 
 * Pusher에서 SSE로의 완전한 마이그레이션을 위한 체크리스트와 도구를 제공합니다.
 */
class PusherDependencyRemovalService
{
    private MigrationTrackingService $trackingService;
    private HybridNotificationService $hybridService;

    public function __construct(
        MigrationTrackingService $trackingService,
        HybridNotificationService $hybridService
    ) {
        $this->trackingService = $trackingService;
        $this->hybridService = $hybridService;
    }

    /**
     * Pusher 의존성 제거 체크리스트 생성
     */
    public function generateRemovalChecklist(): array
    {
        $checklist = [
            'pre_removal_checks' => $this->getPreRemovalChecks(),
            'dependency_analysis' => $this->analyzePusherDependencies(),
            'migration_readiness' => $this->assessMigrationReadiness(),
            'removal_steps' => $this->getRemovalSteps(),
            'post_removal_validation' => $this->getPostRemovalValidation(),
            'rollback_plan' => $this->getRollbackPlan()
        ];

        return $checklist;
    }

    /**
     * 제거 전 확인사항
     */
    private function getPreRemovalChecks(): array
    {
        $checks = [];
        
        // SSE 전환율 확인
        $migrationProgress = $this->hybridService->getMigrationProgress();
        $sseUsage = $migrationProgress['actual_sse_usage'];
        
        $checks['sse_conversion_rate'] = [
            'description' => 'SSE 전환율이 95% 이상인지 확인',
            'current_value' => $sseUsage,
            'target_value' => 95,
            'status' => $sseUsage >= 95 ? 'passed' : 'failed',
            'priority' => 'critical'
        ];
        
        // 활성 Pusher 연결 확인
        $activePusherConnections = $this->getActivePusherConnections();
        $checks['active_pusher_connections'] = [
            'description' => '활성 Pusher 연결이 5% 미만인지 확인',
            'current_value' => $activePusherConnections,
            'target_value' => 5,
            'status' => $activePusherConnections < 5 ? 'passed' : 'failed',
            'priority' => 'critical'
        ];
        
        // 오류율 확인
        $errorRate = $this->getSystemErrorRate();
        $checks['system_error_rate'] = [
            'description' => '시스템 오류율이 1% 미만인지 확인',
            'current_value' => $errorRate,
            'target_value' => 1,
            'status' => $errorRate < 1 ? 'passed' : 'failed',
            'priority' => 'high'
        ];
        
        // SSE 서비스 안정성 확인
        $sseStability = $this->checkSSEStability();
        $checks['sse_stability'] = [
            'description' => 'SSE 서비스가 7일간 안정적으로 운영되었는지 확인',
            'current_value' => $sseStability['stable_days'],
            'target_value' => 7,
            'status' => $sseStability['stable_days'] >= 7 ? 'passed' : 'failed',
            'priority' => 'high'
        ];
        
        return $checks;
    }

    /**
     * Pusher 의존성 분석
     */
    private function analyzePusherDependencies(): array
    {
        $dependencies = [
            'configuration_files' => $this->findPusherConfigFiles(),
            'code_references' => $this->findPusherCodeReferences(),
            'environment_variables' => $this->findPusherEnvironmentVariables(),
            'composer_packages' => $this->findPusherComposerPackages(),
            'frontend_dependencies' => $this->findPusherFrontendDependencies()
        ];
        
        return $dependencies;
    }

    /**
     * Pusher 설정 파일 찾기
     */
    private function findPusherConfigFiles(): array
    {
        $configFiles = [];
        
        // broadcasting.php 확인
        $broadcastingConfig = config('broadcasting.connections.pusher');
        if ($broadcastingConfig) {
            $configFiles[] = [
                'file' => 'config/broadcasting.php',
                'type' => 'configuration',
                'description' => 'Pusher 브로드캐스팅 설정',
                'action_required' => 'pusher 연결 설정 제거 또는 비활성화'
            ];
        }
        
        // .env 파일 확인
        $envVars = [
            'PUSHER_APP_ID',
            'PUSHER_APP_KEY',
            'PUSHER_APP_SECRET',
            'PUSHER_APP_CLUSTER'
        ];
        
        foreach ($envVars as $var) {
            if (env($var)) {
                $configFiles[] = [
                    'file' => '.env',
                    'type' => 'environment',
                    'variable' => $var,
                    'description' => "Pusher 환경 변수: {$var}",
                    'action_required' => '환경 변수 제거 또는 주석 처리'
                ];
            }
        }
        
        return $configFiles;
    }

    /**
     * Pusher 코드 참조 찾기
     */
    private function findPusherCodeReferences(): array
    {
        $references = [];
        
        // 실제 구현에서는 파일 시스템을 스캔하여 Pusher 관련 코드를 찾음
        $searchPatterns = [
            'use Pusher\\Pusher',
            'new Pusher(',
            'pusher->trigger',
            'broadcast(new',
            'BroadcastOn',
            'ShouldBroadcast'
        ];
        
        foreach ($searchPatterns as $pattern) {
            $references[] = [
                'pattern' => $pattern,
                'description' => "Pusher 관련 코드 패턴: {$pattern}",
                'estimated_occurrences' => 0, // 실제로는 grep 등으로 검색
                'action_required' => 'SSE 기반 코드로 교체 또는 제거'
            ];
        }
        
        return $references;
    }

    /**
     * Pusher 환경 변수 찾기
     */
    private function findPusherEnvironmentVariables(): array
    {
        $envVars = [];
        
        $pusherVars = [
            'PUSHER_APP_ID' => env('PUSHER_APP_ID'),
            'PUSHER_APP_KEY' => env('PUSHER_APP_KEY'),
            'PUSHER_APP_SECRET' => env('PUSHER_APP_SECRET'),
            'PUSHER_APP_CLUSTER' => env('PUSHER_APP_CLUSTER'),
            'BROADCAST_DRIVER' => env('BROADCAST_DRIVER')
        ];
        
        foreach ($pusherVars as $key => $value) {
            if ($value) {
                $envVars[] = [
                    'variable' => $key,
                    'value' => $key === 'PUSHER_APP_SECRET' ? '***' : $value,
                    'status' => 'active',
                    'action_required' => $key === 'BROADCAST_DRIVER' ? 'null로 변경' : '제거'
                ];
            }
        }
        
        return $envVars;
    }

    /**
     * Pusher Composer 패키지 찾기
     */
    private function findPusherComposerPackages(): array
    {
        $packages = [];
        
        try {
            $composerLock = json_decode(file_get_contents(base_path('composer.lock')), true);
            
            if ($composerLock && isset($composerLock['packages'])) {
                foreach ($composerLock['packages'] as $package) {
                    if (str_contains($package['name'], 'pusher')) {
                        $packages[] = [
                            'name' => $package['name'],
                            'version' => $package['version'],
                            'description' => $package['description'] ?? '',
                            'action_required' => 'composer remove 명령으로 제거'
                        ];
                    }
                }
            }
        } catch (Exception $e) {
            Log::warning('Composer 패키지 분석 실패', ['error' => $e->getMessage()]);
        }
        
        return $packages;
    }

    /**
     * 프론트엔드 Pusher 의존성 찾기
     */
    private function findPusherFrontendDependencies(): array
    {
        $dependencies = [];
        
        // package.json 확인
        try {
            $packageJson = json_decode(file_get_contents(base_path('package.json')), true);
            
            if ($packageJson) {
                $allDeps = array_merge(
                    $packageJson['dependencies'] ?? [],
                    $packageJson['devDependencies'] ?? []
                );
                
                foreach ($allDeps as $name => $version) {
                    if (str_contains($name, 'pusher')) {
                        $dependencies[] = [
                            'name' => $name,
                            'version' => $version,
                            'type' => 'npm_package',
                            'action_required' => 'npm uninstall 명령으로 제거'
                        ];
                    }
                }
            }
        } catch (Exception $e) {
            Log::warning('package.json 분석 실패', ['error' => $e->getMessage()]);
        }
        
        return $dependencies;
    }

    /**
     * 마이그레이션 준비 상태 평가
     */
    private function assessMigrationReadiness(): array
    {
        $readiness = [
            'overall_score' => 0,
            'readiness_level' => 'not_ready',
            'blocking_issues' => [],
            'warnings' => [],
            'recommendations' => []
        ];
        
        $checks = $this->getPreRemovalChecks();
        $totalChecks = count($checks);
        $passedChecks = 0;
        
        foreach ($checks as $checkName => $check) {
            if ($check['status'] === 'passed') {
                $passedChecks++;
            } else {
                if ($check['priority'] === 'critical') {
                    $readiness['blocking_issues'][] = [
                        'check' => $checkName,
                        'description' => $check['description'],
                        'current_value' => $check['current_value'],
                        'target_value' => $check['target_value']
                    ];
                } else {
                    $readiness['warnings'][] = [
                        'check' => $checkName,
                        'description' => $check['description'],
                        'priority' => $check['priority']
                    ];
                }
            }
        }
        
        $readiness['overall_score'] = round(($passedChecks / $totalChecks) * 100, 2);
        
        if ($readiness['overall_score'] >= 95 && empty($readiness['blocking_issues'])) {
            $readiness['readiness_level'] = 'ready';
        } elseif ($readiness['overall_score'] >= 80) {
            $readiness['readiness_level'] = 'almost_ready';
        } else {
            $readiness['readiness_level'] = 'not_ready';
        }
        
        // 권장사항 생성
        if (!empty($readiness['blocking_issues'])) {
            $readiness['recommendations'][] = '차단 문제들을 먼저 해결해야 합니다.';
        }
        
        if ($readiness['overall_score'] < 95) {
            $readiness['recommendations'][] = 'SSE 전환율을 95% 이상으로 높이세요.';
        }
        
        return $readiness;
    }

    /**
     * 제거 단계 정의
     */
    private function getRemovalSteps(): array
    {
        return [
            [
                'step' => 1,
                'title' => '하이브리드 모드 비활성화',
                'description' => 'SSE 전용 모드로 전환',
                'commands' => [
                    'php artisan config:set sse.migration.hybrid_mode false',
                    'php artisan config:set sse.migration.pusher_fallback_enabled false'
                ],
                'validation' => '24시간 동안 모니터링하여 오류율 확인',
                'rollback' => '하이브리드 모드 재활성화'
            ],
            [
                'step' => 2,
                'title' => 'Pusher 설정 제거',
                'description' => '환경 변수 및 설정 파일에서 Pusher 관련 설정 제거',
                'commands' => [
                    '# .env 파일에서 PUSHER_* 변수 제거',
                    '# config/broadcasting.php에서 pusher 연결 제거'
                ],
                'validation' => '설정 파일 검토 및 애플리케이션 재시작',
                'rollback' => 'Pusher 설정 복원'
            ],
            [
                'step' => 3,
                'title' => 'Pusher 코드 제거',
                'description' => 'Pusher 관련 코드 및 이벤트 리스너 제거',
                'commands' => [
                    '# Pusher 관련 클래스 및 메서드 제거',
                    '# 브로드캐스트 이벤트를 SSE 이벤트로 교체'
                ],
                'validation' => '코드 리뷰 및 테스트 실행',
                'rollback' => 'Git을 통한 코드 복원'
            ],
            [
                'step' => 4,
                'title' => 'Composer 패키지 제거',
                'description' => 'Pusher PHP SDK 및 관련 패키지 제거',
                'commands' => [
                    'composer remove pusher/pusher-php-server',
                    'composer remove laravel/echo'
                ],
                'validation' => 'composer.json 및 composer.lock 확인',
                'rollback' => 'composer install로 패키지 복원'
            ],
            [
                'step' => 5,
                'title' => '프론트엔드 의존성 제거',
                'description' => 'JavaScript Pusher 라이브러리 제거',
                'commands' => [
                    'npm uninstall pusher-js',
                    'npm uninstall laravel-echo'
                ],
                'validation' => 'package.json 확인 및 빌드 테스트',
                'rollback' => 'npm install로 패키지 복원'
            ],
            [
                'step' => 6,
                'title' => '최종 검증',
                'description' => '전체 시스템 기능 검증',
                'commands' => [
                    'php artisan test',
                    'npm run build',
                    'php artisan sse:health-check'
                ],
                'validation' => '모든 테스트 통과 및 기능 정상 작동 확인',
                'rollback' => '전체 시스템 롤백'
            ]
        ];
    }

    /**
     * 제거 후 검증 항목
     */
    private function getPostRemovalValidation(): array
    {
        return [
            'functional_tests' => [
                'description' => '모든 알림 기능이 SSE를 통해 정상 작동하는지 확인',
                'test_cases' => [
                    '전체 사용자 알림 전송',
                    '개별 사용자 알림 전송',
                    '실시간 데이터 업데이트',
                    '연결 끊김 및 재연결 처리'
                ]
            ],
            'performance_tests' => [
                'description' => 'SSE 전용 모드에서의 성능 확인',
                'metrics' => [
                    '응답 시간',
                    '처리량',
                    '메모리 사용량',
                    'CPU 사용량'
                ]
            ],
            'monitoring_setup' => [
                'description' => 'SSE 전용 모니터링 시스템 구성',
                'components' => [
                    '연결 상태 모니터링',
                    '메시지 전송 통계',
                    '오류 추적',
                    '알림 시스템'
                ]
            ],
            'documentation_update' => [
                'description' => '문서 업데이트',
                'items' => [
                    'API 문서',
                    '배포 가이드',
                    '운영 매뉴얼',
                    '트러블슈팅 가이드'
                ]
            ]
        ];
    }

    /**
     * 롤백 계획
     */
    private function getRollbackPlan(): array
    {
        return [
            'triggers' => [
                '오류율 5% 초과',
                '사용자 불만 급증',
                '시스템 불안정',
                '기능 장애 발생'
            ],
            'rollback_steps' => [
                [
                    'step' => 1,
                    'action' => 'Pusher 폴백 즉시 활성화',
                    'command' => 'php artisan config:set sse.migration.pusher_fallback_enabled true',
                    'estimated_time' => '5분'
                ],
                [
                    'step' => 2,
                    'action' => '하이브리드 모드 재활성화',
                    'command' => 'php artisan config:set sse.migration.hybrid_mode true',
                    'estimated_time' => '5분'
                ],
                [
                    'step' => 3,
                    'action' => 'Pusher 설정 복원',
                    'command' => 'Git을 통한 설정 파일 복원',
                    'estimated_time' => '10분'
                ],
                [
                    'step' => 4,
                    'action' => '패키지 복원',
                    'command' => 'composer install && npm install',
                    'estimated_time' => '15분'
                ]
            ],
            'validation_after_rollback' => [
                '모든 알림 기능 정상 작동 확인',
                '오류율 정상 수준 복귀 확인',
                '사용자 피드백 모니터링'
            ]
        ];
    }

    /**
     * 활성 Pusher 연결 수 조회
     */
    private function getActivePusherConnections(): int
    {
        // 실제 구현에서는 Pusher API를 통해 활성 연결 수를 조회
        return Cache::get('pusher_active_connections', 0);
    }

    /**
     * 시스템 오류율 조회
     */
    private function getSystemErrorRate(): float
    {
        $hour = now()->format('Y-m-d-H');
        $errorStats = Cache::get("error_rates:{$hour}", [
            'total_requests' => 0,
            'error_count' => 0
        ]);
        
        if ($errorStats['total_requests'] > 0) {
            return round(($errorStats['error_count'] / $errorStats['total_requests']) * 100, 2);
        }
        
        return 0;
    }

    /**
     * SSE 안정성 확인
     */
    private function checkSSEStability(): array
    {
        $stableDays = 0;
        $maxStableDays = 7;
        
        for ($i = 0; $i < $maxStableDays; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $healthKey = "sse_health_check:{$date}";
            $healthData = Cache::get($healthKey, ['status' => 'unknown']);
            
            if ($healthData['status'] === 'healthy') {
                $stableDays++;
            } else {
                break;
            }
        }
        
        return [
            'stable_days' => $stableDays,
            'target_days' => $maxStableDays,
            'is_stable' => $stableDays >= $maxStableDays
        ];
    }

    /**
     * 자동 제거 실행 (주의: 신중하게 사용)
     */
    public function executeAutomaticRemoval(array $options = []): array
    {
        $dryRun = $options['dry_run'] ?? true;
        $skipValidation = $options['skip_validation'] ?? false;
        
        $result = [
            'started_at' => now()->toISOString(),
            'dry_run' => $dryRun,
            'steps_executed' => [],
            'errors' => [],
            'warnings' => [],
            'completed' => false
        ];
        
        try {
            // 준비 상태 확인
            if (!$skipValidation) {
                $readiness = $this->assessMigrationReadiness();
                if ($readiness['readiness_level'] !== 'ready') {
                    throw new Exception('시스템이 Pusher 제거 준비가 되지 않았습니다.');
                }
            }
            
            $steps = $this->getRemovalSteps();
            
            foreach ($steps as $step) {
                $stepResult = [
                    'step' => $step['step'],
                    'title' => $step['title'],
                    'started_at' => now()->toISOString(),
                    'completed' => false,
                    'error' => null
                ];
                
                try {
                    if (!$dryRun) {
                        // 실제 명령 실행 (구현 필요)
                        $this->executeRemovalStep($step);
                    }
                    
                    $stepResult['completed'] = true;
                    $stepResult['completed_at'] = now()->toISOString();
                    
                } catch (Exception $e) {
                    $stepResult['error'] = $e->getMessage();
                    $result['errors'][] = $stepResult;
                    
                    // 오류 발생 시 중단
                    break;
                }
                
                $result['steps_executed'][] = $stepResult;
            }
            
            $result['completed'] = empty($result['errors']);
            $result['completed_at'] = now()->toISOString();
            
        } catch (Exception $e) {
            $result['errors'][] = [
                'type' => 'general_error',
                'message' => $e->getMessage(),
                'occurred_at' => now()->toISOString()
            ];
        }
        
        return $result;
    }

    /**
     * 개별 제거 단계 실행
     */
    private function executeRemovalStep(array $step): void
    {
        // 실제 구현에서는 각 단계별 명령을 실행
        Log::info('Pusher 제거 단계 실행', [
            'step' => $step['step'],
            'title' => $step['title']
        ]);
        
        // 예시: 설정 변경
        if ($step['step'] === 1) {
            Config::set('sse.migration.hybrid_mode', false);
            Config::set('sse.migration.pusher_fallback_enabled', false);
        }
    }
}