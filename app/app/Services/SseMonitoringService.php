<?php

namespace App\Services;

use App\Models\SseStatistics;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;
use Exception;

/**
 * SSE 모니터링 서비스
 * 
 * SSE 연결 통계 수집, 저장 및 실시간 모니터링 기능을 제공합니다.
 * 연결 상태, 메시지 전송 통계, 성능 메트릭을 관리합니다.
 */
class SseMonitoringService
{
    private ConnectionManager $connectionManager;
    
    /**
     * Redis 키 상수
     */
    private const REALTIME_STATS_KEY = 'sse:realtime_stats';
    private const HOURLY_STATS_KEY = 'sse:hourly_stats';
    private const CONNECTION_HISTORY_KEY = 'sse:connection_history';
    private const MESSAGE_STATS_KEY = 'sse:message_stats';
    
    /**
     * 통계 보관 기간 (초)
     */
    private const REALTIME_STATS_TTL = 3600; // 1시간
    private const HOURLY_STATS_TTL = 86400 * 7; // 7일
    private const CONNECTION_HISTORY_TTL = 86400 * 30; // 30일

    public function __construct(ConnectionManager $connectionManager)
    {
        $this->connectionManager = $connectionManager;
    }

    /**
     * 연결 통계를 수집하고 저장합니다.
     *
     * @param bool $isAuthenticated 인증된 연결 여부
     * @param float|null $connectionDuration 연결 지속 시간 (분)
     * @return void
     */
    public function recordConnection(bool $isAuthenticated = false, ?float $connectionDuration = null): void
    {
        try {
            // 오늘 통계 레코드 가져오기 또는 생성
            $todayStats = SseStatistics::today();
            
            // 연결 수 증가
            $todayStats->incrementConnections($isAuthenticated);
            
            // 연결 지속 시간 업데이트 (제공된 경우)
            if ($connectionDuration !== null) {
                $todayStats->updateAverageConnectionDuration($connectionDuration);
            }
            
            // 현재 동시 연결 수 확인 및 최대값 업데이트
            $currentConnections = count($this->connectionManager->getActiveConnections());
            $todayStats->updatePeakConnections($currentConnections);
            
            // 실시간 통계 업데이트
            $this->updateRealtimeStats($isAuthenticated, $currentConnections);
            
            Log::debug('연결 통계 기록 완료', [
                'is_authenticated' => $isAuthenticated,
                'current_connections' => $currentConnections,
                'connection_duration' => $connectionDuration
            ]);

        } catch (Exception $e) {
            Log::error('연결 통계 기록 실패', [
                'is_authenticated' => $isAuthenticated,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 메시지 전송 통계를 기록합니다.
     *
     * @param int $sentCount 전송 성공 메시지 수
     * @param int $failedCount 전송 실패 메시지 수
     * @return void
     */
    public function recordMessageStats(int $sentCount = 0, int $failedCount = 0): void
    {
        try {
            if ($sentCount === 0 && $failedCount === 0) {
                return;
            }

            // 오늘 통계 레코드 가져오기 또는 생성
            $todayStats = SseStatistics::today();
            
            // 메시지 통계 업데이트
            if ($sentCount > 0) {
                $todayStats->incrementMessagesSent($sentCount);
            }
            
            if ($failedCount > 0) {
                $todayStats->incrementFailedMessages($failedCount);
            }
            
            // 실시간 메시지 통계 업데이트
            $this->updateRealtimeMessageStats($sentCount, $failedCount);
            
            Log::debug('메시지 통계 기록 완료', [
                'sent_count' => $sentCount,
                'failed_count' => $failedCount
            ]);

        } catch (Exception $e) {
            Log::error('메시지 통계 기록 실패', [
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 실시간 모니터링 대시보드 데이터를 반환합니다.
     *
     * @return array 실시간 모니터링 데이터
     */
    public function getRealtimeMonitoringData(): array
    {
        try {
            // 현재 연결 통계
            $connectionStats = $this->connectionManager->getConnectionStats();
            
            // 실시간 통계 조회
            $realtimeStats = $this->getRealtimeStats();
            
            // 오늘 누적 통계
            $todayStats = SseStatistics::today();
            
            // 최근 메시지 통계
            $messageStats = $this->getRealtimeMessageStats();
            
            // 연결 히스토리 (최근 1시간)
            $connectionHistory = $this->getConnectionHistory(60);

            return [
                'current_connections' => $connectionStats,
                'realtime_stats' => $realtimeStats,
                'today_cumulative' => [
                    'total_connections' => $todayStats->total_connections,
                    'authenticated_connections' => $todayStats->authenticated_connections,
                    'guest_connections' => $todayStats->guest_connections,
                    'messages_sent' => $todayStats->messages_sent,
                    'failed_messages' => $todayStats->failed_messages,
                    'peak_concurrent_connections' => $todayStats->peak_concurrent_connections,
                    'average_connection_duration' => $todayStats->average_connection_duration,
                    'success_rate' => $this->calculateSuccessRate($todayStats->messages_sent, $todayStats->failed_messages)
                ],
                'message_stats' => $messageStats,
                'connection_history' => $connectionHistory,
                'system_health' => $this->getSystemHealthStatus(),
                'generated_at' => Carbon::now()->toISOString()
            ];

        } catch (Exception $e) {
            Log::error('실시간 모니터링 데이터 조회 실패', [
                'error' => $e->getMessage()
            ]);

            return [
                'current_connections' => [],
                'realtime_stats' => [],
                'today_cumulative' => [],
                'message_stats' => [],
                'connection_history' => [],
                'system_health' => ['status' => 'error', 'message' => $e->getMessage()],
                'error' => $e->getMessage(),
                'generated_at' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * 기간별 통계 데이터를 반환합니다.
     *
     * @param string $period 기간 ('daily', 'weekly', 'monthly')
     * @param int $limit 조회할 레코드 수
     * @return array 기간별 통계 데이터
     */
    public function getHistoricalStats(string $period = 'daily', int $limit = 30): array
    {
        try {
            $query = SseStatistics::query();
            
            switch ($period) {
                case 'weekly':
                    // 주별 통계 (일요일 기준)
                    $stats = $query->selectRaw('
                        YEARWEEK(date, 1) as period,
                        MIN(date) as period_start,
                        MAX(date) as period_end,
                        SUM(total_connections) as total_connections,
                        SUM(authenticated_connections) as authenticated_connections,
                        SUM(guest_connections) as guest_connections,
                        SUM(messages_sent) as messages_sent,
                        SUM(failed_messages) as failed_messages,
                        MAX(peak_concurrent_connections) as peak_concurrent_connections,
                        AVG(average_connection_duration) as average_connection_duration
                    ')
                    ->groupByRaw('YEARWEEK(date, 1)')
                    ->orderByDesc('period')
                    ->limit($limit)
                    ->get();
                    break;
                    
                case 'monthly':
                    // 월별 통계
                    $stats = $query->selectRaw('
                        DATE_FORMAT(date, "%Y-%m") as period,
                        MIN(date) as period_start,
                        MAX(date) as period_end,
                        SUM(total_connections) as total_connections,
                        SUM(authenticated_connections) as authenticated_connections,
                        SUM(guest_connections) as guest_connections,
                        SUM(messages_sent) as messages_sent,
                        SUM(failed_messages) as failed_messages,
                        MAX(peak_concurrent_connections) as peak_concurrent_connections,
                        AVG(average_connection_duration) as average_connection_duration
                    ')
                    ->groupByRaw('DATE_FORMAT(date, "%Y-%m")')
                    ->orderByDesc('period')
                    ->limit($limit)
                    ->get();
                    break;
                    
                default: // daily
                    // 일별 통계
                    $stats = $query->orderByDesc('date')
                        ->limit($limit)
                        ->get();
                    break;
            }

            // 성공률 계산 추가
            $processedStats = $stats->map(function ($stat) {
                $statArray = $stat->toArray();
                $statArray['success_rate'] = $this->calculateSuccessRate(
                    $stat->messages_sent, 
                    $stat->failed_messages
                );
                return $statArray;
            });

            return [
                'period' => $period,
                'limit' => $limit,
                'data' => $processedStats,
                'summary' => $this->calculateStatsSummary($processedStats),
                'generated_at' => Carbon::now()->toISOString()
            ];

        } catch (Exception $e) {
            Log::error('기간별 통계 조회 실패', [
                'period' => $period,
                'limit' => $limit,
                'error' => $e->getMessage()
            ]);

            return [
                'period' => $period,
                'limit' => $limit,
                'data' => [],
                'summary' => [],
                'error' => $e->getMessage(),
                'generated_at' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * 시스템 알림 및 경고를 확인합니다.
     *
     * @return array 시스템 알림 목록
     */
    public function getSystemAlerts(): array
    {
        try {
            $alerts = [];
            
            // 현재 연결 수 확인
            $currentConnections = count($this->connectionManager->getActiveConnections());
            if ($currentConnections > 1000) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => '높은 연결 수',
                    'message' => "현재 연결 수가 {$currentConnections}개로 높습니다.",
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }
            
            // 오늘 실패율 확인
            $todayStats = SseStatistics::today();
            $failureRate = $this->calculateFailureRate($todayStats->messages_sent, $todayStats->failed_messages);
            if ($failureRate > 5.0) {
                $alerts[] = [
                    'type' => 'error',
                    'title' => '높은 메시지 실패율',
                    'message' => "오늘 메시지 실패율이 {$failureRate}%입니다.",
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }
            
            // Redis 연결 상태 확인
            try {
                Redis::ping();
            } catch (Exception $e) {
                $alerts[] = [
                    'type' => 'critical',
                    'title' => 'Redis 연결 오류',
                    'message' => 'Redis 서버에 연결할 수 없습니다.',
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }
            
            // 최근 24시간 연결 수 급증 확인
            $yesterdayStats = SseStatistics::where('date', Carbon::yesterday()->toDateString())->first();
            if ($yesterdayStats && $todayStats->total_connections > $yesterdayStats->total_connections * 2) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => '연결 수 급증',
                    'message' => '어제 대비 연결 수가 2배 이상 증가했습니다.',
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }

            return [
                'alerts' => $alerts,
                'alert_count' => count($alerts),
                'generated_at' => Carbon::now()->toISOString()
            ];

        } catch (Exception $e) {
            Log::error('시스템 알림 조회 실패', [
                'error' => $e->getMessage()
            ]);

            return [
                'alerts' => [],
                'alert_count' => 0,
                'error' => $e->getMessage(),
                'generated_at' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * 실시간 통계를 업데이트합니다.
     *
     * @param bool $isAuthenticated 인증된 연결 여부
     * @param int $currentConnections 현재 연결 수
     * @return void
     */
    private function updateRealtimeStats(bool $isAuthenticated, int $currentConnections): void
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d H:i:s');
            $statsData = [
                'timestamp' => $timestamp,
                'current_connections' => $currentConnections,
                'is_authenticated' => $isAuthenticated
            ];

            // 실시간 통계에 추가
            Redis::lpush(self::REALTIME_STATS_KEY, json_encode($statsData));
            Redis::ltrim(self::REALTIME_STATS_KEY, 0, 59); // 최근 60개 유지
            Redis::expire(self::REALTIME_STATS_KEY, self::REALTIME_STATS_TTL);

            // 연결 히스토리에 추가
            $historyData = [
                'timestamp' => $timestamp,
                'connections' => $currentConnections,
                'authenticated' => $isAuthenticated ? 1 : 0
            ];
            
            Redis::lpush(self::CONNECTION_HISTORY_KEY, json_encode($historyData));
            Redis::ltrim(self::CONNECTION_HISTORY_KEY, 0, 1439); // 최근 24시간 (분 단위)
            Redis::expire(self::CONNECTION_HISTORY_KEY, self::CONNECTION_HISTORY_TTL);

        } catch (Exception $e) {
            Log::error('실시간 통계 업데이트 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 실시간 메시지 통계를 업데이트합니다.
     *
     * @param int $sentCount 전송 성공 수
     * @param int $failedCount 전송 실패 수
     * @return void
     */
    private function updateRealtimeMessageStats(int $sentCount, int $failedCount): void
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d H:i:s');
            $messageData = [
                'timestamp' => $timestamp,
                'sent' => $sentCount,
                'failed' => $failedCount
            ];

            Redis::lpush(self::MESSAGE_STATS_KEY, json_encode($messageData));
            Redis::ltrim(self::MESSAGE_STATS_KEY, 0, 99); // 최근 100개 유지
            Redis::expire(self::MESSAGE_STATS_KEY, self::REALTIME_STATS_TTL);

        } catch (Exception $e) {
            Log::error('실시간 메시지 통계 업데이트 실패', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 실시간 통계를 조회합니다.
     *
     * @return array 실시간 통계 데이터
     */
    private function getRealtimeStats(): array
    {
        try {
            $stats = Redis::lrange(self::REALTIME_STATS_KEY, 0, 59);
            return array_map(function($item) {
                return json_decode($item, true);
            }, $stats ?: []);

        } catch (Exception $e) {
            Log::error('실시간 통계 조회 실패', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 실시간 메시지 통계를 조회합니다.
     *
     * @return array 실시간 메시지 통계 데이터
     */
    private function getRealtimeMessageStats(): array
    {
        try {
            $stats = Redis::lrange(self::MESSAGE_STATS_KEY, 0, 99);
            return array_map(function($item) {
                return json_decode($item, true);
            }, $stats ?: []);

        } catch (Exception $e) {
            Log::error('실시간 메시지 통계 조회 실패', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 연결 히스토리를 조회합니다.
     *
     * @param int $minutes 조회할 분 수
     * @return array 연결 히스토리 데이터
     */
    private function getConnectionHistory(int $minutes = 60): array
    {
        try {
            $limit = min($minutes, 1440); // 최대 24시간
            $history = Redis::lrange(self::CONNECTION_HISTORY_KEY, 0, $limit - 1);
            
            return array_map(function($item) {
                return json_decode($item, true);
            }, $history ?: []);

        } catch (Exception $e) {
            Log::error('연결 히스토리 조회 실패', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 시스템 건강 상태를 확인합니다.
     *
     * @return array 시스템 건강 상태
     */
    private function getSystemHealthStatus(): array
    {
        $health = [
            'status' => 'healthy',
            'checks' => []
        ];

        try {
            // Redis 연결 확인
            Redis::ping();
            $health['checks']['redis'] = ['status' => 'ok', 'message' => 'Redis 연결 정상'];
        } catch (Exception $e) {
            $health['status'] = 'unhealthy';
            $health['checks']['redis'] = ['status' => 'error', 'message' => 'Redis 연결 실패'];
        }

        // 데이터베이스 연결 확인
        try {
            SseStatistics::count();
            $health['checks']['database'] = ['status' => 'ok', 'message' => '데이터베이스 연결 정상'];
        } catch (Exception $e) {
            $health['status'] = 'unhealthy';
            $health['checks']['database'] = ['status' => 'error', 'message' => '데이터베이스 연결 실패'];
        }

        // 현재 연결 수 확인
        $currentConnections = count($this->connectionManager->getActiveConnections());
        if ($currentConnections > 1000) {
            $health['status'] = 'warning';
            $health['checks']['connections'] = [
                'status' => 'warning', 
                'message' => "높은 연결 수: {$currentConnections}"
            ];
        } else {
            $health['checks']['connections'] = [
                'status' => 'ok', 
                'message' => "연결 수 정상: {$currentConnections}"
            ];
        }

        return $health;
    }

    /**
     * 성공률을 계산합니다.
     *
     * @param int $sent 전송 성공 수
     * @param int $failed 전송 실패 수
     * @return float 성공률 (%)
     */
    private function calculateSuccessRate(int $sent, int $failed): float
    {
        $total = $sent + $failed;
        return $total > 0 ? round(($sent / $total) * 100, 2) : 100.0;
    }

    /**
     * 실패율을 계산합니다.
     *
     * @param int $sent 전송 성공 수
     * @param int $failed 전송 실패 수
     * @return float 실패율 (%)
     */
    private function calculateFailureRate(int $sent, int $failed): float
    {
        $total = $sent + $failed;
        return $total > 0 ? round(($failed / $total) * 100, 2) : 0.0;
    }

    /**
     * 통계 요약을 계산합니다.
     *
     * @param \Illuminate\Support\Collection $stats 통계 데이터
     * @return array 통계 요약
     */
    private function calculateStatsSummary($stats): array
    {
        if ($stats->isEmpty()) {
            return [];
        }

        return [
            'total_connections' => $stats->sum('total_connections'),
            'total_messages_sent' => $stats->sum('messages_sent'),
            'total_failed_messages' => $stats->sum('failed_messages'),
            'average_success_rate' => $stats->avg('success_rate'),
            'peak_connections' => $stats->max('peak_concurrent_connections'),
            'average_connection_duration' => $stats->avg('average_connection_duration'),
            'period_count' => $stats->count()
        ];
    }

    /**
     * 통계 데이터를 정리합니다.
     *
     * @param int $daysOld 삭제할 데이터의 최소 일수
     * @return int 삭제된 레코드 수
     */
    public function cleanupOldStats(int $daysOld = 90): int
    {
        try {
            $cutoffDate = Carbon::now()->subDays($daysOld)->toDateString();
            
            $deletedCount = SseStatistics::where('date', '<', $cutoffDate)->delete();

            if ($deletedCount > 0) {
                Log::info('오래된 SSE 통계 데이터 정리 완료', [
                    'deleted_count' => $deletedCount,
                    'cutoff_date' => $cutoffDate
                ]);
            }

            return $deletedCount;

        } catch (Exception $e) {
            Log::error('SSE 통계 데이터 정리 실패', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
}