<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RepairProcess extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'code'];

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'repair_process_id', 'id');
    }

    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'repair_process_id', 'id');
    }

    public function repairProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'repair_process_id', 'id');
    }

    public function repairGrades(): BelongsToMany
    {
        return $this->belongsToMany(RepairGrade::class, 'repair_grade_repair_process');
    }

    public function repairSymptoms(): BelongsToMany
    {
        return $this->belongsToMany(RepairSymptom::class, 'repair_process_repair_symptom');
    }

    /**
     * 수리 프로세스 매핑과의 관계 (1:1)
     */
    public function costTypeProcessMapping(): HasOne
    {
        return $this->hasOne(RepairCostTypeProcessMapping::class);
    }

    /**
     * 수리 유형과의 관계 (매핑을 통해)
     */
    public function repairCostType()
    {
        return $this->hasOneThrough(
            RepairCostType::class,
            RepairCostTypeProcessMapping::class,
            'repair_process_id', // RepairProcessMapping의 외래 키
            'id', // RepairCostType의 로컬 키
            'id', // RepairProcess의 로컬 키
            'repair_cost_type_id' // RepairProcessMapping의 외래 키
        );
    }
}
