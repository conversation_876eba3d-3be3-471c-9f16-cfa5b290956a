<?php

namespace App\Models;

use App\Models\Casts\CalculationDetailsCast;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class RepairProduct extends Model
{
    use SoftDeletes;

    const STATUS_WAITING = 10;
    const STATUS_REPAIRED = 30;
    const STATUS_DELETED = 90;

    public static array $STATUS_NAME = [
        self::STATUS_WAITING => "구성품 신청",
        self::STATUS_REPAIRED => "수리/점검 완료",
        self::STATUS_DELETED => "삭제",
    ];

    protected $fillable = [
        'product_id',
        'status',
        'waiting_user_id',
        'waiting_at',
        'completed_user_id',
        'completed_at',
        'amount',
        'repair_symptom_id',
        'repair_process_id',
        'repair_grade_id',
        'invoice1',
        'invoice2',
        'invoice3',
        'is_os_install',
        'repair_cost_id',
        'calculation_basis',
        'calculation_details',
        'memo',
    ];

    protected $with = [
        'repairSymptom:id,name,code',
        'repairProcess:id,name,code',
        'repairGrade:id,name,code',
        'waitingUser:id,name,username',
        'completedUser:id,name,username',
        'repairProductParts',
    ];

    /**
     * 캐스팅 설정
     */
    protected $casts = [
        'status' => 'integer',
        'amount' => 'integer',
        'invoice1' => 'integer',
        'invoice2' => 'integer',
        'invoice3' => 'integer',
        'waiting_at' => 'datetime',
        'completed_at' => 'datetime',
        'calculation_details' => CalculationDetailsCast::class,
    ];

    /**
     * 상품과의 관계
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * 수리 증상과의 관계
     */
    public function repairSymptom(): BelongsTo
    {
        return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id', 'id');
    }

    /**
     * 수리 처리와의 관계
     */
    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'repair_process_id', 'id');
    }

    /**
     * 수리 등급과의 관계
     */
    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id', 'id');
    }

    /**
     * 팔레트 제품들과의 관계
     */
    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'repair_product_id', 'id');
    }

    /**
     * 대기 사용자와의 관계
     */
    public function waitingUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'waiting_user_id', 'id');
    }

    /**
     * 완료 사용자와의 관계
     */
    public function completedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'completed_user_id', 'id');
    }

    /**
     * 수리 제품 구성품들과의 관계
     */
    public function repairProductParts(): HasMany
    {
        return $this->hasMany(RepairProductParts::class, 'repair_product_id', 'id');
    }

    /**
     * 수리 구성품들과의 관계
     */
    public function repairParts(): BelongsToMany
    {
        return $this->belongsToMany(RepairParts::class, 'repair_product_parts',
            'repair_product_id', 'repair_parts_id')
            ->withPivot('quantity')
            ->withTimestamps();
    }

    // ========================================
    // 새로운 수리비 시스템 관계 (repair_cost_id 기반)
    // ========================================

    /**
     * 수리비와의 관계 (repair_cost_id로 직접 연결)
     */
    public function repairCost(): BelongsTo
    {
        return $this->belongsTo(RepairCost::class, 'repair_cost_id');
    }

    /**
     * 수리비 범위와의 관계 (repair_cost를 통해)
     */
    public function repairCostRange()
    {
        return $this->repairCost?->repairCostRange;
    }

    /**
     * 수리비 카테고리와의 관계 (repair_cost를 통해)
     */
    public function repairCostCategory()
    {
        return $this->repairCost?->repairCostRange?->repairCostCategory;
    }

    /**
     * 수리비 정책과의 관계 (repair_cost를 통해)
     */
    public function repairCostPolicy()
    {
        return $this->repairCost?->repairCostRange?->repairCostCategory?->repairCostPolicy;
    }

    /**
     * 수리 유형과의 관계 (repair_cost를 통해)
     */
    public function repairCostType()
    {
        return $this->repairCost?->repairCostType;
    }

    /**
     * 수리비 관련 모든 정보를 한 번에 조회하는 메서드
     *
     * @return array 수리비 관련 모든 정보
     */
    public function getRepairCostInfo(): array
    {
        if (!$this->repair_cost_id || !$this->repairCost) {
            return [
                'cost' => null,
                'range' => null,
                'category' => null,
                'policy' => null,
                'type' => null,
            ];
        }

        return [
            'cost' => $this->repairCost,
            'range' => $this->repairCostRange,
            'category' => $this->repairCostCategory,
            'policy' => $this->repairCostPolicy,
            'type' => $this->repairCostType,
            'amount' => $this->repairCost->amount,
            'type_name' => $this->repairCostType?->name,
            'type_code' => $this->repairCostType?->code,
            'range_name' => $this->repairCostRange?->range_name,
            'policy_name' => $this->repairCostPolicy?->name,
            'policy_code' => $this->repairCostPolicy?->code,
        ];
    }

    /**
     * 수리비 금액 조회
     *
     * @return int|null 수리비 금액
     */
    public function getRepairCostAmount(): ?int
    {
        return $this->repairCost?->amount;
    }

    /**
     * 수리비 금액 포맷팅
     *
     * @return string 포맷팅된 수리비
     */
    public function getFormattedRepairCostAttribute(): string
    {
        $amount = $this->getRepairCostAmount();
        return $amount ? number_format($amount) . '원' : '0원';
    }

    /**
     * 수리 유형 이름 조회
     *
     * @return string|null 수리 유형 이름
     */
    public function getRepairTypeNameAttribute(): ?string
    {
        return $this->repairCostType?->name;
    }

    /**
     * 수리 유형 코드 조회
     *
     * @return string|null 수리 유형 코드
     */
    public function getRepairTypeCodeAttribute(): ?string
    {
        return $this->repairCostType?->code;
    }

    /**
     * 범위 이름 조회
     *
     * @return string|null 범위 이름
     */
    public function getRangeNameAttribute(): ?string
    {
        return $this->repairCostRange?->range_name;
    }

    /**
     * 정책 이름 조회
     *
     * @return string|null 정책 이름
     */
    public function getPolicyNameAttribute(): ?string
    {
        return $this->repairCostPolicy?->name;
    }

    /**
     * 정책 코드 조회
     *
     * @return string|null 정책 코드
     */
    public function getPolicyCodeAttribute(): ?string
    {
        return $this->repairCostPolicy?->code;
    }

    // ========================================
    // 기존 메서드들 (하위 호환성 유지)
    // ========================================

    /**
     * 상태 이름 가져오기
     */
    public function getStatusNameAttribute(): string
    {
        return self::$STATUS_NAME[$this->status] ?? '알 수 없음';
    }

    /**
     * 판매가 포맷팅
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount) . '원';
    }

    /**
     * 총 청구금액 계산
     */
    public function getTotalInvoiceAttribute(): int
    {
        return $this->invoice1 + $this->invoice2 + $this->invoice3;
    }

    /**
     * 총 청구금액 포맷팅
     */
    public function getFormattedTotalInvoiceAttribute(): string
    {
        return number_format($this->total_invoice) . '원';
    }

    /**
     * 대기 상태인지 확인
     */
    public function getIsWaitingAttribute(): bool
    {
        return $this->status === self::STATUS_WAITING;
    }

    /**
     * 완료 상태인지 확인
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === self::STATUS_REPAIRED;
    }

    /**
     * 삭제 상태인지 확인
     */
    public function getIsDeletedAttribute(): bool
    {
        return $this->status === self::STATUS_DELETED;
    }

    /**
     * 구성품 총 비용 계산
     */
    public function getPartsTotalCostAttribute(): int
    {
        return $this->repairProductParts->sum(function ($part) {
            return $part->price * $part->quantity;
        });
    }

    /**
     * 구성품 총 비용 포맷팅
     */
    public function getFormattedPartsTotalCostAttribute(): string
    {
        return number_format($this->parts_total_cost) . '원';
    }
}
