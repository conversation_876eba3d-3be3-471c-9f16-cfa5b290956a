<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MonitorRule extends Model
{
    use HasFactory;
    /**
     * 규칙 유형 상수
     */
    const RULE_TYPE_BRAND = 'brand';
    const RULE_TYPE_EXCLUDE = 'exclude';
    const RULE_TYPE_SIZE_PATTERN = 'size_pattern';

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'rule_type',
        'pattern',
        'description',
        'priority',
        'is_active'
    ];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'priority' => 'integer',
        'is_active' => 'boolean'
    ];

    /**
     * 활성화된 규칙만 조회하는 스코프
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * 규칙 유형별 조회 스코프
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('rule_type', $type);
    }

    /**
     * 우선순위 순으로 정렬하는 스코프
     */
    public function scopeOrderByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 브랜드 규칙 조회
     */
    public static function getBrandRules(): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->byType(static::RULE_TYPE_BRAND)
            ->orderByPriority()
            ->get();
    }

    /**
     * 제외 규칙 조회
     */
    public static function getExcludeRules(): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->byType(static::RULE_TYPE_EXCLUDE)
            ->orderByPriority()
            ->get();
    }

    /**
     * 크기 패턴 규칙 조회
     */
    public static function getSizePatternRules(): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->byType(static::RULE_TYPE_SIZE_PATTERN)
            ->orderByPriority()
            ->get();
    }

    /**
     * 규칙 유형 목록 반환
     */
    public static function getRuleTypes(): array
    {
        return [
            static::RULE_TYPE_BRAND => '브랜드 키워드',
            static::RULE_TYPE_EXCLUDE => '제외 키워드',
            static::RULE_TYPE_SIZE_PATTERN => '크기 추출 패턴'
        ];
    }

    /**
     * 규칙 유형 한글명 반환
     */
    public function getRuleTypeNameAttribute(): string
    {
        $types = static::getRuleTypes();
        return $types[$this->rule_type] ?? $this->rule_type;
    }

    /**
     * 패턴 검증 메서드
     */
    public function validatePattern(string $testString): bool
    {
        try {
            switch ($this->rule_type) {
                case static::RULE_TYPE_BRAND:
                case static::RULE_TYPE_EXCLUDE:
                    // 키워드 매칭 (대소문자 구분 없음)
                    return stripos($testString, $this->pattern) !== false;
                
                case static::RULE_TYPE_SIZE_PATTERN:
                    // 정규식 패턴 매칭
                    return preg_match($this->pattern, $testString) === 1;
                
                default:
                    return false;
            }
        } catch (\Exception $e) {
            // 패턴 오류 시 false 반환
            return false;
        }
    }

    /**
     * 우선순위 재정렬 메서드
     */
    public static function reorderPriorities(array $ruleIds): bool
    {
        try {
            foreach ($ruleIds as $index => $ruleId) {
                static::where('id', $ruleId)->update(['priority' => $index + 1]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
