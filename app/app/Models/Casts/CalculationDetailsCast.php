<?php

namespace App\Models\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class CalculationDetailsCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): ?array
    {
        // DB에서 null/빈 문자열이면 null 반환
        if ($value === null || $value === '') {
            return null;
        }

        // 이미 배열이면 그대로 반환
        if (is_array($value)) {
            return $value;
        }

        // 문자열이면 JSON 디코드 시도
        if (is_string($value) && !empty($value)) {
            $decoded = json_decode($value, true);
            return json_last_error() === JSON_ERROR_NONE ? $decoded : null;
        }

        return null;
    }

    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        // null/빈 값은 null 저장
        if ($value === null || $value === '') {
            return null;
        }

        // 배열이면 JSON으로 저장
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION);
        }

        // 문자열이 들어오는 경우: 유효한 JSON이면 정규화하여 저장, 아니면 그대로 저장(필요 시 정책에 맞게 조정)
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION);
            }

            // 정책에 따라: 여기서 예외를 던지거나 null을 저장하도록 바꿀 수 있습니다.
            return $value;
        }

        // 그 외 타입은 문자열로 강제 저장하거나 null 처리
        return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION);
    }
}
