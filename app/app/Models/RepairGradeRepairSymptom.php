<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairGradeRepairSymptom extends Model
{
    protected $table = 'repair_grade_repair_symptom';
    protected $primaryKey = ['repair_grade_id', 'repair_symptom_id'];
    public $incrementing = false;

    protected $fillable = [
        'repair_grade_id',
        'repair_symptom_id',
    ];

    /**
     * 수리 등급과의 관계
     */
    public function repairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'repair_grade_id', 'id');
    }

    /**
     * 수리 처리와의 관계
     */
    public function repairSymptom(): BelongsTo
    {
        return $this->belongsTo(RepairSymptom::class, 'repair_symptom_id', 'id');
    }
}
