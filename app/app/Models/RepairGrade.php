<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairGrade extends Model
{
    const GRADE_BEST = "ST_BEST"; # 최상
    const GRADE_GOOD = "ST_GOOD"; # 좋음
    const GRADE_NORMAL = "ST_NORMAL"; # 일반
    const GRADE_REFURB = "ST_REFURB"; # 리퍼
    const GRADE_XL = "ST_XL"; # 폐기
    const GRADE_XL1 = "ST_XL1"; # 폐기
    const GRADE_XL2 = "ST_XL2"; # 폐기
    const GRADE_XL3 = "ST_XL3"; # 폐기
    const GRADE_XL4 = "ST_XL4"; # 폐기
    const GRADE_XL5 = "ST_XL5"; # 폐기
    const GRADE_XL6 = "ST_XL6"; # 폐기
    const GRADE_WAITING = "ST_WAITING"; # 수리대기

    public static array $GRADE_NAME = [
        self::GRADE_BEST => "CB",
        self::GRADE_GOOD => "CG",
        self::GRADE_NORMAL => "CN",
        self::GRADE_REFURB => "RP",
        self::GRADE_XL => "XL",
        self::GRADE_XL1 => "XL1",
        self::GRADE_XL2 => "XL2",
        self::GRADE_XL3 => "XL3",
        self::GRADE_XL4 => "XL4",
        self::GRADE_XL5 => "XL5",
        self::GRADE_XL6 => "XL6",
        self::GRADE_WAITING => "WT",
    ];

    protected $fillable = ['order_no', 'name', 'code'];

    public function pallets(): HasMany
    {
        return $this->hasMany(Pallet::class, 'repair_grade_id', 'id');
    }

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'repair_grade_id', 'id');
    }

    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'repair_grade_id', 'id');
    }

    public function repairProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'repair_grade_id', 'id');
    }

    public function repairProcesses(): BelongsToMany
    {
        return $this->belongsToMany(RepairProcess::class, 'repair_grade_repair_process');
    }

    /**
     * 이 등급에 연결된 증상들을 가져옵니다.
     */
    public function repairSymptoms(): BelongsToMany
    {
        return $this->belongsToMany(RepairSymptom::class, 'repair_grade_repair_symptom');
    }
}
