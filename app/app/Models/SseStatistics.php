<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * SSE 통계 모델
 * 
 * SSE 연결 및 메시지 전송 통계를 일별로 수집하여
 * 모니터링 및 성능 분석에 활용하기 위한 모델입니다.
 * 
 * @property int $id
 * @property \Carbon\Carbon $date 통계 수집 날짜
 * @property int $total_connections 총 연결 수
 * @property int $authenticated_connections 인증된 연결 수
 * @property int $guest_connections 게스트 연결 수
 * @property int $messages_sent 전송된 메시지 수
 * @property int $failed_messages 전송 실패 메시지 수
 * @property int $peak_concurrent_connections 최대 동시 연결 수
 * @property float $average_connection_duration 평균 연결 지속 시간(분)
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class SseStatistics extends Model
{
    use HasFactory;

    /**
     * 테이블명
     */
    protected $table = 'sse_statistics';

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'date',
        'total_connections',
        'authenticated_connections',
        'guest_connections',
        'messages_sent',
        'failed_messages',
        'peak_concurrent_connections',
        'average_connection_duration',
    ];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'date' => 'date',
        'total_connections' => 'integer',
        'authenticated_connections' => 'integer',
        'guest_connections' => 'integer',
        'messages_sent' => 'integer',
        'failed_messages' => 'integer',
        'peak_concurrent_connections' => 'integer',
        'average_connection_duration' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 오늘 통계 조회 또는 생성
     */
    public static function today(): self
    {
        return static::firstOrCreate(
            ['date' => now()->toDateString()],
            [
                'total_connections' => 0,
                'authenticated_connections' => 0,
                'guest_connections' => 0,
                'messages_sent' => 0,
                'failed_messages' => 0,
                'peak_concurrent_connections' => 0,
                'average_connection_duration' => 0,
            ]
        );
    }

    /**
     * 연결 수 증가
     */
    public function incrementConnections(bool $isAuthenticated = false): void
    {
        $this->increment('total_connections');
        
        if ($isAuthenticated) {
            $this->increment('authenticated_connections');
        } else {
            $this->increment('guest_connections');
        }
    }

    /**
     * 메시지 전송 수 증가
     */
    public function incrementMessagesSent(int $count = 1): void
    {
        $this->increment('messages_sent', $count);
    }

    /**
     * 실패 메시지 수 증가
     */
    public function incrementFailedMessages(int $count = 1): void
    {
        $this->increment('failed_messages', $count);
    }

    /**
     * 최대 동시 연결 수 업데이트
     */
    public function updatePeakConnections(int $currentConnections): void
    {
        if ($currentConnections > $this->peak_concurrent_connections) {
            $this->update(['peak_concurrent_connections' => $currentConnections]);
        }
    }

    /**
     * 평균 연결 지속 시간 업데이트
     */
    public function updateAverageConnectionDuration(float $duration): void
    {
        // 가중 평균 계산
        $currentAverage = $this->average_connection_duration;
        $totalConnections = $this->total_connections;
        
        if ($totalConnections > 0) {
            $newAverage = (($currentAverage * ($totalConnections - 1)) + $duration) / $totalConnections;
            $this->update(['average_connection_duration' => $newAverage]);
        }
    }
}
