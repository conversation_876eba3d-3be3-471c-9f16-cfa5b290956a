<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * SSE 오프라인 알림 모델
 * 
 * 사용자가 오프라인 상태일 때 전송된 알림을 저장하고
 * 로그인 시 미전달 알림을 전송하기 위한 모델입니다.
 * 
 * @property int $id
 * @property int $user_id 알림 대상 사용자 ID
 * @property string $type 알림 타입 (notification, data_update 등)
 * @property array $data 알림 데이터 (JSON 형태)
 * @property \Carbon\Carbon $created_at 알림 생성 시간
 * @property \Carbon\Carbon|null $delivered_at 알림 전달 완료 시간
 * @property-read \App\Models\User $user 알림 대상 사용자
 */
class SseOfflineNotification extends Model
{
    use HasFactory;

    /**
     * 테이블명
     */
    protected $table = 'sse_offline_notifications';

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'user_id',
        'type',
        'data',
        'delivered_at',
    ];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'data' => 'array',
        'created_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];

    /**
     * created_at만 사용하고 updated_at은 사용하지 않음
     */
    const UPDATED_AT = null;

    /**
     * 알림 대상 사용자와의 관계
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 미전달 알림 조회 스코프
     */
    public function scopeUndelivered($query)
    {
        return $query->whereNull('delivered_at');
    }

    /**
     * 특정 사용자의 미전달 알림 조회 스코프
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 알림을 전달 완료로 표시
     */
    public function markAsDelivered(): bool
    {
        $this->delivered_at = now();
        return $this->save();
    }

    /**
     * 알림이 전달되었는지 확인
     */
    public function isDelivered(): bool
    {
        return !is_null($this->delivered_at);
    }
}
