<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * 수리 프로세스 매핑 모델
 *
 * @property int $id
 * @property int $repair_process_id 수리 프로세스 ID
 * @property int $repair_cost_type_id 수리 유형 ID
 * @property bool $is_active 활성화 상태
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read RepairProcess $repairProcess 수리 프로세스
 * @property-read RepairCostType $repairCostType 수리 유형
 */
class RepairCostTypeProcessMapping extends Model
{
    use HasFactory;

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'repair_process_id',
        'repair_cost_type_id',
        'is_active'
    ];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * RepairProcess와의 관계 설정
     */
    public function repairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class);
    }

    /**
     * RepairCostType과의 관계 설정
     */
    public function repairCostType(): BelongsTo
    {
        return $this->belongsTo(RepairCostType::class);
    }

    /**
     * 활성화된 매핑만 조회하는 스코프
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 비활성화된 매핑만 조회하는 스코프
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * 특정 수리 유형의 매핑만 조회하는 스코프
     */
    public function scopeByRepairType($query, string $repairTypeCode)
    {
        return $query->whereHas('repairCostType', function($q) use ($repairTypeCode) {
            $q->where('code', $repairTypeCode);
        });
    }

    /**
     * 특정 프로세스의 매핑만 조회하는 스코프
     */
    public function scopeByProcessId($query, int $processId)
    {
        return $query->where('repair_process_id', $processId);
    }

    /**
     * 특정 프로세스 코드의 매핑만 조회하는 스코프
     */
    public function scopeByProcessCode($query, string $processCode)
    {
        return $query->whereHas('repairProcess', function($q) use ($processCode) {
            $q->where('code', $processCode);
        });
    }

    /**
     * 수리 유형 코드 반환 (기존 호환성을 위해)
     */
    public function getRepairTypeCodeAttribute(): string
    {
        return $this->repairCostType->code;
    }

    /**
     * 수리 유형 이름 반환 (기존 호환성을 위해)
     */
    public function getRepairTypeNameAttribute(): string
    {
        return $this->repairCostType->name;
    }

    /**
     * 수리 유형 표시명 반환 (기존 호환성을 위해)
     */
    public function getRepairTypeDisplayNameAttribute(): string
    {
        return $this->repairCostType->display_name;
    }

    /**
     * 프로세스 코드 반환 (기존 호환성을 위해)
     */
    public function getProcessCodeAttribute(): string
    {
        return $this->repairProcess->code;
    }

    /**
     * 프로세스 이름 반환 (기존 호환성을 위해)
     */
    public function getProcessNameAttribute(): string
    {
        return $this->repairProcess->name;
    }
}
