<?php

namespace App\Excel\Middleware;

use Maatwebsite\Excel\Middleware\CellMiddleware;

class CleanCellValue extends CellMiddleware
{
    public function __invoke($value, callable $next)
    {
        if (!is_string($value)) {
            return $next($value);
        }

        // UTF-8 인코딩 정리(value를 UTF-8로 재인코딩)
        // 만약 깨진 UTF-8 문자열이 들어오면 가능한 범위 내에서 잘못된 바이트 교정
        $cleaned = mb_convert_encoding($value, 'UTF-8', 'UTF-8');

        // 잘못된 UTF-8 시퀀스 제거
        // ASCII 컨트롤 문자 (NULL, BEL, VT 등) 및 DEL(0x7F) 전체 문자열에서 삭제
        $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);

        // 제어 문자 제거
        // Zero Width Space(0x200B) ~ Zero Width Joiner(0x200D), BOM(0xFEFF)
        // 문자열 전체에서 일괄 삭제 (앞뒤, 가운데 구분 없이)
        $cleaned = preg_replace('/[\x{200B}-\x{200D}\x{FEFF}]/u', '', $cleaned);

        // 캐리지 리턴 및 줄바꿈 제거(줄바꿈 자체 삭제가 아니라 [EOL]으로 대체)
        // 나중에 보여 줄 때 [EOL] 을 줄바꿈으로 치환해 보여 줘야 함
        $cleaned = preg_replace('/\r\n|\r|\n/', '[EOL]', $cleaned);

        return $next($cleaned);
    }
}
