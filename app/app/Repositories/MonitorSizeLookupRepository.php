<?php

namespace App\Repositories;

use App\Models\MonitorSizeLookup;
use App\Repositories\Interfaces\MonitorSizeLookupRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;

class MonitorSizeLookupRepository implements MonitorSizeLookupRepositoryInterface
{
    public function search(array $filters, int $perPage = 20): LengthAwarePaginator
    {
        $query = MonitorSizeLookup::query();

        // name 정확 매칭 또는 부분 검색은 name으로는 정확 매칭만, 부분 검색은 별도 필요 시 확장
        if (!empty($filters['name'])) {
            $query->where('name', $filters['name']);
        }

        if (!empty($filters['name_hash'])) {
            $query->where('name_hash', $filters['name_hash']);
        }

        if (!empty($filters['brand'])) {
            $query->where('brand', $filters['brand']);
        }

        if (!empty($filters['unit'])) {
            $query->where('unit', $filters['unit']);
        }

        if (!empty($filters['min_size'])) {
            $query->where('size', '>=', (float) $filters['min_size']);
        }
        if (!empty($filters['max_size'])) {
            $query->where('size', '<=', (float) $filters['max_size']);
        }

        // 정렬 처리
        $sortBy = $filters['sortBy'] ?? null;
        $sortDir = $filters['sortDir'] ?? 'asc';
        if ($sortBy) {
            $query->orderBy($sortBy, $sortDir === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderByDesc('id');
        }

        $perPage = $filters['pageSize'] ?? $perPage;

        return $query->paginate((int) $perPage)->withQueryString();
    }

    public function findById(int $id): ?MonitorSizeLookup
    {
        return MonitorSizeLookup::find($id);
    }

    public function updateById(int $id, array $attributes): MonitorSizeLookup
    {
        $item = MonitorSizeLookup::findOrFail($id);
        $item->update($attributes);
        return $item->refresh();
    }
}


