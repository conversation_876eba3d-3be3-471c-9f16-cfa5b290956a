<?php

namespace App\Listeners;

use App\Services\NotificationManager;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 사용자 로그인 시 미전달 오프라인 알림을 전송하는 리스너
 * 
 * 사용자가 로그인할 때 자동으로 실행되어 오프라인 상태에서
 * 저장된 알림들을 실시간으로 전송합니다.
 */
class DeliverOfflineNotifications implements ShouldQueue
{
    use InteractsWithQueue;

    private NotificationManager $notificationManager;

    /**
     * 리스너 생성자
     */
    public function __construct(NotificationManager $notificationManager)
    {
        $this->notificationManager = $notificationManager;
    }

    /**
     * 로그인 이벤트 처리
     * 
     * 사용자 로그인 시 미전달 오프라인 알림을 조회하고 전송합니다.
     * 큐를 통해 비동기적으로 처리되어 로그인 성능에 영향을 주지 않습니다.
     */
    public function handle(Login $event): void
    {
        try {
            $user = $event->user;
            
            if (!$user || !$user->id) {
                Log::warning('로그인 이벤트에서 유효하지 않은 사용자 정보');
                return;
            }

            Log::info('사용자 로그인 감지, 오프라인 알림 전송 시작', [
                'user_id' => $user->id,
                'username' => $user->username ?? 'unknown'
            ]);

            // 잠시 대기 후 알림 전송 (SSE 연결이 설정될 시간을 확보)
            sleep(2);

            // 미전달 오프라인 알림 전송
            $result = $this->notificationManager->deliverOfflineNotifications($user->id);

            if ($result['total_notifications'] > 0) {
                Log::info('로그인 시 오프라인 알림 전송 완료', [
                    'user_id' => $user->id,
                    'total_notifications' => $result['total_notifications'],
                    'delivered_count' => $result['delivered_count'],
                    'failed_count' => $result['failed_count']
                ]);
            }

        } catch (Exception $e) {
            Log::error('로그인 시 오프라인 알림 전송 중 오류 발생', [
                'user_id' => $event->user->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 작업 실패 시 처리
     */
    public function failed(Login $event, Exception $exception): void
    {
        Log::error('오프라인 알림 전송 작업 실패', [
            'user_id' => $event->user->id ?? 'unknown',
            'error' => $exception->getMessage()
        ]);
    }
}
