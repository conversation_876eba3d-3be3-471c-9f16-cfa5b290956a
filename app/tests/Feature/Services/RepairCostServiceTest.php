<?php

use App\Services\RepairCostService;
use App\Services\MonitorSizeExtractionService;
use App\Services\RepairCostTypeProcessMappingService;
use App\Models\Product;
use App\Models\RepairCostPolicy;
use App\Models\RepairCostCategory;
use App\Models\RepairCostRange;
use App\Models\RepairCost;
use App\Models\Cate4;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    $this->monitorService = Mockery::mock(MonitorSizeExtractionService::class);
    $this->mappingService = Mockery::mock(RepairCostTypeProcessMappingService::class);
    $this->service = new RepairCostService($this->monitorService, $this->mappingService);
});

it('수리비 계산이 성공적으로 수행된다', function () {
    // Given: 테스트 데이터 생성
    $system = RepairCostPolicy::create([
        'name' => 'general',
        'display_name' => '일반',
        'pricing_type' => 'price',
        'is_active' => true
    ]);

    $cate4 = Cate4::create([
        'cate4_no' => 1001,
        'name' => '테스트 카테고리'
    ]);

    $category = RepairCostCategory::create([
        'repair_cost_policy_id' => $system->id,
        'cate4_id' => $cate4->id,
        'pricing_criteria' => 'price',
        'is_active' => true
    ]);

    $range = RepairCostRange::create([
        'repair_cost_category_id' => $category->id,
        'range_name' => '10만원 미만',
        'min_value' => 0,
        'max_value' => 100000,
        'unit' => 'won',
        'is_active' => true
    ]);

    $repairCost = RepairCost::create([
        'repair_cost_range_id' => $range->id,
        'repair_type' => 'repair_parts',
        'amount' => 30000
    ]);

    $product = Product::create([
        'qaid' => 'TEST001',
        'name' => '테스트 제품',
        'cate4_id' => $cate4->id,
        'amount' => 50000,
        'status' => Product::STATUS_REGISTERED
    ]);

    // Mock 설정
    $this->mappingService
        ->shouldReceive('getRepairTypeByCode')
        ->with('TEST_PROCESS')
        ->once()
        ->andReturn('repair_parts');

    // When: 수리비 계산 실행
    $result = $this->service->calculateRepairCost($product, 'TEST_PROCESS');

    // Then: 결과 검증
    expect($result['amount'])->toBe(30000);
    expect($result['basis'])->toContain('일반');
    expect($result['details']['policy_name'])->toBe('general');
    expect($result['details']['repair_type'])->toBe('repair_parts');
});

it('프로세스 코드 매핑이 없을 때 기본값을 반환한다', function () {
    // Given: 매핑이 없는 프로세스 코드
    $product = Product::create([
        'qaid' => 'TEST002',
        'name' => '테스트 제품',
        'status' => Product::STATUS_REGISTERED
    ]);

    // Mock 설정
    $this->mappingService
        ->shouldReceive('getRepairTypeByCode')
        ->with('UNKNOWN_PROCESS')
        ->once()
        ->andReturn(null);

    // When: 수리비 계산 실행
    $result = $this->service->calculateRepairCost($product, 'UNKNOWN_PROCESS');

    // Then: 기본값 반환 확인
    expect($result['amount'])->toBe(RepairCostService::DEFAULT_REPAIR_COST);
    expect($result['basis'])->toContain('기본값 적용');
    expect($result['details']['failure_reason'])->toBe('매핑되지 않은 프로세스 코드');
});

it('카테고리 매핑을 올바르게 조회한다', function () {
    // Given: 카테고리 매핑 데이터
    $system = RepairCostPolicy::create([
        'name' => 'monitor',
        'display_name' => '모니터',
        'pricing_type' => 'size',
        'is_active' => true
    ]);

    $cate4 = Cate4::create([
        'cate4_no' => 2001,
        'name' => '모니터 카테고리'
    ]);

    $category = RepairCostCategory::create([
        'repair_cost_policy_id' => $system->id,
        'cate4_id' => $cate4->id,
        'pricing_criteria' => 'size',
        'is_active' => true
    ]);

    $product = Product::create([
        'qaid' => 'TEST003',
        'name' => '삼성 모니터 24인치',
        'cate4_id' => $cate4->id,
        'status' => Product::STATUS_REGISTERED
    ]);

    // When: 카테고리 조회
    $foundCategory = $this->service->findCostCategory($product);

    // Then: 올바른 카테고리 반환 확인
    expect($foundCategory)->not->toBeNull();
    expect($foundCategory->id)->toBe($category->id);
    expect($foundCategory->system->name)->toBe('monitor');
});

it('기본 카테고리를 올바르게 조회한다', function () {
    // Given: 기본 시스템 설정
    $system = RepairCostPolicy::create([
        'name' => 'general',
        'display_name' => '일반',
        'pricing_type' => 'price',
        'is_active' => true
    ]);

    $cate4 = Cate4::create([
        'cate4_no' => 3001,
        'name' => '일반 카테고리'
    ]);

    $category = RepairCostCategory::create([
        'repair_cost_policy_id' => $system->id,
        'cate4_id' => $cate4->id,
        'pricing_criteria' => 'price',
        'is_active' => true
    ]);

    // When: 기본 카테고리 조회
    $defaultCategory = $this->service->getDefaultCategory('general');

    // Then: 올바른 기본 카테고리 반환 확인
    expect($defaultCategory)->not->toBeNull();
    expect($defaultCategory->id)->toBe($category->id);
});

it('계산 결과를 올바르게 검증한다', function () {
    // Given: 유효한 계산 결과
    $validResult = [
        'amount' => 30000,
        'basis' => '일반 > 10만원 미만 > 수리_부품교체',
        'details' => [
            'policy_name' => 'general',
            'category_id' => 1,
            'range_id' => 1,
            'repair_type' => 'repair_parts'
        ]
    ];

    // 유효하지 않은 계산 결과
    $invalidResult = [
        'amount' => 0,
        'basis' => '테스트'
    ];

    // When & Then: 검증 결과 확인
    expect($this->service->validateCalculationResult($validResult))->toBeTrue();
    expect($this->service->validateCalculationResult($invalidResult))->toBeFalse();
});
