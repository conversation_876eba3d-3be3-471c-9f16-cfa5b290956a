<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\ConnectionManager;
use App\Http\Middleware\SseSecurityMiddleware;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

/**
 * SSE 연결 통합 테스트
 * 
 * SSE 컨트롤러와 보안 미들웨어의 통합 동작을 테스트합니다.
 * - 인증된 사용자 연결 테스트
 * - 게스트 사용자 연결 테스트  
 * - 하트비트 및 연결 유지 테스트
 */
class SseConnectionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private ConnectionManager $connectionManager;
    private SseSecurityMiddleware $securityMiddleware;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->connectionManager = app(ConnectionManager::class);
        $this->securityMiddleware = app(SseSecurityMiddleware::class);
        
        // Redis 테스트 데이터 정리
        Redis::flushdb();
        Cache::flush();
    }

    protected function tearDown(): void
    {
        // 테스트 후 정리
        Redis::flushdb();
        Cache::flush();
        
        parent::tearDown();
    }

    /**
     * 인증된 사용자 SSE 연결 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 인증된_사용자가_SSE에_연결할_수_있다(): void
    {
        // Given: 인증된 사용자
        $user = User::factory()->create([
            'name' => '테스트 사용자',
            'email' => '<EMAIL>'
        ]);
        
        $this->actingAs($user);
        Session::start();

        // When: SSE 연결 정보 요청
        $response = $this->postJson('/sse/connect', [
            '_token' => csrf_token()
        ], [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);

        // Then: 성공적으로 연결 정보 반환
        if ($response->status() !== 200) {
            dump('Response status: ' . $response->status());
            dump('Response body: ' . $response->getContent());
        }
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'user_id' => $user->id,
                        'is_authenticated' => true,
                        'heartbeat_interval' => 30
                    ]
                ]);

        // 연결 통계 확인
        $this->assertArrayHasKey('connection_stats', $response->json('data'));
        $stats = $response->json('data.connection_stats');
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_connections', $stats);
    }

    /**
     * 게스트 사용자 SSE 연결 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 게스트_사용자가_SSE에_연결할_수_있다(): void
    {
        // Given: 비인증 상태
        Session::start();

        // When: SSE 연결 정보 요청
        $response = $this->postJson('/sse/connect', [
            '_token' => csrf_token()
        ], [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);

        // Then: 게스트로 연결 정보 반환
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'user_id' => null,
                        'is_authenticated' => false,
                        'heartbeat_interval' => 30
                    ]
                ]);
    }

    /**
     * SSE 스트림 연결 테스트 (인증된 사용자)
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 인증된_사용자가_SSE_스트림에_연결할_수_있다(): void
    {
        // Given: 인증된 사용자
        $user = User::factory()->create();
        $this->actingAs($user);

        // When: SSE 스트림 요청
        $response = $this->get('/sse/stream', [
            'Accept' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);

        // Then: SSE 스트림 응답 확인
        if ($response->getStatusCode() !== 200) {
            dump('SSE Stream Response Status: ' . $response->getStatusCode());
            dump('SSE Stream Response Body: ' . $response->getContent());
        }
        
        $response->assertStatus(200);
        $this->assertStringContainsString('text/event-stream', $response->headers->get('Content-Type'));
        $this->assertStringContainsString('no-cache', $response->headers->get('Cache-Control'));
        $response->assertHeader('Connection', 'keep-alive');
        $response->assertHeader('X-Accel-Buffering', 'no');
    }

    /**
     * SSE 스트림 연결 테스트 (게스트 사용자)
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 게스트_사용자가_SSE_스트림에_연결할_수_있다(): void
    {
        // Given: 비인증 상태

        // When: SSE 스트림 요청
        $response = $this->get('/sse/stream', [
            'Accept' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);

        // Then: SSE 스트림 응답 확인
        if ($response->getStatusCode() !== 200) {
            dump('Guest SSE Stream Response Status: ' . $response->getStatusCode());
            dump('Guest SSE Stream Response Body: ' . $response->getContent());
        }
        
        $response->assertStatus(200);
        $this->assertStringContainsString('text/event-stream', $response->headers->get('Content-Type'));
        $this->assertStringContainsString('no-cache', $response->headers->get('Cache-Control'));
    }

    /**
     * 연결 해제 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function SSE_연결을_해제할_수_있다(): void
    {
        // Given: 인증된 사용자와 연결 ID
        $user = User::factory()->create();
        $this->actingAs($user);
        Session::start();
        
        $connectionId = 'test-connection-' . uniqid();
        
        // 연결 추가
        $this->connectionManager->addConnection($connectionId, $user->id, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        // When: 연결 해제 요청
        $response = $this->postJson('/sse/disconnect', [
            'connection_id' => $connectionId,
            '_token' => csrf_token()
        ], [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);

        // Then: 성공적으로 연결 해제
        if ($response->status() !== 200) {
            dump('Disconnect Response Status: ' . $response->status());
            dump('Disconnect Response Body: ' . $response->getContent());
        }
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '연결이 성공적으로 해제되었습니다.'
                ]);

        // 연결이 실제로 제거되었는지 확인
        $connection = $this->connectionManager->getConnection($connectionId);
        $this->assertNull($connection);
    }

    /**
     * Rate Limiting 테스트
     * 
     * @test
     * @group sse
     * @group security
     */
    public function Rate_Limit을_초과하면_연결이_거부된다(): void
    {
        // Given: 세션 시작
        Session::start();
        
        // When: Rate Limit을 초과하는 요청 (61회)
        for ($i = 0; $i < 61; $i++) {
            $response = $this->postJson('/sse/connect', [
                '_token' => csrf_token()
            ], [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]);
            
            if ($i < 60) {
                // 처음 60회는 성공해야 함 (400은 다른 검증 실패, 429는 Rate Limit)
                if (!in_array($response->status(), [200, 400, 500])) {
                    dump("Request {$i}: Status {$response->status()}, Body: {$response->getContent()}");
                }
                $this->assertTrue(in_array($response->status(), [200, 400, 500])); // 400은 다른 검증 실패
            }
        }

        // 61번째 요청은 Rate Limit으로 거부되어야 함
        $response = $this->postJson('/sse/connect', [
            '_token' => csrf_token()
        ]);
        
        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'message' => '요청이 너무 많습니다. 잠시 후 다시 시도해주세요.'
                ]);
    }

    /**
     * CSRF 토큰 검증 테스트
     * 
     * @test
     * @group sse
     * @group security
     */
    public function CSRF_토큰이_없으면_POST_요청이_거부된다(): void
    {
        // Given: 인증된 사용자 (CSRF 토큰 없음)
        $user = User::factory()->create();
        $this->actingAs($user);

        // When: CSRF 토큰 없이 POST 요청
        $response = $this->postJson('/sse/connect', [], [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);

        // Then: CSRF 검증 실패로 거부 (400 또는 403 가능)
        if (!in_array($response->status(), [400, 403])) {
            dump('CSRF Test Response Status: ' . $response->status());
            dump('CSRF Test Response Body: ' . $response->getContent());
        }
        
        $this->assertTrue(in_array($response->status(), [400, 403]));
        
        $responseData = $response->json();
        $this->assertFalse($responseData['success']);
        $this->assertArrayHasKey('message', $responseData);
    }

    /**
     * 잘못된 User-Agent 테스트
     * 
     * @test
     * @group sse
     * @group security
     */
    public function 의심스러운_User_Agent는_연결이_거부된다(): void
    {
        // Given: 의심스러운 User-Agent
        Session::start();
        
        $suspiciousUserAgents = [
            'bot',
            'crawler',
            'spider',
            'scraper',
            'GoogleBot',
            'BingBot'
        ];

        foreach ($suspiciousUserAgents as $userAgent) {
            // When: 의심스러운 User-Agent로 요청
            $response = $this->postJson('/sse/connect', [
                '_token' => csrf_token()
            ], [
                'User-Agent' => $userAgent
            ]);

            // Then: 연결 거부
            $response->assertStatus(400)
                    ->assertJson([
                        'success' => false,
                        'message' => '유효하지 않은 연결 요청입니다.'
                    ]);
        }
    }

    /**
     * 연결 통계 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 연결_통계가_정확히_계산된다(): void
    {
        // Given: 여러 사용자 연결
        $authenticatedUser = User::factory()->create();
        $connectionId1 = 'auth-connection-' . uniqid();
        $connectionId2 = 'guest-connection-' . uniqid();

        // 인증된 사용자 연결
        $this->connectionManager->addConnection($connectionId1, $authenticatedUser->id, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        // 게스트 연결
        $this->connectionManager->addConnection($connectionId2, null, [
            'ip_address' => '*********',
            'user_agent' => 'Test Agent'
        ]);

        // When: 연결 통계 조회
        $stats = $this->connectionManager->getConnectionStats();

        // Then: 정확한 통계 반환
        $this->assertEquals(2, $stats['total_connections']);
        $this->assertEquals(1, $stats['authenticated_connections']);
        $this->assertEquals(1, $stats['guest_connections']);
        $this->assertArrayHasKey('timestamp', $stats);

        // 정리
        $this->connectionManager->removeConnection($connectionId1);
        $this->connectionManager->removeConnection($connectionId2);
    }

    /**
     * 하트비트 업데이트 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 하트비트가_정상적으로_업데이트된다(): void
    {
        // Given: 연결된 사용자
        $user = User::factory()->create();
        $connectionId = 'heartbeat-test-' . uniqid();
        
        $this->connectionManager->addConnection($connectionId, $user->id, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        // 초기 연결 정보 확인
        $initialConnection = $this->connectionManager->getConnection($connectionId);
        $this->assertNotNull($initialConnection);
        $initialHeartbeat = $initialConnection['last_heartbeat'];

        // 잠시 대기
        sleep(1);

        // When: 하트비트 업데이트
        $result = $this->connectionManager->updateHeartbeat($connectionId);

        // Then: 하트비트 성공적으로 업데이트
        $this->assertTrue($result);

        $updatedConnection = $this->connectionManager->getConnection($connectionId);
        $this->assertNotNull($updatedConnection);
        $this->assertNotEquals($initialHeartbeat, $updatedConnection['last_heartbeat']);

        // 정리
        $this->connectionManager->removeConnection($connectionId);
    }

    /**
     * 만료된 연결 정리 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 만료된_연결이_정리된다(): void
    {
        // Given: 만료된 연결 시뮬레이션
        $user = User::factory()->create();
        $connectionId = 'expired-connection-' . uniqid();
        
        // 연결 추가
        $this->connectionManager->addConnection($connectionId, $user->id, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        // 연결 데이터를 수동으로 과거 시간으로 설정 (만료 시뮬레이션)
        $expiredData = [
            'connection_id' => $connectionId,
            'user_id' => $user->id,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'connected_at' => now()->subHours(2)->toISOString(),
            'last_heartbeat' => now()->subMinutes(5)->toISOString(), // 5분 전 하트비트
            'is_authenticated' => true,
            'subscriptions' => ['notifications']
        ];

        // Redis에 만료된 데이터 직접 저장
        Redis::setex("sse:connections:{$connectionId}", 3600, json_encode($expiredData));

        // When: 만료된 연결 정리 실행
        $cleanedCount = $this->connectionManager->cleanupExpiredConnections();

        // Then: 만료된 연결이 정리됨
        $this->assertGreaterThanOrEqual(1, $cleanedCount);
        
        $connection = $this->connectionManager->getConnection($connectionId);
        $this->assertNull($connection);
    }

    /**
     * 사용자별 연결 조회 테스트
     * 
     * @test
     * @group sse
     * @group integration
     */
    public function 사용자별_연결을_조회할_수_있다(): void
    {
        // Given: 한 사용자의 여러 연결
        $user = User::factory()->create();
        $connectionId1 = 'user-conn-1-' . uniqid();
        $connectionId2 = 'user-conn-2-' . uniqid();
        
        $this->connectionManager->addConnection($connectionId1, $user->id, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Desktop Agent'
        ]);
        
        $this->connectionManager->addConnection($connectionId2, $user->id, [
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Mobile Agent'
        ]);

        // When: 사용자 연결 조회
        $userConnections = $this->connectionManager->getUserConnections($user->id);
        $isOnline = $this->connectionManager->isUserOnline($user->id);

        // Then: 정확한 연결 정보 반환
        $this->assertCount(2, $userConnections);
        $this->assertContains($connectionId1, $userConnections);
        $this->assertContains($connectionId2, $userConnections);
        $this->assertTrue($isOnline);

        // 정리
        $this->connectionManager->removeConnection($connectionId1);
        $this->connectionManager->removeConnection($connectionId2);
        
        // 연결 제거 후 오프라인 확인
        $this->assertFalse($this->connectionManager->isUserOnline($user->id));
    }
}