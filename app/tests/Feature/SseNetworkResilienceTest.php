<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\ConnectionManager;
use App\Services\NotificationManager;
use App\Services\ErrorRecoveryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Mockery;

/**
 * SSE 네트워크 복원력 및 재연결 테스트
 * 
 * 네트워크 단절, 서버 오류, Redis 연결 실패 등의 시나리오를 테스트합니다.
 */
class SseNetworkResilienceTest extends TestCase
{
    use RefreshDatabase;

    private ConnectionManager $connectionManager;
    private NotificationManager $notificationManager;
    private ErrorRecoveryService $errorRecoveryService;
    private User $testUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->connectionManager = app(ConnectionManager::class);
        $this->notificationManager = app(NotificationManager::class);
        $this->errorRecoveryService = app(ErrorRecoveryService::class);
        
        $this->testUser = User::factory()->create([
            'name' => '네트워크 테스트 사용자',
            'email' => '<EMAIL>'
        ]);

        Redis::flushdb();
    }

    protected function tearDown(): void
    {
        Redis::flushdb();
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     * Redis 연결 실패 시 폴백 메커니즘 테스트
     */
    public function redis_연결_실패_시_폴백_메커니즘_테스트()
    {
        // Given: Redis 연결이 실패하는 상황 시뮬레이션
        $mockRedis = Mockery::mock('alias:' . \Illuminate\Support\Facades\Redis::class);
        $mockRedis->shouldReceive('get')->andThrow(new \Exception('Redis connection failed'));
        $mockRedis->shouldReceive('set')->andThrow(new \Exception('Redis connection failed'));

        // When: 연결 추가 시도
        $connectionId = 'fallback-test-' . uniqid();
        
        try {
            $this->connectionManager->addConnection($connectionId, $this->testUser->id);
        } catch (\Exception $e) {
            // Redis 실패 시 메모리 기반 폴백 사용
            $this->errorRecoveryService->handleRedisError($e);
        }

        // Then: 오류 복구 서비스가 작동함
        $this->assertTrue(true); // 예외가 발생하지 않고 처리됨을 확인
    }

    /**
     * @test
     * 연결 중단 후 자동 재연결 테스트
     */
    public function 연결_중단_후_자동_재연결_테스트()
    {
        // Given: 사용자가 연결된 상태
        $connectionId = 'reconnect-test-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);
        
        $this->assertTrue($this->connectionManager->isUserOnline($this->testUser->id));

        // When: 연결이 예기치 않게 중단됨 (네트워크 오류 시뮬레이션)
        $this->connectionManager->removeConnection($connectionId);
        $this->assertFalse($this->connectionManager->isUserOnline($this->testUser->id));

        // 재연결 시뮬레이션
        $newConnectionId = 'reconnect-new-' . uniqid();
        $this->connectionManager->addConnection($newConnectionId, $this->testUser->id);

        // Then: 재연결이 성공함
        $this->assertTrue($this->connectionManager->isUserOnline($this->testUser->id));
        
        // 새 연결 ID로 사용자 매핑이 업데이트됨
        $userConnections = $this->connectionManager->getUserConnections($this->testUser->id);
        $this->assertContains($newConnectionId, $userConnections);
        $this->assertNotContains($connectionId, $userConnections);
    }

    /**
     * @test
     * 메시지 전송 실패 시 재시도 메커니즘 테스트
     */
    public function 메시지_전송_실패_시_재시도_메커니즘_테스트()
    {
        // Given: 연결된 사용자
        $connectionId = 'retry-test-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);

        // Redis 일시적 실패 시뮬레이션
        $failureCount = 0;
        $maxFailures = 2;

        Redis::shouldReceive('lpush')
            ->times($maxFailures)
            ->andReturnUsing(function() use (&$failureCount, $maxFailures) {
                $failureCount++;
                if ($failureCount <= $maxFailures) {
                    throw new \Exception('Temporary Redis failure');
                }
                return 1;
            });

        // When: 메시지 전송 시도
        $notificationData = [
            'title' => '재시도 테스트',
            'message' => '메시지 전송 재시도 테스트입니다.'
        ];

        // 재시도 로직 포함한 메시지 전송
        $success = false;
        $attempts = 0;
        $maxAttempts = 3;

        while (!$success && $attempts < $maxAttempts) {
            try {
                $this->notificationManager->sendToUser($this->testUser->id, $notificationData);
                $success = true;
            } catch (\Exception $e) {
                $attempts++;
                if ($attempts >= $maxAttempts) {
                    // 최대 재시도 횟수 초과 시 오프라인 저장
                    $this->notificationManager->storeOfflineNotification(
                        $this->testUser->id, 
                        $notificationData
                    );
                    $success = true; // 오프라인 저장으로 처리 완료
                }
                sleep(1); // 재시도 간 지연
            }
        }

        // Then: 재시도 후 성공하거나 오프라인 저장됨
        $this->assertTrue($success);
        $this->assertLessThanOrEqual($maxAttempts, $attempts);
    }

    /**
     * @test
     * 대량 연결 중단 시 복구 테스트
     */
    public function 대량_연결_중단_시_복구_테스트()
    {
        // Given: 다수의 사용자가 연결된 상태
        $users = User::factory()->count(20)->create();
        $connections = [];

        foreach ($users as $user) {
            $connectionId = "mass-disconnect-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[$user->id] = $connectionId;
        }

        // 모든 사용자가 온라인 상태인지 확인
        foreach ($users as $user) {
            $this->assertTrue($this->connectionManager->isUserOnline($user->id));
        }

        // When: 서버 재시작 등으로 모든 연결이 중단됨
        Redis::flushdb(); // 모든 연결 정보 삭제

        // 사용자들이 재연결 시도
        $reconnectedUsers = [];
        foreach ($users->take(15) as $user) { // 15명만 재연결 (5명은 오프라인 상태)
            $newConnectionId = "reconnect-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($newConnectionId, $user->id);
            $reconnectedUsers[] = $user->id;
        }

        // Then: 재연결된 사용자들은 온라인 상태
        foreach ($reconnectedUsers as $userId) {
            $user = User::find($userId);
            $this->assertTrue($this->connectionManager->isUserOnline($user->id));
        }

        // 재연결하지 않은 사용자들은 오프라인 상태
        $offlineUsers = $users->skip(15);
        foreach ($offlineUsers as $user) {
            $this->assertFalse($this->connectionManager->isUserOnline($user->id));
        }

        // 전체 알림 발송 시 온라인 사용자에게만 전송되고, 오프라인 사용자는 저장됨
        $notificationData = [
            'title' => '복구 테스트 알림',
            'message' => '서버 복구 후 첫 번째 알림입니다.'
        ];

        $this->notificationManager->sendToAll($notificationData);

        // 오프라인 사용자들의 알림이 데이터베이스에 저장되었는지 확인
        foreach ($offlineUsers as $user) {
            $this->assertDatabaseHas('sse_offline_notifications', [
                'user_id' => $user->id,
                'type' => 'notification',
                'delivered_at' => null
            ]);
        }
    }

    /**
     * @test
     * 네트워크 지연 상황에서의 메시지 순서 보장 테스트
     */
    public function 네트워크_지연_상황에서의_메시지_순서_보장_테스트()
    {
        // Given: 사용자가 연결된 상태
        $connectionId = 'order-test-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);

        // When: 여러 메시지를 빠르게 연속 전송
        $messages = [];
        for ($i = 1; $i <= 5; $i++) {
            $messageData = [
                'title' => "메시지 {$i}",
                'message' => "순서 테스트 메시지 번호 {$i}",
                'sequence' => $i,
                'timestamp' => now()->addSeconds($i)->toISOString()
            ];
            
            $this->notificationManager->sendToUser($this->testUser->id, $messageData);
            $messages[] = $messageData;
            
            // 네트워크 지연 시뮬레이션
            usleep(100000); // 0.1초 지연
        }

        // Then: 메시지가 전송 순서대로 저장됨
        $storedMessages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
        $this->assertCount(5, $storedMessages);

        // 메시지 순서 확인
        for ($i = 0; $i < 5; $i++) {
            $storedMessage = json_decode($storedMessages[$i], true);
            $this->assertEquals($i + 1, $storedMessage['data']['sequence']);
        }
    }

    /**
     * @test
     * 메모리 부족 상황에서의 연결 관리 테스트
     */
    public function 메모리_부족_상황에서의_연결_관리_테스트()
    {
        // Given: 메모리 제한 시뮬레이션을 위한 대량 연결
        $maxConnections = 1000;
        $connections = [];

        // 대량 연결 생성
        for ($i = 1; $i <= $maxConnections; $i++) {
            $user = User::factory()->create(['name' => "사용자{$i}"]);
            $connectionId = "memory-test-{$i}-" . uniqid();
            
            try {
                $this->connectionManager->addConnection($connectionId, $user->id);
                $connections[] = ['user_id' => $user->id, 'connection_id' => $connectionId];
            } catch (\Exception $e) {
                // 메모리 부족 시 연결 제한 적용
                break;
            }
        }

        // When: 메모리 정리 및 오래된 연결 제거
        $activeConnections = $this->connectionManager->getActiveConnections();
        $connectionCount = count($activeConnections);

        // 오래된 연결 정리 (예: 30분 이상 비활성)
        $cutoffTime = now()->subMinutes(30);
        $cleanedCount = 0;

        foreach ($connections as $connection) {
            $connectionInfo = Redis::get("sse:connections:{$connection['connection_id']}");
            if ($connectionInfo) {
                $info = json_decode($connectionInfo, true);
                $lastActivity = \Carbon\Carbon::parse($info['last_heartbeat'] ?? $info['connected_at']);
                
                if ($lastActivity->lt($cutoffTime)) {
                    $this->connectionManager->removeConnection($connection['connection_id']);
                    $cleanedCount++;
                }
            }
        }

        // Then: 메모리 정리가 수행됨
        $newActiveConnections = $this->connectionManager->getActiveConnections();
        $newConnectionCount = count($newActiveConnections);

        $this->assertLessThanOrEqual($connectionCount, $newConnectionCount);
        echo "\n메모리 정리 결과: {$cleanedCount}개 연결 정리, {$newConnectionCount}개 연결 유지\n";
    }

    /**
     * @test
     * 동시 다중 메시지 전송 시 경합 조건 테스트
     */
    public function 동시_다중_메시지_전송_시_경합_조건_테스트()
    {
        // Given: 여러 사용자가 연결된 상태
        $users = User::factory()->count(10)->create();
        $connections = [];

        foreach ($users as $user) {
            $connectionId = "race-test-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[$user->id] = $connectionId;
        }

        // When: 동시에 여러 메시지 전송 (경합 조건 시뮬레이션)
        $processes = [];
        $messageCount = 5;

        for ($i = 0; $i < $messageCount; $i++) {
            $processes[] = function() use ($i) {
                $notificationData = [
                    'title' => "동시 메시지 {$i}",
                    'message' => "경합 조건 테스트 메시지 {$i}",
                    'batch_id' => 'race-test-batch'
                ];
                
                $this->notificationManager->sendToAll($notificationData);
            };
        }

        // 동시 실행 시뮬레이션
        foreach ($processes as $process) {
            $process();
        }

        // Then: 모든 메시지가 정상적으로 전송됨
        foreach ($connections as $userId => $connectionId) {
            $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
            $this->assertGreaterThanOrEqual($messageCount, count($messages));

            // 중복 메시지가 없는지 확인
            $messageTitles = [];
            foreach ($messages as $message) {
                $data = json_decode($message, true);
                if (isset($data['data']['batch_id']) && $data['data']['batch_id'] === 'race-test-batch') {
                    $messageTitles[] = $data['data']['title'];
                }
            }

            $uniqueTitles = array_unique($messageTitles);
            $this->assertEquals(count($messageTitles), count($uniqueTitles), '중복 메시지가 발견됨');
        }
    }

    /**
     * @test
     * 장시간 연결 유지 및 타임아웃 처리 테스트
     */
    public function 장시간_연결_유지_및_타임아웃_처리_테스트()
    {
        // Given: 사용자가 연결된 상태
        $connectionId = 'timeout-test-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);

        // 연결 시간을 과거로 설정하여 타임아웃 시뮬레이션
        $connectionInfo = [
            'connection_id' => $connectionId,
            'user_id' => $this->testUser->id,
            'connected_at' => now()->subHours(2)->toISOString(),
            'last_heartbeat' => now()->subMinutes(35)->toISOString(), // 35분 전 마지막 하트비트
            'is_authenticated' => true
        ];

        Redis::set("sse:connections:{$connectionId}", json_encode($connectionInfo));

        // When: 타임아웃 검사 실행
        $timeoutThreshold = now()->subMinutes(30); // 30분 타임아웃
        $timedOutConnections = [];

        $allConnections = $this->connectionManager->getActiveConnections();
        foreach ($allConnections as $connId) {
            $info = Redis::get("sse:connections:{$connId}");
            if ($info) {
                $data = json_decode($info, true);
                $lastActivity = \Carbon\Carbon::parse($data['last_heartbeat'] ?? $data['connected_at']);
                
                if ($lastActivity->lt($timeoutThreshold)) {
                    $timedOutConnections[] = $connId;
                }
            }
        }

        // 타임아웃된 연결 정리
        foreach ($timedOutConnections as $connId) {
            $this->connectionManager->removeConnection($connId);
        }

        // Then: 타임아웃된 연결이 정리됨
        $this->assertContains($connectionId, $timedOutConnections);
        $this->assertFalse($this->connectionManager->isUserOnline($this->testUser->id));
        
        // Redis에서 연결 정보가 제거됨
        $this->assertNull(Redis::get("sse:connections:{$connectionId}"));
    }

    /**
     * @test
     * 서버 부하 상황에서의 우선순위 기반 메시지 전송 테스트
     */
    public function 서버_부하_상황에서의_우선순위_기반_메시지_전송_테스트()
    {
        // Given: 다수의 사용자가 연결된 상태
        $users = User::factory()->count(50)->create();
        $connections = [];

        foreach ($users as $user) {
            $connectionId = "priority-test-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[$user->id] = $connectionId;
        }

        // When: 다양한 우선순위의 메시지 전송
        $messages = [
            ['priority' => 'critical', 'title' => '긴급 알림', 'message' => '즉시 확인 필요'],
            ['priority' => 'high', 'title' => '중요 알림', 'message' => '빠른 확인 필요'],
            ['priority' => 'normal', 'title' => '일반 알림', 'message' => '일반적인 알림'],
            ['priority' => 'low', 'title' => '정보 알림', 'message' => '참고용 정보']
        ];

        // 우선순위 역순으로 전송 (낮은 우선순위부터)
        foreach (array_reverse($messages) as $messageData) {
            $this->notificationManager->sendToAll($messageData);
        }

        // Then: 우선순위에 따라 메시지가 적절히 처리됨
        $sampleConnectionId = array_values($connections)[0];
        $storedMessages = Redis::lrange("sse:messages:{$sampleConnectionId}", 0, -1);
        
        $this->assertCount(4, $storedMessages);

        // 우선순위별 메시지 확인
        $priorities = [];
        foreach ($storedMessages as $message) {
            $data = json_decode($message, true);
            $priorities[] = $data['data']['priority'];
        }

        // 모든 우선순위 메시지가 전송되었는지 확인
        $this->assertContains('critical', $priorities);
        $this->assertContains('high', $priorities);
        $this->assertContains('normal', $priorities);
        $this->assertContains('low', $priorities);
    }
}