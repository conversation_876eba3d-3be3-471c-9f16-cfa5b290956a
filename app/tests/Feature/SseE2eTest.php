<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cate4;
use App\Models\Cate5;
use App\Services\ConnectionManager;
use App\Services\NotificationManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Illuminate\Support\Facades\Event;
use App\Events\ReqStartNotification;

/**
 * SSE E2E 통합 테스트
 * 
 * 실제 Svelte 앱 환경에서의 SSE 연결 및 실시간 데이터 업데이트를 테스트합니다.
 */
class SseE2eTest extends TestCase
{
    use RefreshDatabase;

    private ConnectionManager $connectionManager;
    private NotificationManager $notificationManager;
    private User $testUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->connectionManager = app(ConnectionManager::class);
        $this->notificationManager = app(NotificationManager::class);
        
        // 테스트 사용자 생성
        $this->testUser = User::factory()->create([
            'name' => '테스트 사용자',
            'email' => '<EMAIL>'
        ]);

        // Redis 테스트 데이터 정리
        Redis::flushdb();
    }

    protected function tearDown(): void
    {
        // Redis 정리
        Redis::flushdb();
        parent::tearDown();
    }

    /**
     * @test
     * SSE 연결 및 인증 E2E 테스트
     */
    public function sse_연결_및_인증_e2e_테스트()
    {
        // Given: 사용자가 로그인된 상태
        $this->actingAs($this->testUser);

        // When: SSE 스트림 엔드포인트에 연결 요청
        $response = $this->get('/api/sse/stream');

        // Then: 연결이 성공적으로 설정됨
        $response->assertOk();
        $response->assertHeader('Content-Type', 'text/event-stream');
        $response->assertHeader('Cache-Control', 'no-cache');
        $response->assertHeader('Connection', 'keep-alive');

        // SSE 헤더 확인
        $content = $response->getContent();
        $this->assertStringContainsString('data: {"type":"connected"', $content);
    }

    /**
     * @test
     * 게스트 사용자 SSE 연결 테스트
     */
    public function 게스트_사용자_sse_연결_테스트()
    {
        // Given: 비로그인 상태

        // When: SSE 스트림 엔드포인트에 연결 요청
        $response = $this->get('/api/sse/stream');

        // Then: 게스트 연결이 성공적으로 설정됨
        $response->assertOk();
        $response->assertHeader('Content-Type', 'text/event-stream');

        // 게스트 연결 메시지 확인
        $content = $response->getContent();
        $this->assertStringContainsString('data: {"type":"connected","user_type":"guest"}', $content);
    }

    /**
     * @test
     * 카테고리 변경 시 실시간 업데이트 E2E 테스트
     */
    public function 카테고리_변경_시_실시간_업데이트_e2e_테스트()
    {
        // Given: 사용자가 SSE에 연결된 상태
        $this->actingAs($this->testUser);
        $connectionId = 'test-connection-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);

        // 초기 카테고리 데이터 생성
        $cate4 = Cate4::create([
            'name' => '테스트 카테고리4',
            'description' => '테스트용 카테고리'
        ]);

        $cate5 = Cate5::create([
            'cate4_id' => $cate4->id,
            'name' => '테스트 카테고리5',
            'description' => '테스트용 하위 카테고리'
        ]);

        // When: 카테고리 데이터 변경
        $cate4->update(['name' => '업데이트된 카테고리4']);

        // Then: 연결된 클라이언트에게 업데이트 메시지가 전송됨
        $this->assertTrue($this->connectionManager->isUserOnline($this->testUser->id));

        // Redis에서 전송된 메시지 확인
        $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
        $this->assertNotEmpty($messages);

        $lastMessage = json_decode(end($messages), true);
        $this->assertEquals('data_update', $lastMessage['type']);
        $this->assertEquals('categories', $lastMessage['data']['model']);
        $this->assertArrayHasKey('payload', $lastMessage['data']);
    }

    /**
     * @test
     * 전체 알림 브로드캐스트 E2E 테스트
     */
    public function 전체_알림_브로드캐스트_e2e_테스트()
    {
        // Given: 여러 사용자가 연결된 상태
        $user1 = User::factory()->create(['name' => '사용자1']);
        $user2 = User::factory()->create(['name' => '사용자2']);

        $connection1 = 'conn-1-' . uniqid();
        $connection2 = 'conn-2-' . uniqid();
        $guestConnection = 'guest-' . uniqid();

        $this->connectionManager->addConnection($connection1, $user1->id);
        $this->connectionManager->addConnection($connection2, $user2->id);
        $this->connectionManager->addConnection($guestConnection); // 게스트 연결

        // When: 전체 알림 발송
        $notificationData = [
            'title' => '전체 공지사항',
            'message' => '시스템 점검이 예정되어 있습니다.',
            'priority' => 'high'
        ];

        $this->notificationManager->sendToAll($notificationData);

        // Then: 모든 연결(인증된 사용자 + 게스트)에게 알림이 전송됨
        $connections = [$connection1, $connection2, $guestConnection];
        
        foreach ($connections as $connectionId) {
            $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
            $this->assertNotEmpty($messages, "연결 {$connectionId}에 메시지가 전송되지 않음");

            $lastMessage = json_decode(end($messages), true);
            $this->assertEquals('notification', $lastMessage['type']);
            $this->assertEquals('전체 공지사항', $lastMessage['data']['title']);
        }
    }

    /**
     * @test
     * 개별 사용자 알림 E2E 테스트
     */
    public function 개별_사용자_알림_e2e_테스트()
    {
        // Given: 여러 사용자가 연결된 상태
        $targetUser = User::factory()->create(['name' => '대상 사용자']);
        $otherUser = User::factory()->create(['name' => '다른 사용자']);

        $targetConnection = 'target-' . uniqid();
        $otherConnection = 'other-' . uniqid();

        $this->connectionManager->addConnection($targetConnection, $targetUser->id);
        $this->connectionManager->addConnection($otherConnection, $otherUser->id);

        // When: 특정 사용자에게만 알림 발송
        $notificationData = [
            'title' => '개인 알림',
            'message' => '귀하의 작업이 완료되었습니다.',
            'action_url' => '/tasks/123'
        ];

        $this->notificationManager->sendToUser($targetUser->id, $notificationData);

        // Then: 대상 사용자에게만 알림이 전송됨
        $targetMessages = Redis::lrange("sse:messages:{$targetConnection}", 0, -1);
        $this->assertNotEmpty($targetMessages);

        $targetMessage = json_decode(end($targetMessages), true);
        $this->assertEquals('notification', $targetMessage['type']);
        $this->assertEquals('개인 알림', $targetMessage['data']['title']);

        // 다른 사용자에게는 전송되지 않음
        $otherMessages = Redis::lrange("sse:messages:{$otherConnection}", 0, -1);
        $this->assertEmpty($otherMessages);
    }

    /**
     * @test
     * 오프라인 사용자 알림 저장 및 전달 E2E 테스트
     */
    public function 오프라인_사용자_알림_저장_및_전달_e2e_테스트()
    {
        // Given: 사용자가 오프라인 상태
        $offlineUser = User::factory()->create(['name' => '오프라인 사용자']);

        // When: 오프라인 사용자에게 알림 발송
        $notificationData = [
            'title' => '오프라인 알림',
            'message' => '로그인 후 확인해주세요.',
            'priority' => 'normal'
        ];

        $this->notificationManager->sendToUser($offlineUser->id, $notificationData);

        // Then: 알림이 데이터베이스에 저장됨
        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $offlineUser->id,
            'type' => 'notification',
            'delivered_at' => null
        ]);

        // 사용자가 로그인하면 저장된 알림이 전달됨
        $connectionId = 'offline-user-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $offlineUser->id);

        // 오프라인 알림 전달 시뮬레이션
        $this->notificationManager->deliverOfflineNotifications($offlineUser->id);

        // 알림이 전달되고 delivered_at이 업데이트됨
        $this->assertDatabaseMissing('sse_offline_notifications', [
            'user_id' => $offlineUser->id,
            'delivered_at' => null
        ]);
    }

    /**
     * @test
     * 다중 디바이스 연결 E2E 테스트
     */
    public function 다중_디바이스_연결_e2e_테스트()
    {
        // Given: 한 사용자가 여러 디바이스로 연결
        $user = User::factory()->create(['name' => '다중 디바이스 사용자']);

        $webConnection = 'web-' . uniqid();
        $mobileConnection = 'mobile-' . uniqid();
        $desktopConnection = 'desktop-' . uniqid();

        $this->connectionManager->addConnection($webConnection, $user->id);
        $this->connectionManager->addConnection($mobileConnection, $user->id);
        $this->connectionManager->addConnection($desktopConnection, $user->id);

        // When: 해당 사용자에게 알림 발송
        $notificationData = [
            'title' => '다중 디바이스 알림',
            'message' => '모든 디바이스에서 확인 가능합니다.'
        ];

        $this->notificationManager->sendToUser($user->id, $notificationData);

        // Then: 모든 디바이스 연결에 알림이 전송됨
        $connections = [$webConnection, $mobileConnection, $desktopConnection];
        
        foreach ($connections as $connectionId) {
            $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
            $this->assertNotEmpty($messages, "연결 {$connectionId}에 메시지가 전송되지 않음");

            $lastMessage = json_decode(end($messages), true);
            $this->assertEquals('notification', $lastMessage['type']);
            $this->assertEquals('다중 디바이스 알림', $lastMessage['data']['title']);
        }

        // 사용자 연결 목록 확인
        $userConnections = $this->connectionManager->getUserConnections($user->id);
        $this->assertCount(3, $userConnections);
        $this->assertContains($webConnection, $userConnections);
        $this->assertContains($mobileConnection, $userConnections);
        $this->assertContains($desktopConnection, $userConnections);
    }

    /**
     * @test
     * 연결 해제 및 정리 E2E 테스트
     */
    public function 연결_해제_및_정리_e2e_테스트()
    {
        // Given: 사용자가 연결된 상태
        $user = User::factory()->create(['name' => '연결 해제 테스트 사용자']);
        $connectionId = 'disconnect-test-' . uniqid();

        $this->connectionManager->addConnection($connectionId, $user->id);
        $this->assertTrue($this->connectionManager->isUserOnline($user->id));

        // When: 연결 해제
        $this->connectionManager->removeConnection($connectionId);

        // Then: 연결이 정리됨
        $this->assertFalse($this->connectionManager->isUserOnline($user->id));
        
        // Redis에서 연결 정보가 제거됨
        $this->assertNull(Redis::get("sse:connections:{$connectionId}"));
        
        // 사용자 연결 목록에서도 제거됨
        $userConnections = $this->connectionManager->getUserConnections($user->id);
        $this->assertEmpty($userConnections);
    }

    /**
     * @test
     * 대량 연결 및 메시지 전송 성능 테스트
     */
    public function 대량_연결_및_메시지_전송_성능_테스트()
    {
        // Given: 100개의 동시 연결 시뮬레이션
        $connections = [];
        $users = User::factory()->count(50)->create();

        // 사용자당 2개씩 연결 (총 100개)
        foreach ($users as $user) {
            $conn1 = "perf-{$user->id}-1-" . uniqid();
            $conn2 = "perf-{$user->id}-2-" . uniqid();
            
            $this->connectionManager->addConnection($conn1, $user->id);
            $this->connectionManager->addConnection($conn2, $user->id);
            
            $connections[] = $conn1;
            $connections[] = $conn2;
        }

        // When: 전체 브로드캐스트 메시지 전송
        $startTime = microtime(true);
        
        $notificationData = [
            'title' => '성능 테스트 알림',
            'message' => '100개 연결 대상 브로드캐스트 테스트'
        ];

        $this->notificationManager->sendToAll($notificationData);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Then: 성능 기준 확인 (1초 이내 완료)
        $this->assertLessThan(1.0, $executionTime, "대량 메시지 전송이 1초를 초과함: {$executionTime}초");

        // 모든 연결에 메시지가 전송되었는지 확인 (샘플링)
        $sampleConnections = array_slice($connections, 0, 10);
        foreach ($sampleConnections as $connectionId) {
            $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
            $this->assertNotEmpty($messages, "연결 {$connectionId}에 메시지가 전송되지 않음");
        }

        echo "\n성능 테스트 결과: {$executionTime}초 (100개 연결)\n";
    }

    /**
     * @test
     * 이벤트 기반 카테고리 업데이트 통합 테스트
     */
    public function 이벤트_기반_카테고리_업데이트_통합_테스트()
    {
        // Given: 사용자가 연결된 상태
        $this->actingAs($this->testUser);
        $connectionId = 'event-test-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);

        // When: 카테고리 생성 (Observer를 통한 자동 이벤트 발생)
        $cate4 = Cate4::create([
            'name' => '새 카테고리',
            'description' => '이벤트 테스트용'
        ]);

        // Then: 자동으로 데이터 업데이트 메시지가 전송됨
        // Observer가 작동하여 자동으로 브로드캐스트됨
        $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
        
        // 메시지가 전송되었는지 확인
        $this->assertNotEmpty($messages);
        
        $lastMessage = json_decode(end($messages), true);
        $this->assertEquals('data_update', $lastMessage['type']);
        $this->assertEquals('categories', $lastMessage['data']['model']);
        $this->assertEquals('created', $lastMessage['data']['action']);

        // When: 카테고리 수정
        $cate4->update(['name' => '수정된 카테고리']);

        // Then: 업데이트 메시지가 추가로 전송됨
        $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
        $this->assertGreaterThanOrEqual(2, count($messages));

        $lastMessage = json_decode(end($messages), true);
        $this->assertEquals('data_update', $lastMessage['type']);
        $this->assertEquals('updated', $lastMessage['data']['action']);

        // When: 카테고리 삭제
        $cate4->delete();

        // Then: 삭제 메시지가 전송됨
        $messages = Redis::lrange("sse:messages:{$connectionId}", 0, -1);
        $this->assertGreaterThanOrEqual(3, count($messages));

        $lastMessage = json_decode(end($messages), true);
        $this->assertEquals('data_update', $lastMessage['type']);
        $this->assertEquals('deleted', $lastMessage['data']['action']);
    }

    /**
     * @test
     * 하트비트 및 연결 유지 테스트
     */
    public function 하트비트_및_연결_유지_테스트()
    {
        // Given: 사용자가 연결된 상태
        $this->actingAs($this->testUser);
        $connectionId = 'heartbeat-test-' . uniqid();
        $this->connectionManager->addConnection($connectionId, $this->testUser->id);

        // When: 하트비트 메시지 전송
        $heartbeatData = [
            'type' => 'heartbeat',
            'timestamp' => now()->toISOString()
        ];

        // 하트비트 전송 시뮬레이션
        Redis::lpush("sse:messages:{$connectionId}", json_encode($heartbeatData));

        // Then: 연결이 활성 상태로 유지됨
        $this->assertTrue($this->connectionManager->isUserOnline($this->testUser->id));

        // 연결 정보에 마지막 하트비트 시간이 업데이트됨
        $connectionInfo = json_decode(Redis::get("sse:connections:{$connectionId}"), true);
        $this->assertNotNull($connectionInfo);
        $this->assertArrayHasKey('last_heartbeat', $connectionInfo);
    }
}