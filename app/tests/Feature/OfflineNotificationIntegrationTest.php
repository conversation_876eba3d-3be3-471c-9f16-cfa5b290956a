<?php

namespace Tests\Feature;

use App\Events\ReqFinishNotification;
use App\Events\ReqStartNotification;
use App\Listeners\DeliverOfflineNotifications;
use App\Models\SseOfflineNotification;
use App\Models\User;
use App\Services\ConnectionManager;
use App\Services\MessageFormatterService;
use App\Services\NotificationManager;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Carbon\Carbon;

/**
 * 오프라인 알림 시스템 통합 테스트
 * 
 * 실제 시나리오를 기반으로 한 전체 플로우 테스트
 */
class OfflineNotificationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private NotificationManager $notificationManager;

    protected function setUp(): void
    {
        parent::setUp();

        // 테스트용 사용자 생성
        $this->user = User::factory()->create();

        // 실제 서비스 인스턴스 사용
        $this->notificationManager = app(NotificationManager::class);

        // Redis 모킹
        Redis::shouldReceive('publish')->andReturn(1)->byDefault();
    }

    /**
     * 전체 오프라인 알림 플로우 통합 테스트
     * 
     * 시나리오:
     * 1. 사용자가 오프라인 상태에서 알림 발생
     * 2. 알림이 데이터베이스에 저장됨
     * 3. 사용자 로그인 시 저장된 알림이 전송됨
     * 4. 전송 완료 후 알림 상태가 업데이트됨
     */
    public function test_complete_offline_notification_flow(): void
    {
        // 1. 사용자가 오프라인 상태에서 알림 전송
        $notificationData = [
            'title' => '중요한 알림',
            'message' => '시스템 업데이트가 완료되었습니다',
            'priority' => 'high',
            'action_url' => '/dashboard'
        ];

        // ConnectionManager를 모킹하여 사용자가 오프라인 상태로 설정
        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn([]);
        
        $messageFormatter = app(MessageFormatterService::class);
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        // 오프라인 사용자에게 알림 전송
        $result = $notificationManager->sendToUser($this->user->id, $notificationData, 'notification');

        // 2. 알림이 데이터베이스에 저장되었는지 확인
        $this->assertFalse($result['delivered_online']);
        $this->assertTrue($result['stored_offline']);

        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $this->user->id,
            'type' => 'notification',
            'delivered_at' => null
        ]);

        $storedNotification = SseOfflineNotification::where('user_id', $this->user->id)->first();
        $this->assertEquals($notificationData, $storedNotification->data);

        // 3. 사용자 로그인 시 알림 전송
        // ConnectionManager를 다시 모킹하여 사용자가 온라인 상태로 설정
        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn(['conn_1', 'conn_2']);
        
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        // 로그인 시 오프라인 알림 전송
        $deliveryResult = $notificationManager->deliverOfflineNotifications($this->user->id);

        // 4. 전송 결과 확인
        $this->assertEquals(1, $deliveryResult['total_notifications']);
        $this->assertEquals(1, $deliveryResult['delivered_count']);
        $this->assertEquals(0, $deliveryResult['failed_count']);

        // 5. 알림 상태가 전달 완료로 업데이트되었는지 확인
        $storedNotification->refresh();
        $this->assertNotNull($storedNotification->delivered_at);
        $this->assertTrue($storedNotification->isDelivered());
    }

    /**
     * 로그인 이벤트 리스너 통합 테스트
     */
    public function test_login_event_triggers_offline_notification_delivery(): void
    {
        // 큐 이벤트 감시
        Queue::fake();
        Event::fake([Login::class]);

        // 오프라인 알림 생성
        SseOfflineNotification::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 로그인 이벤트 발생
        $loginEvent = new Login('web', $this->user, false);
        event($loginEvent);

        // 이벤트가 발생했는지 확인
        Event::assertDispatched(Login::class, function ($event) {
            return $event->user->id === $this->user->id;
        });
    }

    /**
     * 다중 디바이스 로그인 시나리오 테스트
     */
    public function test_multiple_device_login_scenario(): void
    {
        // 오프라인 알림들 생성
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 다중 디바이스 연결 시뮬레이션
        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn([
            'desktop_conn_1',
            'mobile_conn_1',
            'tablet_conn_1'
        ]);

        $messageFormatter = app(MessageFormatterService::class);
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        // 오프라인 알림 전송
        $result = $notificationManager->deliverOfflineNotifications($this->user->id);

        // 모든 디바이스에 전송되었는지 확인
        $this->assertEquals(2, $result['total_notifications']);
        $this->assertEquals(2, $result['delivered_count']);

        // 알림들이 전달 완료로 표시되었는지 확인
        $undeliveredCount = SseOfflineNotification::forUser($this->user->id)
            ->undelivered()
            ->count();
        $this->assertEquals(0, $undeliveredCount);
    }

    /**
     * 대량 오프라인 알림 처리 성능 테스트
     */
    public function test_handles_large_number_of_offline_notifications(): void
    {
        // 대량의 오프라인 알림 생성
        SseOfflineNotification::factory()->count(50)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn(['conn_1']);

        $messageFormatter = app(MessageFormatterService::class);
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        // 실행 시간 측정
        $startTime = microtime(true);
        $result = $notificationManager->deliverOfflineNotifications($this->user->id);
        $endTime = microtime(true);

        // 성능 검증 (5초 이내)
        $executionTime = $endTime - $startTime;
        $this->assertLessThan(5, $executionTime, '대량 알림 처리가 너무 오래 걸립니다');

        // 결과 검증
        $this->assertEquals(50, $result['total_notifications']);
        $this->assertEquals(50, $result['delivered_count']);
    }

    /**
     * 알림 타입별 처리 테스트
     */
    public function test_handles_different_notification_types(): void
    {
        // 다양한 타입의 알림 생성
        $notificationTypes = ['notification', 'data_update', 'system_status'];
        
        foreach ($notificationTypes as $type) {
            SseOfflineNotification::factory()->ofType($type)->create([
                'user_id' => $this->user->id,
                'delivered_at' => null
            ]);
        }

        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn(['conn_1']);

        $messageFormatter = app(MessageFormatterService::class);
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        // 모든 타입의 알림 전송
        $result = $notificationManager->deliverOfflineNotifications($this->user->id);

        // 결과 검증
        $this->assertEquals(3, $result['total_notifications']);
        $this->assertEquals(3, $result['delivered_count']);

        // 모든 알림이 전달 완료로 표시되었는지 확인
        $deliveredNotifications = SseOfflineNotification::forUser($this->user->id)
            ->whereNotNull('delivered_at')
            ->get();
        
        $this->assertCount(3, $deliveredNotifications);
        
        // 각 타입별로 하나씩 있는지 확인
        $deliveredTypes = $deliveredNotifications->pluck('type')->toArray();
        $this->assertEquals($notificationTypes, array_values(array_unique($deliveredTypes)));
    }

    /**
     * 연결 실패 시 오프라인 저장 테스트
     */
    public function test_stores_notification_when_connection_fails(): void
    {
        // Redis 연결 실패 시뮬레이션
        Redis::shouldReceive('publish')->andThrow(new \Exception('Redis connection failed'));

        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn(['conn_1']);

        $messageFormatter = app(MessageFormatterService::class);
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        $notificationData = [
            'title' => '연결 실패 테스트',
            'message' => '이 알림은 연결 실패로 인해 저장되어야 합니다'
        ];

        // 알림 전송 시도
        $result = $notificationManager->sendToUser($this->user->id, $notificationData, 'notification');

        // 전송 실패로 인한 오프라인 저장 확인
        $this->assertEquals(1, $result['failed_sends']);
    }

    /**
     * 사용자별 알림 수 제한 통합 테스트
     */
    public function test_enforces_notification_limit_per_user(): void
    {
        // 최대 허용 수에 근접한 알림 생성 (999개)
        SseOfflineNotification::factory()->count(999)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        $connectionManager = $this->createMock(ConnectionManager::class);
        $connectionManager->method('getUserConnections')->willReturn([]);

        $messageFormatter = app(MessageFormatterService::class);
        $notificationManager = new NotificationManager($connectionManager, $messageFormatter);

        // 새 알림 2개 추가 (제한 초과)
        for ($i = 0; $i < 2; $i++) {
            $notificationManager->sendToUser($this->user->id, [
                'title' => "알림 {$i}",
                'message' => '제한 테스트용 알림'
            ], 'notification');
        }

        // 여전히 1000개만 유지되는지 확인
        $totalCount = SseOfflineNotification::forUser($this->user->id)->count();
        $this->assertEquals(1000, $totalCount);
    }

    /**
     * 스케줄러를 통한 자동 정리 테스트
     */
    public function test_scheduled_cleanup_integration(): void
    {
        // 정리 대상 알림들 생성
        SseOfflineNotification::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(10)
        ]);

        SseOfflineNotification::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(35)
        ]);

        // 정리되지 않을 알림들
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(3)
        ]);

        // 정리 명령어 실행
        $this->artisan('sse:cleanup-notifications')
            ->assertExitCode(0);

        // 정리 결과 확인
        $remainingCount = SseOfflineNotification::forUser($this->user->id)->count();
        $this->assertEquals(2, $remainingCount);

        // 남은 알림들이 최근 것들인지 확인
        $remainingNotifications = SseOfflineNotification::forUser($this->user->id)->get();
        foreach ($remainingNotifications as $notification) {
            $this->assertNotNull($notification->delivered_at);
            $this->assertTrue($notification->delivered_at->greaterThan(Carbon::now()->subDays(5)));
        }
    }

    /**
     * User 모델 관계 통합 테스트
     */
    public function test_user_model_relationships(): void
    {
        // 사용자의 오프라인 알림들 생성
        $deliveredNotification = SseOfflineNotification::factory()->delivered()->create([
            'user_id' => $this->user->id
        ]);

        $undeliveredNotification = SseOfflineNotification::factory()->undelivered()->create([
            'user_id' => $this->user->id
        ]);

        // User 모델을 통한 관계 조회 테스트
        $allNotifications = $this->user->sseOfflineNotifications;
        $undeliveredNotifications = $this->user->undeliveredSseNotifications;

        // 결과 검증
        $this->assertCount(2, $allNotifications);
        $this->assertCount(1, $undeliveredNotifications);
        $this->assertEquals($undeliveredNotification->id, $undeliveredNotifications->first()->id);

        // 역방향 관계 테스트
        $this->assertEquals($this->user->id, $deliveredNotification->user->id);
        $this->assertEquals($this->user->id, $undeliveredNotification->user->id);
    }
}