<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\ConnectionManager;
use App\Services\NotificationManager;
use App\Services\PerformanceOptimizer;
use App\Services\SseMonitoringService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Carbon\Carbon;

/**
 * SSE 성능 및 안정성 검증 테스트
 * 
 * 장시간 연결 유지, 메모리 사용량, 대용량 데이터 전송 등을 테스트합니다.
 */
class SsePerformanceTest extends TestCase
{
    use RefreshDatabase;

    private ConnectionManager $connectionManager;
    private NotificationManager $notificationManager;
    private PerformanceOptimizer $performanceOptimizer;
    private SseMonitoringService $monitoringService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->connectionManager = app(ConnectionManager::class);
        $this->notificationManager = app(NotificationManager::class);
        $this->performanceOptimizer = app(PerformanceOptimizer::class);
        $this->monitoringService = app(SseMonitoringService::class);

        Redis::flushdb();
    }

    protected function tearDown(): void
    {
        Redis::flushdb();
        parent::tearDown();
    }

    /**
     * @test
     * 장시간 연결 유지 및 메모리 사용량 모니터링 테스트
     */
    public function 장시간_연결_유지_및_메모리_사용량_모니터링_테스트()
    {
        // Given: 다수의 사용자 연결 설정
        $userCount = 50;
        $users = User::factory()->count($userCount)->create();
        $connections = [];
        $initialMemory = memory_get_usage(true);

        foreach ($users as $user) {
            $connectionId = "long-term-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[] = $connectionId;
        }

        // When: 장시간 연결 유지 시뮬레이션 (실제로는 짧은 시간으로 테스트)
        $testDuration = 30; // 30초 (실제 환경에서는 더 길게)
        $memorySnapshots = [];
        $startTime = time();

        while (time() - $startTime < $testDuration) {
            // 주기적으로 메시지 전송
            $this->notificationManager->sendToAll([
                'title' => '장시간 테스트',
                'message' => '연결 유지 테스트 메시지',
                'timestamp' => now()->toISOString()
            ]);

            // 메모리 사용량 기록
            $currentMemory = memory_get_usage(true);
            $memorySnapshots[] = [
                'time' => time() - $startTime,
                'memory' => $currentMemory,
                'connections' => count($this->connectionManager->getActiveConnections())
            ];

            sleep(5); // 5초 간격
        }

        $finalMemory = memory_get_usage(true);
        $memoryIncrease = $finalMemory - $initialMemory;

        // Then: 메모리 사용량이 합리적인 범위 내에 있어야 함
        $this->assertLessThan(50 * 1024 * 1024, $memoryIncrease, '메모리 증가량이 50MB를 초과함'); // 50MB 제한

        // 모든 연결이 여전히 활성 상태여야 함
        $activeConnections = $this->connectionManager->getActiveConnections();
        $this->assertCount($userCount, $activeConnections);

        // 메모리 누수 검사 (메모리 사용량이 지속적으로 증가하지 않아야 함)
        $memoryTrend = $this->analyzeMemoryTrend($memorySnapshots);
        $this->assertLessThan(1024 * 1024, $memoryTrend, '메모리 누수 감지: 지속적인 메모리 증가'); // 1MB/분 이하

        echo "\n장시간 연결 테스트 결과:\n";
        echo "- 테스트 시간: {$testDuration}초\n";
        echo "- 연결 수: {$userCount}개\n";
        echo "- 초기 메모리: " . $this->formatBytes($initialMemory) . "\n";
        echo "- 최종 메모리: " . $this->formatBytes($finalMemory) . "\n";
        echo "- 메모리 증가: " . $this->formatBytes($memoryIncrease) . "\n";
    }

    /**
     * @test
     * 대용량 데이터 전송 성능 및 안정성 테스트
     */
    public function 대용량_데이터_전송_성능_및_안정성_테스트()
    {
        // Given: 연결된 사용자들
        $users = User::factory()->count(20)->create();
        $connections = [];

        foreach ($users as $user) {
            $connectionId = "large-data-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[] = $connectionId;
        }

        // When: 대용량 데이터 전송
        $largeDataSizes = [
            '1KB' => 1024,
            '10KB' => 10 * 1024,
            '100KB' => 100 * 1024,
            '1MB' => 1024 * 1024
        ];

        $performanceResults = [];

        foreach ($largeDataSizes as $sizeLabel => $sizeBytes) {
            $largeData = [
                'title' => "대용량 데이터 테스트 ({$sizeLabel})",
                'message' => '대용량 데이터 전송 테스트입니다.',
                'payload' => str_repeat('A', $sizeBytes - 200) // 헤더 공간 고려
            ];

            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);

            try {
                $this->notificationManager->sendToAll($largeData);
                $success = true;
            } catch (\Exception $e) {
                $success = false;
                $error = $e->getMessage();
            }

            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);

            $performanceResults[$sizeLabel] = [
                'size_bytes' => $sizeBytes,
                'success' => $success,
                'duration' => $endTime - $startTime,
                'memory_used' => $endMemory - $startMemory,
                'error' => $error ?? null
            ];

            // 메모리 정리
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            sleep(1); // 서버 부하 방지
        }

        // Then: 성능 기준 검증
        foreach ($performanceResults as $sizeLabel => $result) {
            $this->assertTrue($result['success'], "대용량 데이터 전송 실패 ({$sizeLabel}): " . ($result['error'] ?? ''));
            
            // 전송 시간 기준 (크기에 따라 다르게 적용)
            $maxDuration = $result['size_bytes'] < 100 * 1024 ? 1.0 : 5.0; // 100KB 미만: 1초, 이상: 5초
            $this->assertLessThan($maxDuration, $result['duration'], 
                "대용량 데이터 전송 시간 초과 ({$sizeLabel}): {$result['duration']}초");
        }

        // 결과 출력
        echo "\n대용량 데이터 전송 테스트 결과:\n";
        foreach ($performanceResults as $sizeLabel => $result) {
            echo "- {$sizeLabel}: " . 
                 ($result['success'] ? '성공' : '실패') . 
                 ", 시간: " . round($result['duration'], 3) . "초" .
                 ", 메모리: " . $this->formatBytes($result['memory_used']) . "\n";
        }
    }

    /**
     * @test
     * 동시 다중 연결 부하 테스트
     */
    public function 동시_다중_연결_부하_테스트()
    {
        // Given: 대량 동시 연결 시뮬레이션
        $connectionCount = 200;
        $batchSize = 20; // 배치 단위로 연결 생성
        $connections = [];
        $users = User::factory()->count($connectionCount)->create();

        $startTime = microtime(true);
        $initialMemory = memory_get_usage(true);

        // When: 배치 단위로 연결 생성
        for ($batch = 0; $batch < $connectionCount / $batchSize; $batch++) {
            $batchStartTime = microtime(true);
            
            for ($i = 0; $i < $batchSize; $i++) {
                $userIndex = $batch * $batchSize + $i;
                if ($userIndex >= $connectionCount) break;
                
                $user = $users[$userIndex];
                $connectionId = "load-test-{$user->id}-" . uniqid();
                
                try {
                    $this->connectionManager->addConnection($connectionId, $user->id);
                    $connections[] = $connectionId;
                } catch (\Exception $e) {
                    echo "연결 생성 실패 (사용자 {$user->id}): " . $e->getMessage() . "\n";
                }
            }
            
            $batchEndTime = microtime(true);
            $batchDuration = $batchEndTime - $batchStartTime;
            
            echo "배치 " . ($batch + 1) . " 완료: {$batchSize}개 연결, {$batchDuration}초\n";
            
            // 배치 간 짧은 지연
            usleep(100000); // 0.1초
        }

        $connectionCreationTime = microtime(true) - $startTime;
        $actualConnectionCount = count($connections);

        // 전체 브로드캐스트 테스트
        $broadcastStartTime = microtime(true);
        
        $this->notificationManager->sendToAll([
            'title' => '부하 테스트 브로드캐스트',
            'message' => "{$actualConnectionCount}개 연결 대상 메시지",
            'timestamp' => now()->toISOString()
        ]);
        
        $broadcastEndTime = microtime(true);
        $broadcastDuration = $broadcastEndTime - $broadcastStartTime;

        $finalMemory = memory_get_usage(true);
        $memoryUsed = $finalMemory - $initialMemory;

        // Then: 성능 기준 검증
        $this->assertGreaterThan($connectionCount * 0.9, $actualConnectionCount, 
            '연결 생성 성공률이 90% 미만');
        
        $this->assertLessThan(30.0, $connectionCreationTime, 
            '연결 생성 시간이 30초 초과');
        
        $this->assertLessThan(5.0, $broadcastDuration, 
            '브로드캐스트 시간이 5초 초과');
        
        $this->assertLessThan(100 * 1024 * 1024, $memoryUsed, 
            '메모리 사용량이 100MB 초과');

        // 결과 출력
        echo "\n동시 다중 연결 부하 테스트 결과:\n";
        echo "- 목표 연결 수: {$connectionCount}개\n";
        echo "- 실제 연결 수: {$actualConnectionCount}개\n";
        echo "- 연결 생성 시간: " . round($connectionCreationTime, 2) . "초\n";
        echo "- 브로드캐스트 시간: " . round($broadcastDuration, 3) . "초\n";
        echo "- 메모리 사용량: " . $this->formatBytes($memoryUsed) . "\n";
        echo "- 연결 성공률: " . round(($actualConnectionCount / $connectionCount) * 100, 1) . "%\n";
    }

    /**
     * @test
     * 메시지 처리량 성능 테스트
     */
    public function 메시지_처리량_성능_테스트()
    {
        // Given: 연결된 사용자들
        $users = User::factory()->count(30)->create();
        $connections = [];

        foreach ($users as $user) {
            $connectionId = "throughput-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[] = $connectionId;
        }

        // When: 다양한 메시지 처리량 테스트
        $throughputTests = [
            '저부하' => ['messages' => 50, 'interval' => 100], // 50개 메시지, 100ms 간격
            '중부하' => ['messages' => 100, 'interval' => 50],  // 100개 메시지, 50ms 간격
            '고부하' => ['messages' => 200, 'interval' => 25],  // 200개 메시지, 25ms 간격
        ];

        $throughputResults = [];

        foreach ($throughputTests as $testName => $config) {
            $messageCount = $config['messages'];
            $interval = $config['interval']; // 밀리초

            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);
            $successCount = 0;
            $errorCount = 0;

            for ($i = 1; $i <= $messageCount; $i++) {
                try {
                    $this->notificationManager->sendToAll([
                        'title' => "{$testName} 테스트 {$i}",
                        'message' => "처리량 테스트 메시지 번호 {$i}",
                        'sequence' => $i,
                        'test_name' => $testName
                    ]);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    echo "메시지 전송 실패 ({$testName} #{$i}): " . $e->getMessage() . "\n";
                }

                // 간격 조절
                if ($interval > 0 && $i < $messageCount) {
                    usleep($interval * 1000); // 밀리초를 마이크로초로 변환
                }
            }

            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);

            $totalTime = $endTime - $startTime;
            $throughput = $successCount / $totalTime; // 메시지/초
            $memoryUsed = $endMemory - $startMemory;

            $throughputResults[$testName] = [
                'messages_sent' => $messageCount,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'total_time' => $totalTime,
                'throughput' => $throughput,
                'memory_used' => $memoryUsed,
                'success_rate' => ($successCount / $messageCount) * 100
            ];

            // 테스트 간 정리 시간
            sleep(2);
        }

        // Then: 처리량 기준 검증
        foreach ($throughputResults as $testName => $result) {
            $this->assertGreaterThan(95, $result['success_rate'], 
                "{$testName} 테스트 성공률이 95% 미만: {$result['success_rate']}%");
            
            // 최소 처리량 기준 (테스트 부하에 따라 다르게 적용)
            $minThroughput = $testName === '고부하' ? 50 : 100; // 고부하 시 50 메시지/초, 나머지는 100 메시지/초
            $this->assertGreaterThan($minThroughput, $result['throughput'], 
                "{$testName} 테스트 처리량 부족: {$result['throughput']} 메시지/초");
        }

        // 결과 출력
        echo "\n메시지 처리량 성능 테스트 결과:\n";
        foreach ($throughputResults as $testName => $result) {
            echo "- {$testName}:\n";
            echo "  * 전송: {$result['messages_sent']}개\n";
            echo "  * 성공: {$result['success_count']}개\n";
            echo "  * 실패: {$result['error_count']}개\n";
            echo "  * 처리량: " . round($result['throughput'], 1) . " 메시지/초\n";
            echo "  * 성공률: " . round($result['success_rate'], 1) . "%\n";
            echo "  * 메모리: " . $this->formatBytes($result['memory_used']) . "\n";
        }
    }

    /**
     * @test
     * 시스템 리소스 모니터링 테스트
     */
    public function 시스템_리소스_모니터링_테스트()
    {
        // Given: 모니터링 시스템 설정
        $monitoringDuration = 20; // 20초간 모니터링
        $sampleInterval = 2; // 2초 간격으로 샘플링
        
        $users = User::factory()->count(40)->create();
        $connections = [];

        foreach ($users as $user) {
            $connectionId = "monitoring-{$user->id}-" . uniqid();
            $this->connectionManager->addConnection($connectionId, $user->id);
            $connections[] = $connectionId;
        }

        // When: 시스템 리소스 모니터링 시작
        $resourceSnapshots = [];
        $startTime = time();

        while (time() - $startTime < $monitoringDuration) {
            // 시스템 리소스 수집
            $snapshot = [
                'timestamp' => time() - $startTime,
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'active_connections' => count($this->connectionManager->getActiveConnections()),
                'redis_info' => $this->getRedisInfo(),
                'system_load' => $this->getSystemLoad()
            ];

            $resourceSnapshots[] = $snapshot;

            // 부하 생성 (메시지 전송)
            if ((time() - $startTime) % 5 === 0) { // 5초마다
                $this->notificationManager->sendToAll([
                    'title' => '모니터링 테스트',
                    'message' => '시스템 리소스 모니터링 중',
                    'timestamp' => now()->toISOString()
                ]);
            }

            sleep($sampleInterval);
        }

        // Then: 리소스 사용량 분석
        $memoryTrend = $this->analyzeResourceTrend($resourceSnapshots, 'memory_usage');
        $connectionStability = $this->analyzeConnectionStability($resourceSnapshots);
        
        // 메모리 사용량이 안정적이어야 함 (급격한 증가 없음)
        $this->assertLessThan(2 * 1024 * 1024, $memoryTrend, 
            '메모리 사용량이 급격히 증가함: ' . $this->formatBytes($memoryTrend) . '/분');
        
        // 연결 수가 안정적이어야 함
        $this->assertGreaterThan(95, $connectionStability, 
            '연결 안정성이 95% 미만: ' . round($connectionStability, 1) . '%');

        // 결과 출력
        echo "\n시스템 리소스 모니터링 테스트 결과:\n";
        echo "- 모니터링 시간: {$monitoringDuration}초\n";
        echo "- 샘플 수: " . count($resourceSnapshots) . "개\n";
        echo "- 메모리 트렌드: " . $this->formatBytes($memoryTrend) . "/분\n";
        echo "- 연결 안정성: " . round($connectionStability, 1) . "%\n";
        
        $finalSnapshot = end($resourceSnapshots);
        echo "- 최종 메모리: " . $this->formatBytes($finalSnapshot['memory_usage']) . "\n";
        echo "- 최대 메모리: " . $this->formatBytes($finalSnapshot['memory_peak']) . "\n";
        echo "- 활성 연결: {$finalSnapshot['active_connections']}개\n";
    }

    /**
     * @test
     * 다양한 OS 환경 호환성 테스트 (시뮬레이션)
     */
    public function 다양한_os_환경_호환성_테스트()
    {
        // Given: 다양한 클라이언트 환경 시뮬레이션
        $clientEnvironments = [
            'Windows Chrome' => ['os' => 'Windows', 'browser' => 'Chrome'],
            'macOS Safari' => ['os' => 'macOS', 'browser' => 'Safari'],
            'Linux Firefox' => ['os' => 'Linux', 'browser' => 'Firefox'],
            'Android Chrome' => ['os' => 'Android', 'browser' => 'Chrome'],
            'iOS Safari' => ['os' => 'iOS', 'browser' => 'Safari']
        ];

        $compatibilityResults = [];

        foreach ($clientEnvironments as $envName => $envInfo) {
            // When: 각 환경별 연결 및 메시지 수신 테스트
            $user = User::factory()->create(['name' => "사용자 ({$envName})"]);
            $connectionId = "compat-{$envInfo['os']}-{$envInfo['browser']}-" . uniqid();

            try {
                // 연결 생성
                $this->connectionManager->addConnection($connectionId, $user->id);
                $connectionSuccess = true;

                // 메시지 전송 테스트
                $testMessage = [
                    'title' => "{$envName} 호환성 테스트",
                    'message' => "환경별 호환성 검증 메시지",
                    'client_info' => $envInfo
                ];

                $this->notificationManager->sendToUser($user->id, $testMessage);
                $messageSuccess = true;

                // 특수 문자 및 유니코드 테스트
                $unicodeMessage = [
                    'title' => "유니코드 테스트 🚀",
                    'message' => "한글, 이모지, 특수문자 테스트: áéíóú ñ 中文 日本語 🎉",
                    'special_chars' => "!@#$%^&*()_+-=[]{}|;':\",./<>?"
                ];

                $this->notificationManager->sendToUser($user->id, $unicodeMessage);
                $unicodeSuccess = true;

            } catch (\Exception $e) {
                $connectionSuccess = false;
                $messageSuccess = false;
                $unicodeSuccess = false;
                $error = $e->getMessage();
            }

            $compatibilityResults[$envName] = [
                'connection_success' => $connectionSuccess,
                'message_success' => $messageSuccess,
                'unicode_success' => $unicodeSuccess,
                'error' => $error ?? null
            ];

            // 연결 정리
            if ($connectionSuccess) {
                $this->connectionManager->removeConnection($connectionId);
            }
        }

        // Then: 모든 환경에서 호환성이 확보되어야 함
        foreach ($compatibilityResults as $envName => $result) {
            $this->assertTrue($result['connection_success'], 
                "{$envName} 환경에서 연결 실패: " . ($result['error'] ?? ''));
            
            $this->assertTrue($result['message_success'], 
                "{$envName} 환경에서 메시지 전송 실패");
            
            $this->assertTrue($result['unicode_success'], 
                "{$envName} 환경에서 유니코드 처리 실패");
        }

        // 결과 출력
        echo "\n다양한 OS 환경 호환성 테스트 결과:\n";
        foreach ($compatibilityResults as $envName => $result) {
            $status = ($result['connection_success'] && $result['message_success'] && $result['unicode_success']) 
                ? '✅ 호환' : '❌ 비호환';
            echo "- {$envName}: {$status}\n";
        }
    }

    /**
     * 메모리 트렌드 분석
     */
    private function analyzeMemoryTrend(array $snapshots): int
    {
        if (count($snapshots) < 2) return 0;

        $firstSnapshot = reset($snapshots);
        $lastSnapshot = end($snapshots);
        
        $memoryDiff = $lastSnapshot['memory'] - $firstSnapshot['memory'];
        $timeDiff = $lastSnapshot['time'] - $firstSnapshot['time'];
        
        // 분당 메모리 증가량 계산
        return $timeDiff > 0 ? ($memoryDiff / $timeDiff) * 60 : 0;
    }

    /**
     * 리소스 트렌드 분석
     */
    private function analyzeResourceTrend(array $snapshots, string $metric): float
    {
        if (count($snapshots) < 2) return 0;

        $values = array_column($snapshots, $metric);
        $times = array_column($snapshots, 'timestamp');
        
        // 선형 회귀를 사용한 트렌드 분석
        $n = count($values);
        $sumX = array_sum($times);
        $sumY = array_sum($values);
        $sumXY = 0;
        $sumX2 = 0;
        
        for ($i = 0; $i < $n; $i++) {
            $sumXY += $times[$i] * $values[$i];
            $sumX2 += $times[$i] * $times[$i];
        }
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        
        // 분당 변화량으로 변환
        return $slope * 60;
    }

    /**
     * 연결 안정성 분석
     */
    private function analyzeConnectionStability(array $snapshots): float
    {
        if (empty($snapshots)) return 0;

        $expectedConnections = $snapshots[0]['active_connections'];
        $stableCount = 0;
        
        foreach ($snapshots as $snapshot) {
            if ($snapshot['active_connections'] === $expectedConnections) {
                $stableCount++;
            }
        }
        
        return (count($snapshots) > 0) ? ($stableCount / count($snapshots)) * 100 : 0;
    }

    /**
     * Redis 정보 수집
     */
    private function getRedisInfo(): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();
            
            return [
                'connected_clients' => $info['connected_clients'] ?? 0,
                'used_memory' => $info['used_memory'] ?? 0,
                'used_memory_human' => $info['used_memory_human'] ?? '0B',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 시스템 로드 정보 수집
     */
    private function getSystemLoad(): array
    {
        $load = sys_getloadavg();
        
        return [
            '1min' => $load[0] ?? 0,
            '5min' => $load[1] ?? 0,
            '15min' => $load[2] ?? 0
        ];
    }

    /**
     * 바이트를 읽기 쉬운 형태로 변환
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}