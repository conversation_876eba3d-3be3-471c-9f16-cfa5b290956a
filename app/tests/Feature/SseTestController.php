<?php

namespace Tests\Feature;

use App\Http\Controllers\Controller;
use App\Models\Cate4;
use App\Services\NotificationManager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * SSE E2E 테스트를 위한 테스트 컨트롤러
 * 
 * JavaScript E2E 테스트에서 사용할 테스트용 엔드포인트를 제공합니다.
 * 프로덕션 환경에서는 사용하지 않습니다.
 */
class SseTestController extends Controller
{
    private NotificationManager $notificationManager;

    public function __construct(NotificationManager $notificationManager)
    {
        $this->notificationManager = $notificationManager;
    }

    /**
     * 테스트용 알림 전송
     */
    public function sendNotification(Request $request): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $data = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'sequence' => 'sometimes|integer',
            'sendTime' => 'sometimes|integer',
            'priority' => 'sometimes|string|in:low,normal,high,critical'
        ]);

        // 전체 사용자에게 알림 전송
        $this->notificationManager->sendToAll($data);

        return response()->json([
            'success' => true,
            'message' => '테스트 알림이 전송되었습니다.',
            'data' => $data
        ]);
    }

    /**
     * 테스트용 대용량 알림 전송
     */
    public function sendLargeNotification(Request $request): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $data = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'payload' => 'required|array'
        ]);

        // 대용량 데이터 포함한 알림 전송
        $this->notificationManager->sendToAll($data);

        return response()->json([
            'success' => true,
            'message' => '대용량 테스트 알림이 전송되었습니다.',
            'data_size' => strlen(json_encode($data))
        ]);
    }

    /**
     * 테스트용 카테고리 생성
     */
    public function createCategory(Request $request): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $data = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'sometimes|string'
        ]);

        // 카테고리 생성 (Observer를 통해 자동으로 SSE 브로드캐스트됨)
        $category = Cate4::create($data);

        return response()->json([
            'success' => true,
            'message' => '테스트 카테고리가 생성되었습니다.',
            'category' => $category
        ]);
    }

    /**
     * 테스트용 개별 사용자 알림 전송
     */
    public function sendUserNotification(Request $request): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $data = $request->validate([
            'user_id' => 'required|integer|exists:users,id',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'action_url' => 'sometimes|string'
        ]);

        $userId = $data['user_id'];
        unset($data['user_id']);

        // 특정 사용자에게 알림 전송
        $this->notificationManager->sendToUser($userId, $data);

        return response()->json([
            'success' => true,
            'message' => '개별 사용자 알림이 전송되었습니다.',
            'user_id' => $userId,
            'data' => $data
        ]);
    }

    /**
     * 테스트용 연결 상태 확인
     */
    public function getConnectionStatus(): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $connectionManager = app(\App\Services\ConnectionManager::class);
        $activeConnections = $connectionManager->getActiveConnections();

        return response()->json([
            'success' => true,
            'active_connections' => count($activeConnections),
            'connections' => $activeConnections
        ]);
    }

    /**
     * 테스트용 하트비트 전송
     */
    public function sendHeartbeat(): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $heartbeatData = [
            'type' => 'heartbeat',
            'timestamp' => now()->toISOString(),
            'server_time' => time()
        ];

        // 모든 연결에 하트비트 전송
        $this->notificationManager->sendToAll($heartbeatData, 'heartbeat');

        return response()->json([
            'success' => true,
            'message' => '하트비트가 전송되었습니다.',
            'data' => $heartbeatData
        ]);
    }

    /**
     * 테스트용 배치 메시지 전송
     */
    public function sendBatchMessages(Request $request): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $data = $request->validate([
            'count' => 'required|integer|min:1|max:1000',
            'delay' => 'sometimes|integer|min:0|max:5000', // ms
            'prefix' => 'sometimes|string|max:50'
        ]);

        $count = $data['count'];
        $delay = $data['delay'] ?? 0;
        $prefix = $data['prefix'] ?? '배치 테스트';

        $sentMessages = [];

        for ($i = 1; $i <= $count; $i++) {
            $messageData = [
                'title' => "{$prefix} {$i}",
                'message' => "배치 메시지 번호 {$i}",
                'sequence' => $i,
                'batch_id' => uniqid('batch_'),
                'timestamp' => now()->toISOString()
            ];

            $this->notificationManager->sendToAll($messageData);
            $sentMessages[] = $messageData;

            if ($delay > 0 && $i < $count) {
                usleep($delay * 1000); // ms를 μs로 변환
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$count}개의 배치 메시지가 전송되었습니다.",
            'sent_count' => count($sentMessages),
            'delay' => $delay
        ]);
    }

    /**
     * 테스트용 오류 시뮬레이션
     */
    public function simulateError(Request $request): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $data = $request->validate([
            'error_type' => 'required|string|in:redis,network,timeout,memory',
            'duration' => 'sometimes|integer|min:1|max:30' // 초
        ]);

        $errorType = $data['error_type'];
        $duration = $data['duration'] ?? 5;

        switch ($errorType) {
            case 'redis':
                // Redis 연결 오류 시뮬레이션
                throw new \Exception('Simulated Redis connection error');

            case 'network':
                // 네트워크 지연 시뮬레이션
                sleep($duration);
                break;

            case 'timeout':
                // 타임아웃 시뮬레이션
                set_time_limit($duration);
                sleep($duration + 1);
                break;

            case 'memory':
                // 메모리 부족 시뮬레이션
                $largeArray = array_fill(0, 1000000, str_repeat('x', 1000));
                break;
        }

        return response()->json([
            'success' => true,
            'message' => "{$errorType} 오류가 시뮬레이션되었습니다.",
            'error_type' => $errorType,
            'duration' => $duration
        ]);
    }

    /**
     * 테스트용 성능 메트릭 수집
     */
    public function getPerformanceMetrics(): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        $connectionManager = app(\App\Services\ConnectionManager::class);
        
        $metrics = [
            'timestamp' => now()->toISOString(),
            'active_connections' => count($connectionManager->getActiveConnections()),
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'server_load' => sys_getloadavg(),
            'redis_info' => $this->getRedisInfo()
        ];

        return response()->json([
            'success' => true,
            'metrics' => $metrics
        ]);
    }

    /**
     * Redis 정보 수집
     */
    private function getRedisInfo(): array
    {
        try {
            $redis = app('redis');
            $info = $redis->info();
            
            return [
                'connected_clients' => $info['connected_clients'] ?? 0,
                'used_memory' => $info['used_memory'] ?? 0,
                'used_memory_human' => $info['used_memory_human'] ?? '0B',
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Redis 정보를 가져올 수 없습니다: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 테스트 환경 초기화
     */
    public function resetTestEnvironment(): JsonResponse
    {
        if (!app()->environment(['testing', 'local'])) {
            abort(404);
        }

        try {
            // Redis 테스트 데이터 정리
            $redis = app('redis');
            $keys = $redis->keys('sse:test:*');
            if (!empty($keys)) {
                $redis->del($keys);
            }

            // 테스트용 카테고리 정리
            Cate4::where('name', 'like', '%테스트%')->delete();
            Cate4::where('name', 'like', '%E2E%')->delete();

            return response()->json([
                'success' => true,
                'message' => '테스트 환경이 초기화되었습니다.',
                'cleaned_keys' => count($keys ?? [])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '테스트 환경 초기화 실패: ' . $e->getMessage()
            ], 500);
        }
    }
}