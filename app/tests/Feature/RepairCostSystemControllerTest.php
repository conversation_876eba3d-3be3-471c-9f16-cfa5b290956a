<?php

use App\Models\RepairCostPolicy;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 테스트용 사용자 생성 및 인증
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    // 테스트용 데이터 생성
    RepairCostPolicy::create([
        'name' => 'monitor',
        'display_name' => '모니터',
        'description' => '모니터 제품의 크기별 수리비 관리',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_SIZE,
        'is_active' => true
    ]);

    RepairCostPolicy::create([
        'name' => 'apple',
        'display_name' => '애플',
        'description' => '애플 제품의 모델별 수리비 관리',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_MODEL,
        'is_active' => true
    ]);
});

test('수리비 정책 목록을 조회할 수 있다', function () {
    $response = $this->getJson('/wms/settings/repairs/cost-systems');

    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'data' => [
                     'systems' => [
                         '*' => [
                             'id',
                             'name',
                             'display_name',
                             'description',
                             'pricing_type',
                             'is_active',
                             'categories_count',
                             'active_categories_count'
                         ]
                     ],
                     'pricing_types',
                     'policy_names'
                 ]
             ]);

    expect($response->json('success'))->toBeTrue();
    expect($response->json('data.systems'))->toHaveCount(2);
});

test('새로운 수리비 정책을 생성할 수 있다', function () {
    $systemData = [
        'name' => 'general',
        'display_name' => '일반 제품',
        'description' => '일반 제품의 가격별 수리비 관리',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
        'is_active' => true
    ];

    $response = $this->postJson('/wms/settings/repairs/cost-systems', $systemData);

    $response->assertStatus(201)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'data' => [
                     'id',
                     'name',
                     'display_name',
                     'description',
                     'pricing_type',
                     'is_active'
                 ]
             ]);

    expect($response->json('success'))->toBeTrue();
    expect($response->json('data.name'))->toBe('general');

    $this->assertDatabaseHas('repair_cost_policies', [
        'name' => 'general',
        'display_name' => '일반 제품'
    ]);
});

test('특정 수리비 정책을 조회할 수 있다', function () {
    $system = RepairCostPolicy::first();

    $response = $this->getJson("/wms/settings/repairs/cost-systems/{$system->id}");

    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'data' => [
                     'id',
                     'name',
                     'display_name',
                     'description',
                     'pricing_type',
                     'is_active',
                     'categories',
                     'categories_count',
                     'active_categories_count'
                 ]
             ]);

    expect($response->json('success'))->toBeTrue();
    expect($response->json('data.id'))->toBe($system->id);
});

test('수리비 정책을 수정할 수 있다', function () {
    $system = RepairCostPolicy::first();

    $updateData = [
        'display_name' => '수정된 모니터',
        'description' => '수정된 설명',
        'is_active' => false
    ];

    $response = $this->putJson("/wms/settings/repairs/cost-systems/{$system->id}", $updateData);

    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'data'
             ]);

    expect($response->json('success'))->toBeTrue();
    expect($response->json('data.display_name'))->toBe('수정된 모니터');
    expect($response->json('data.is_active'))->toBeFalse();

    $this->assertDatabaseHas('repair_cost_policies', [
        'id' => $system->id,
        'display_name' => '수정된 모니터',
        'is_active' => false
    ]);
});

test('시스템 활성화 상태를 토글할 수 있다', function () {
    $system = RepairCostPolicy::first();
    $originalStatus = $system->is_active;

    $response = $this->patchJson("/wms/settings/repairs/cost-systems/{$system->id}/toggle-active");

    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'data' => [
                     'id',
                     'is_active'
                 ]
             ]);

    expect($response->json('success'))->toBeTrue();
    expect($response->json('data.is_active'))->toBe(!$originalStatus);

    $this->assertDatabaseHas('repair_cost_policies', [
        'id' => $system->id,
        'is_active' => !$originalStatus
    ]);
});

test('시스템별 통계를 조회할 수 있다', function () {
    $response = $this->getJson('/wms/settings/repairs/cost-systems/statistics');

    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'data' => [
                     'systems' => [
                         '*' => [
                             'id',
                             'name',
                             'display_name',
                             'pricing_type',
                             'is_active',
                             'statistics' => [
                                 'categories_count',
                                 'active_categories_count',
                                 'ranges_count',
                                 'active_ranges_count',
                                 'costs_count',
                                 'active_costs_count'
                             ]
                         ]
                     ],
                     'overall' => [
                         'total_systems',
                         'active_systems',
                         'total_categories',
                         'active_categories',
                         'total_ranges',
                         'active_ranges',
                         'total_costs',
                         'active_costs'
                     ]
                 ]
             ]);

    expect($response->json('success'))->toBeTrue();
    expect($response->json('data.overall.total_systems'))->toBe(2);
});

test('유효하지 않은 시스템명으로 생성 시 실패한다', function () {
    $systemData = [
        'name' => 'invalid_system',
        'display_name' => '유효하지 않은 시스템',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
        'is_active' => true
    ];

    $response = $this->postJson('/wms/settings/repairs/cost-systems', $systemData);

    $response->assertStatus(422)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'error_code',
                 'errors'
             ]);

    expect($response->json('success'))->toBeFalse();
});

test('중복된 시스템명으로 생성 시 실패한다', function () {
    $systemData = [
        'name' => 'monitor', // 이미 존재하는 시스템명
        'display_name' => '중복 모니터',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_SIZE,
        'is_active' => true
    ];

    $response = $this->postJson('/wms/settings/repairs/cost-systems', $systemData);

    $response->assertStatus(422)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'error_code',
                 'errors'
             ]);

    expect($response->json('success'))->toBeFalse();
});

test('존재하지 않는 시스템 조회 시 404를 반환한다', function () {
    $response = $this->getJson('/wms/settings/repairs/cost-systems/999');

    $response->assertStatus(404)
             ->assertJsonStructure([
                 'success',
                 'message',
                 'error_code'
             ]);

    expect($response->json('success'))->toBeFalse();
});
