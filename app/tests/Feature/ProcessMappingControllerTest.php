<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\RepairCostTypeProcessMapping;
use App\Models\RepairProcess;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ProcessMappingControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // 테스트용 사용자 생성 및 인증
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /** @test */
    public function 프로세스_매핑_목록을_조회할_수_있다()
    {
        // Given: 프로세스 매핑들이 생성되어 있음
        RepairCostTypeProcessMapping::factory()->count(3)->create();

        // When: 매핑 목록 조회
        $response = $this->getJson('/wms/settings/repairs/process-mappings');

        // Then: 성공적으로 조회됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'mappings',
                    'grouped_mappings',
                    'repair_types',
                    'statistics'
                ]
            ]);
    }

    /** @test */
    public function 새로운_프로세스_매핑을_생성할_수_있다()
    {
        // Given: RepairProcess가 생성되어 있음
        $process = RepairProcess::factory()->create();

        // Given: 새로운 매핑 데이터
        $mappingData = [
            'process_code' => $process->code,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            'is_active' => true
        ];

        // When: 매핑 생성
        $response = $this->postJson('/wms/settings/repairs/process-mappings', $mappingData);

        // Then: 성공적으로 생성됨
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'process_code',
                    'repair_type',
                    'is_active',
                    'repair_process'
                ]
            ]);

        $this->assertDatabaseHas('repair_process_mappings', [
            'process_code' => $process->code,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS
        ]);
    }

    /** @test */
    public function 프로세스_매핑을_수정할_수_있다()
    {
        // Given: 기존 매핑이 있음
        $mapping = RepairCostTypeProcessMapping::factory()->create();

        // When: 매핑 수정
        $updateData = [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
            'is_active' => false
        ];

        $response = $this->putJson("/wms/settings/repairs/process-mappings/{$mapping->id}", $updateData);

        // Then: 성공적으로 수정됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data'
            ]);

        $this->assertDatabaseHas('repair_process_mappings', [
            'id' => $mapping->id,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
            'is_active' => false
        ]);
    }

    /** @test */
    public function 프로세스_매핑을_삭제할_수_있다()
    {
        // Given: 기존 매핑이 있음
        $mapping = RepairCostTypeProcessMapping::factory()->create();

        // When: 매핑 삭제
        $response = $this->deleteJson("/wms/settings/repairs/process-mappings/{$mapping->id}");

        // Then: 성공적으로 삭제됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message'
            ]);

        $this->assertDatabaseMissing('repair_process_mappings', [
            'id' => $mapping->id
        ]);
    }

    /** @test */
    public function 대량_매핑_설정을_수행할_수_있다()
    {
        // Given: 여러 RepairProcess들이 생성되어 있음
        $process1 = RepairProcess::factory()->create();
        $process2 = RepairProcess::factory()->create();
        $process3 = RepairProcess::factory()->create();

        // Given: 대량 매핑 데이터
        $bulkData = [
            'mappings' => [
                [
                    'process_code' => $process1->code,
                    'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
                    'is_active' => true
                ],
                [
                    'process_code' => $process2->code,
                    'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
                    'is_active' => true
                ],
                [
                    'process_code' => $process3->code,
                    'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE,
                    'is_active' => false
                ]
            ]
        ];

        // When: 대량 매핑 설정
        $response = $this->postJson('/wms/settings/repairs/process-mappings/bulk-update', $bulkData);

        // Then: 성공적으로 처리됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'results',
                    'statistics'
                ]
            ]);

        // 매핑들이 생성되었는지 확인
        $this->assertDatabaseHas('repair_process_mappings', [
            'process_code' => $process1->code,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS
        ]);
        $this->assertDatabaseHas('repair_process_mappings', [
            'process_code' => $process2->code,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING
        ]);
        $this->assertDatabaseHas('repair_process_mappings', [
            'process_code' => $process3->code,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE
        ]);
    }

    /** @test */
    public function 매핑되지_않은_프로세스_목록을_조회할_수_있다()
    {
        // Given: 여러 RepairProcess들이 생성되어 있음
        $process1 = RepairProcess::factory()->create();
        $process2 = RepairProcess::factory()->create();
        $process3 = RepairProcess::factory()->create();

        // Given: 일부만 매핑되어 있음
        RepairCostTypeProcessMapping::factory()->withProcessCode($process1->code)->create();

        // When: 매핑되지 않은 프로세스 목록 조회
        $response = $this->getJson('/wms/settings/repairs/process-mappings/unmapped-processes');

        // Then: 성공적으로 조회됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'unmapped_processes',
                    'count'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['count']); // process2, process3만 매핑되지 않음
    }

    /** @test */
    public function 매핑_통계를_조회할_수_있다()
    {
        // Given: 다양한 매핑들이 생성되어 있음
        RepairCostTypeProcessMapping::factory()->count(2)->create(['repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS]);
        RepairCostTypeProcessMapping::factory()->count(3)->create(['repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING]);
        RepairCostTypeProcessMapping::factory()->count(1)->create(['repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE]);

        // When: 통계 조회
        $response = $this->getJson('/wms/settings/repairs/process-mappings/statistics');

        // Then: 성공적으로 조회됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_processes',
                    'mapped_processes',
                    'unmapped_processes',
                    'mapping_rate',
                    'by_repair_type'
                ]
            ]);
    }

    /** @test */
    public function 중복_수리_유형_매핑시_오류가_발생한다()
    {
        // Given: 이미 매핑된 수리 유형이 있음
        $existingMapping = RepairCostTypeProcessMapping::factory()->parts()->active()->create();

        // Given: 같은 수리 유형으로 새 매핑 시도
        $process = RepairProcess::factory()->create();
        $mappingData = [
            'process_code' => $process->code,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            'is_active' => true
        ];

        // When: 매핑 생성 시도
        $response = $this->postJson('/wms/settings/repairs/process-mappings', $mappingData);

        // Then: 1:1 매핑 제약 오류 발생
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
                'data'
            ]);
    }

    /** @test */
    public function 존재하지_않는_프로세스_코드로_매핑_생성시_오류가_발생한다()
    {
        // Given: 존재하지 않는 프로세스 코드
        $mappingData = [
            'process_code' => 'NON_EXISTENT_CODE',
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            'is_active' => true
        ];

        // When: 매핑 생성 시도
        $response = $this->postJson('/wms/settings/repairs/process-mappings', $mappingData);

        // Then: 유효성 검사 오류 발생
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
                'errors'
            ]);
    }

    /** @test */
    public function 존재하지_않는_매핑_수정시_404_오류가_발생한다()
    {
        // When: 존재하지 않는 매핑 수정 시도
        $response = $this->putJson('/wms/settings/repairs/process-mappings/999', [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING
        ]);

        // Then: 404 오류 발생
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code'
            ]);
    }

    /** @test */
    public function 수리_유형별_필터링이_작동한다()
    {
        // Given: 다양한 수리 유형의 매핑들이 있음
        RepairCostTypeProcessMapping::factory()->parts()->create();
        RepairCostTypeProcessMapping::factory()->cleaning()->create();

        // When: 부품교체 매핑만 필터링
        $response = $this->getJson('/wms/settings/repairs/process-mappings?repair_type=repair_parts');

        // Then: 부품교체 매핑만 반환됨
        $response->assertStatus(200);
        $data = $response->json('data.mappings');
        $this->assertCount(1, $data);
        $this->assertEquals(RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS, $data[0]['repair_type']);
    }

    /** @test */
    public function 활성화_상태별_필터링이_작동한다()
    {
        // Given: 활성/비활성 매핑들이 있음
        RepairCostTypeProcessMapping::factory()->active()->create();
        RepairCostTypeProcessMapping::factory()->inactive()->create();

        // When: 활성 매핑만 필터링
        $response = $this->getJson('/wms/settings/repairs/process-mappings?is_active=true');

        // Then: 활성 매핑만 반환됨
        $response->assertStatus(200);
        $data = $response->json('data.mappings');
        $this->assertCount(1, $data);
        $this->assertTrue($data[0]['is_active']);
    }

    /** @test */
    public function 프로세스_코드별_검색이_작동한다()
    {
        // Given: 특정 프로세스 코드를 가진 매핑이 있음
        $process = RepairProcess::factory()->create(['code' => 'TEST001']);
        RepairCostTypeProcessMapping::factory()->withProcessCode($process->code)->create();

        // When: 프로세스 코드로 검색
        $response = $this->getJson('/wms/settings/repairs/process-mappings?process_code=TEST');

        // Then: 해당 매핑이 반환됨
        $response->assertStatus(200);
        $data = $response->json('data.mappings');
        $this->assertCount(1, $data);
        $this->assertEquals('TEST001', $data[0]['process_code']);
    }

    /** @test */
    public function 대량_매핑에서_일부_실패시_부분_성공_응답을_반환한다()
    {
        // Given: 유효한 프로세스와 존재하지 않는 프로세스
        $validProcess = RepairProcess::factory()->create();

        // Given: 대량 매핑 데이터 (일부 유효하지 않음)
        $bulkData = [
            'mappings' => [
                [
                    'process_code' => $validProcess->code,
                    'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
                    'is_active' => true
                ],
                [
                    'process_code' => 'INVALID_CODE',
                    'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
                    'is_active' => true
                ]
            ]
        ];

        // When: 대량 매핑 시도
        $response = $this->postJson('/wms/settings/repairs/process-mappings/bulk-update', $bulkData);

        // Then: 부분 실패 응답
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
                'data' => [
                    'results',
                    'errors'
                ]
            ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['results']); // 성공한 결과가 있음
        $this->assertNotEmpty($data['errors']); // 실패한 결과가 있음
    }

    /** @test */
    public function 매핑_충돌을_감지할_수_있다()
    {
        // Given: 중복 매핑이 있음 (같은 수리 유형에 여러 프로세스)
        $process1 = RepairProcess::factory()->create();
        $process2 = RepairProcess::factory()->create();

        RepairCostTypeProcessMapping::factory()->withProcessCode($process1->code)->parts()->active()->create();
        RepairCostTypeProcessMapping::factory()->withProcessCode($process2->code)->parts()->active()->create();

        // When: 충돌 감지
        $response = $this->getJson('/wms/settings/repairs/process-mappings/conflicts');

        // Then: 충돌이 감지됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'conflicts',
                    'conflict_count'
                ]
            ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, $data['conflict_count']);

        // 중복 수리 유형 충돌이 있는지 확인
        $hasDuplicateConflict = collect($data['conflicts'])->contains('type', 'duplicate_repair_type');
        $this->assertTrue($hasDuplicateConflict);
    }

    /** @test */
    public function 매핑_상태를_일괄_변경할_수_있다()
    {
        // Given: 여러 매핑들이 있음
        $mapping1 = RepairCostTypeProcessMapping::factory()->active()->create();
        $mapping2 = RepairCostTypeProcessMapping::factory()->active()->create();
        $mapping3 = RepairCostTypeProcessMapping::factory()->active()->create();

        // When: 매핑 상태를 비활성화로 일괄 변경
        $updateData = [
            'mapping_ids' => [$mapping1->id, $mapping2->id],
            'is_active' => false,
            'reason' => '테스트를 위한 일괄 비활성화'
        ];

        $response = $this->postJson('/wms/settings/repairs/process-mappings/bulk-status-update', $updateData);

        // Then: 성공적으로 변경됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'updated_count',
                    'statistics'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['updated_count']);

        // 데이터베이스에서 상태가 변경되었는지 확인
        $this->assertDatabaseHas('repair_process_mappings', [
            'id' => $mapping1->id,
            'is_active' => false
        ]);
        $this->assertDatabaseHas('repair_process_mappings', [
            'id' => $mapping2->id,
            'is_active' => false
        ]);
        $this->assertDatabaseHas('repair_process_mappings', [
            'id' => $mapping3->id,
            'is_active' => true // 변경되지 않음
        ]);
    }

    /** @test */
    public function 매핑_검증_리포트를_생성할_수_있다()
    {
        // Given: 다양한 매핑 상황이 있음
        RepairCostTypeProcessMapping::factory()->count(3)->create();
        RepairCostTypeProcessMapping::factory()->inactive()->create();

        // When: 검증 리포트 생성
        $response = $this->getJson('/wms/settings/repairs/process-mappings/validation-report');

        // Then: 리포트가 생성됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'generated_at',
                    'statistics',
                    'conflicts',
                    'quality_score',
                    'recommendations',
                    'summary'
                ]
            ]);

        $data = $response->json('data');
        $this->assertIsFloat($data['quality_score']);
        $this->assertGreaterThanOrEqual(0, $data['quality_score']);
        $this->assertLessThanOrEqual(100, $data['quality_score']);
        $this->assertArrayHasKey('total_conflicts', $data['summary']);
    }

    /** @test */
    public function 수리_유형별_매핑_현황을_조회할_수_있다()
    {
        // Given: 특정 수리 유형의 매핑들이 있음
        RepairCostTypeProcessMapping::factory()->count(2)->parts()->create();
        RepairCostTypeProcessMapping::factory()->count(1)->cleaning()->create();

        // When: 부품교체 매핑 현황 조회
        $response = $this->getJson('/wms/settings/repairs/process-mappings/by-repair-type/repair_parts');

        // Then: 해당 수리 유형의 매핑만 반환됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'repair_type',
                    'repair_type_name',
                    'mappings',
                    'count',
                    'active_count'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals('repair_parts', $data['repair_type']);
        $this->assertEquals('수리_부품교체', $data['repair_type_name']);
        $this->assertEquals(2, $data['count']);
        $this->assertEquals(2, $data['active_count']);
    }

    /** @test */
    public function 매핑_이력을_조회할_수_있다()
    {
        // Given: 매핑들이 생성되어 있음
        RepairCostTypeProcessMapping::factory()->count(3)->create();

        // When: 매핑 이력 조회
        $response = $this->getJson('/wms/settings/repairs/process-mappings/history');

        // Then: 이력이 조회됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'history',
                    'count',
                    'filters'
                ]
            ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, $data['count']);
    }

    /** @test */
    public function 매핑_백업을_생성할_수_있다()
    {
        // Given: 매핑들이 생성되어 있음
        RepairCostTypeProcessMapping::factory()->count(2)->create();

        // When: 매핑 백업 생성
        $response = $this->postJson('/wms/settings/repairs/process-mappings/backup');

        // Then: 백업이 생성됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'backup_id',
                    'backup_date',
                    'mapping_count'
                ]
            ]);

        $data = $response->json('data');
        $this->assertNotEmpty($data['backup_id']);
        $this->assertEquals(2, $data['mapping_count']);
    }

    /** @test */
    public function 매핑_백업을_복원할_수_있다()
    {
        // Given: 매핑들이 생성되어 있고 백업이 있음
        RepairCostTypeProcessMapping::factory()->count(2)->create();

        $backupResponse = $this->postJson('/wms/settings/repairs/process-mappings/backup');
        $backupId = $backupResponse->json('data.backup_id');

        // Given: 기존 매핑들을 삭제
        RepairCostTypeProcessMapping::truncate();

        // When: 매핑 복원
        $restoreData = [
            'backup_id' => $backupId,
            'confirm_restore' => true
        ];

        $response = $this->postJson('/wms/settings/repairs/process-mappings/restore', $restoreData);

        // Then: 복원이 성공함
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'backup_id',
                    'restored_count',
                    'current_backup_id',
                    'restored_at'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals($backupId, $data['backup_id']);
        $this->assertEquals(2, $data['restored_count']);

        // 데이터베이스에 매핑이 복원되었는지 확인
        $this->assertEquals(2, RepairCostTypeProcessMapping::count());
    }

    /** @test */
    public function 매핑_복원시_확인_필요()
    {
        // Given: 백업 ID
        $backupId = 'test_backup_id';

        // When: 확인 없이 복원 시도
        $restoreData = [
            'backup_id' => $backupId,
            'confirm_restore' => false
        ];

        $response = $this->postJson('/wms/settings/repairs/process-mappings/restore', $restoreData);

        // Then: 확인 필요 오류
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code'
            ]);

        $this->assertEquals('RESTORE_NOT_CONFIRMED', $response->json('error_code'));
    }

    /** @test */
    public function 매핑_이력_필터링이_작동한다()
    {
        // Given: 특정 프로세스 코드를 가진 매핑이 있음
        $process = RepairProcess::factory()->create(['code' => 'FILTER001']);
        RepairCostTypeProcessMapping::factory()->withProcessCode($process->code)->create();

        // When: 프로세스 코드로 이력 필터링
        $response = $this->getJson('/wms/settings/repairs/process-mappings/history?process_code=FILTER001');

        // Then: 해당 매핑만 반환됨
        $response->assertStatus(200);
        $data = $response->json('data.history');
        $this->assertCount(1, $data);
        $this->assertEquals('FILTER001', $data[0]['process_code']);
    }

    /** @test */
    public function 매핑_상태_일괄_변경시_일부_실패_처리()
    {
        // Given: 존재하지 않는 매핑 ID 포함
        $validMapping = RepairCostTypeProcessMapping::factory()->create();
        $invalidMappingId = 99999;

        // When: 일부 유효하지 않은 매핑 ID로 상태 변경 시도
        $updateData = [
            'mapping_ids' => [$validMapping->id, $invalidMappingId],
            'is_active' => false
        ];

        $response = $this->postJson('/wms/settings/repairs/process-mappings/bulk-status-update', $updateData);

        // Then: 부분 실패 응답
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
                'data' => [
                    'updated_count',
                    'errors'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(1, $data['updated_count']); // 하나만 성공
        $this->assertNotEmpty($data['errors']); // 하나는 실패
    }

    /** @test */
    public function 매핑_검증_리포트의_품질_점수가_정확하다()
    {
        // Given: 완벽한 매핑 상황 (모든 프로세스가 매핑되고 충돌 없음)
        RepairProcess::factory()->count(5)->create();
        RepairCostTypeProcessMapping::factory()->count(5)->create();

        // When: 검증 리포트 생성
        $response = $this->getJson('/wms/settings/repairs/process-mappings/validation-report');

        // Then: 높은 품질 점수
        $response->assertStatus(200);
        $qualityScore = $response->json('data.quality_score');
        $this->assertGreaterThan(80, $qualityScore); // 완벽한 상황에서는 높은 점수
    }

    /** @test */
    public function 매핑_충돌_감지시_다양한_충돌_유형을_감지한다()
    {
        // Given: 다양한 충돌 상황
        // 1. 중복 매핑
        $process1 = RepairProcess::factory()->create();
        $process2 = RepairProcess::factory()->create();
        RepairCostTypeProcessMapping::factory()->withProcessCode($process1->code)->parts()->active()->create();
        RepairCostTypeProcessMapping::factory()->withProcessCode($process2->code)->parts()->active()->create();

        // 2. 비활성화된 매핑
        RepairCostTypeProcessMapping::factory()->inactive()->create();

        // When: 충돌 감지
        $response = $this->getJson('/wms/settings/repairs/process-mappings/conflicts');

        // Then: 다양한 충돌 유형이 감지됨
        $response->assertStatus(200);
        $conflicts = $response->json('data.conflicts');

        $conflictTypes = collect($conflicts)->pluck('type')->toArray();
        $this->assertContains('duplicate_repair_type', $conflictTypes);
        $this->assertContains('inactive_mappings', $conflictTypes);
    }
}
