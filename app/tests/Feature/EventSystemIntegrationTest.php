<?php

use App\Models\Cate4;
use App\Models\Cate5;
use App\Services\EventManager;
use App\Services\NotificationManager;
use App\Services\DataSerializationService;
use App\Services\CategoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 테스트용 Redis 연결 설정
    Redis::flushall();
    Cache::flush();
    
    // 서비스 인스턴스 생성
    $this->categoryService = app(CategoryService::class);
    $this->dataSerializer = app(DataSerializationService::class);
    $this->notificationManager = app(NotificationManager::class);
    $this->eventManager = app(EventManager::class);
});

describe('이벤트 시스템 통합 테스트', function () {
    
    it('Cate4 생성 시 자동 브로드캐스팅이 작동한다', function () {
        // Given: 초기 카테고리 데이터 준비
        $initialCategories = $this->categoryService->getAllCategories();
        $initialCount = count($initialCategories->toArray(request()));
        
        // When: 새로운 Cate4 생성
        $cate4 = Cate4::create([
            'name' => '테스트 카테고리 4'
        ]);
        
        // Then: 카테고리 캐시가 무효화되고 새 데이터가 반영된다
        $updatedCategories = $this->categoryService->getAllCategories(false); // 캐시 사용 안함
        $updatedCount = count($updatedCategories->toArray(request()));
        
        expect($updatedCount)->toBe($initialCount + 1);
        expect($cate4->exists)->toBeTrue();
        
        // 이벤트 매니저가 정상적으로 작동했는지 확인 (실제 동작 확인)
        // Observer가 정상적으로 작동했다면 EventManager의 handleModelEvent가 호출되었을 것
    });

    it('Cate4 수정 시 자동 브로드캐스팅이 작동한다', function () {
        // Given: 기존 Cate4 생성
        $cate4 = Cate4::create([
            'name' => '원본 카테고리 4'
        ]);
        
        // 초기 캐시 생성
        $this->categoryService->getAllCategories();
        
        // When: Cate4 수정
        $cate4->update([
            'name' => '수정된 카테고리 4'
        ]);
        
        // Then: 캐시가 무효화되고 수정된 데이터가 반영된다
        $updatedCategories = $this->categoryService->getAllCategories(false);
        $categoryArray = $updatedCategories->toArray(request());
        
        $updatedCategory = collect($categoryArray)->firstWhere('id', $cate4->id);
        expect($updatedCategory['name'])->toBe('수정된 카테고리 4');
    });

    it('Cate4 삭제 시 자동 브로드캐스팅이 작동한다', function () {
        // Given: 기존 Cate4 생성
        $cate4 = Cate4::create([
            'name' => '삭제될 카테고리 4'
        ]);
        
        $initialCategories = $this->categoryService->getAllCategories();
        $initialCount = count($initialCategories->toArray(request()));
        
        // When: Cate4 삭제
        $cate4->delete();
        
        // Then: 캐시가 무효화되고 삭제된 데이터가 반영된다
        $updatedCategories = $this->categoryService->getAllCategories(false);
        $updatedCount = count($updatedCategories->toArray(request()));
        
        expect($updatedCount)->toBe($initialCount - 1);
    });

    it('Cate5 생성 시 자동 브로드캐스팅이 작동한다', function () {
        // Given: 부모 Cate4 생성
        $cate4 = Cate4::create([
            'name' => '부모 카테고리 4'
        ]);
        
        $initialCategories = $this->categoryService->getAllCategories();
        $initialCate5Count = collect($initialCategories->toArray(request()))
            ->where('id', $cate4->id)
            ->first()['cate5'] ?? [];
        
        // When: 새로운 Cate5 생성
        $cate5 = Cate5::create([
            'cate4_id' => $cate4->id,
            'name' => '테스트 카테고리 5'
        ]);
        
        // Then: 카테고리 캐시가 무효화되고 새 데이터가 반영된다
        $updatedCategories = $this->categoryService->getAllCategories(false);
        $updatedCate4 = collect($updatedCategories->toArray(request()))
            ->where('id', $cate4->id)
            ->first();
        
        expect($updatedCate4['cate5'])->toHaveCount(count($initialCate5Count) + 1);
        expect($cate5->exists)->toBeTrue();
    });

    it('Cate5 수정 시 자동 브로드캐스팅이 작동한다', function () {
        // Given: 부모 Cate4와 Cate5 생성
        $cate4 = Cate4::create([
            'name' => '부모 카테고리 4'
        ]);
        
        $cate5 = Cate5::create([
            'cate4_id' => $cate4->id,
            'name' => '원본 카테고리 5'
        ]);
        
        // When: Cate5 수정
        $cate5->update([
            'name' => '수정된 카테고리 5'
        ]);
        
        // Then: 캐시가 무효화되고 수정된 데이터가 반영된다
        $updatedCategories = $this->categoryService->getAllCategories(false);
        $updatedCate4 = collect($updatedCategories->toArray(request()))
            ->where('id', $cate4->id)
            ->first();
        
        $updatedCate5 = collect($updatedCate4['cate5'])
            ->where('id', $cate5->id)
            ->first();
        
        expect($updatedCate5['name'])->toBe('수정된 카테고리 5');
    });

    it('Cate5 삭제 시 자동 브로드캐스팅이 작동한다', function () {
        // Given: 부모 Cate4와 Cate5 생성
        $cate4 = Cate4::create([
            'name' => '부모 카테고리 4'
        ]);
        
        $cate5 = Cate5::create([
            'cate4_id' => $cate4->id,
            'name' => '삭제될 카테고리 5'
        ]);
        
        $initialCategories = $this->categoryService->getAllCategories();
        $initialCate5Count = collect($initialCategories->toArray(request()))
            ->where('id', $cate4->id)
            ->first()['cate5'] ?? [];
        
        // When: Cate5 삭제
        $cate5->delete();
        
        // Then: 캐시가 무효화되고 삭제된 데이터가 반영된다
        $updatedCategories = $this->categoryService->getAllCategories(false);
        $updatedCate4 = collect($updatedCategories->toArray(request()))
            ->where('id', $cate4->id)
            ->first();
        
        expect($updatedCate4['cate5'])->toHaveCount(count($initialCate5Count) - 1);
    });

    it('디바운싱이 정상적으로 작동한다', function () {
        // Given: 테스트용 Cate4 생성
        $cate4 = Cate4::create([
            'name' => '디바운싱 테스트 카테고리'
        ]);
        
        // When: 짧은 시간 내에 여러 번 수정
        for ($i = 1; $i <= 5; $i++) {
            $cate4->update([
                'name' => "디바운싱 테스트 카테고리 {$i}"
            ]);
            usleep(100000); // 0.1초 대기
        }
        
        // Then: 디바운스 상태 확인
        $debounceStatus = $this->eventManager->getDebounceStatus('cate4');
        
        expect($debounceStatus['model_name'])->toBe('cate4');
        expect($debounceStatus['pending_events'])->toBeArray();
        expect($debounceStatus['debounce_delay'])->toBeInt();
    });

    it('중복 전송 방지가 정상적으로 작동한다', function () {
        // Given: 테스트용 Cate4 생성
        $cate4 = Cate4::create([
            'name' => '중복 방지 테스트 카테고리'
        ]);
        
        // 초기 디바운스 캐시 상태 확인
        $initialStatus = $this->eventManager->getDebounceStatus('cate4');
        $initialEventCount = count($initialStatus['pending_events']);
        
        // When: 동일한 수정을 여러 번 수행
        for ($i = 0; $i < 3; $i++) {
            $cate4->update([
                'name' => '중복 방지 테스트 카테고리 - 동일 수정'
            ]);
        }
        
        // Then: 디바운스 캐시에 이벤트가 누적되었는지 확인
        $finalStatus = $this->eventManager->getDebounceStatus('cate4');
        $finalEventCount = count($finalStatus['pending_events']);
        
        expect($finalEventCount)->toBeGreaterThan($initialEventCount);
        expect($finalEventCount)->toBeLessThanOrEqual($initialEventCount + 3);
    });

    it('다양한 모델 변경 이벤트가 정상적으로 처리된다', function () {
        // Given: 여러 카테고리 생성
        $cate4_1 = Cate4::create(['name' => '카테고리 4-1']);
        $cate4_2 = Cate4::create(['name' => '카테고리 4-2']);
        
        $cate5_1 = Cate5::create([
            'cate4_id' => $cate4_1->id,
            'name' => '카테고리 5-1'
        ]);
        
        $cate5_2 = Cate5::create([
            'cate4_id' => $cate4_2->id,
            'name' => '카테고리 5-2'
        ]);
        
        // When: 다양한 변경 작업 수행
        $cate4_1->update(['name' => '수정된 카테고리 4-1']);
        $cate5_1->update(['name' => '수정된 카테고리 5-1']);
        $cate5_2->delete();
        
        // Then: 모든 변경사항이 캐시에 반영되었는지 확인
        $finalCategories = $this->categoryService->getAllCategories(false);
        $categoryArray = $finalCategories->toArray(request());
        
        // Cate4 수정 확인
        $updatedCate4_1 = collect($categoryArray)->firstWhere('id', $cate4_1->id);
        expect($updatedCate4_1['name'])->toBe('수정된 카테고리 4-1');
        
        // Cate5 수정 확인
        $cate4_1_data = collect($categoryArray)->firstWhere('id', $cate4_1->id);
        $updatedCate5_1 = collect($cate4_1_data['cate5'])->firstWhere('id', $cate5_1->id);
        expect($updatedCate5_1['name'])->toBe('수정된 카테고리 5-1');
        
        // Cate5 삭제 확인
        $cate4_2_data = collect($categoryArray)->firstWhere('id', $cate4_2->id);
        $deletedCate5_2 = collect($cate4_2_data['cate5'])->firstWhere('id', $cate5_2->id);
        expect($deletedCate5_2)->toBeNull();
    });

    it('데이터 직렬화가 정상적으로 작동한다', function () {
        // Given: 테스트용 카테고리 데이터 생성
        $cate4 = Cate4::create(['name' => '직렬화 테스트 카테고리 4']);
        $cate5 = Cate5::create([
            'cate4_id' => $cate4->id,
            'name' => '직렬화 테스트 카테고리 5'
        ]);
        
        // When: 카테고리 데이터 직렬화
        $serializedData = $this->dataSerializer->serializeCategoryData();
        
        // Then: 직렬화된 데이터 구조 확인
        expect($serializedData)->toHaveKeys(['data', 'timestamp', 'serialized_at']);
        expect($serializedData['data'])->toBeArray();
        expect($serializedData['timestamp'])->toBeString();
        expect($serializedData['serialized_at'])->toBeString();
        
        // 생성한 카테고리가 직렬화된 데이터에 포함되어 있는지 확인
        $categoryFound = collect($serializedData['data'])
            ->contains('id', $cate4->id);
        expect($categoryFound)->toBeTrue();
    });

    it('이벤트 매니저의 디바운스 캐시 강제 처리가 작동한다', function () {
        // Given: 디바운스 이벤트 생성
        $cate4 = Cate4::create(['name' => '강제 처리 테스트']);
        $cate4->update(['name' => '강제 처리 테스트 - 수정됨']);
        
        // 디바운스 이벤트가 대기 중인지 확인
        $statusBefore = $this->eventManager->getDebounceStatus('cate4');
        expect($statusBefore['pending_events'])->not->toBeEmpty();
        
        // When: 디바운스 캐시 강제 처리
        $result = $this->eventManager->flushDebounceCache('cate4');
        
        // Then: 처리 성공 및 캐시 정리 확인
        expect($result)->toBeTrue();
        
        // 잠시 대기 후 상태 확인
        sleep(1);
        $statusAfter = $this->eventManager->getDebounceStatus('cate4');
        expect($statusAfter['pending_events'])->toBeEmpty();
    });

    it('Observer 등록 상태를 확인할 수 있다', function () {
        // When: 등록된 Observer 목록 조회
        $registeredObservers = $this->eventManager->getRegisteredObservers();
        
        // Then: Observer 등록 상태 확인
        expect($registeredObservers)->toBeArray();
        
        // 실제 Observer가 작동하는지 확인
        $cate4 = Cate4::create(['name' => 'Observer 테스트']);
        
        // Observer가 정상적으로 작동했다면 캐시가 무효화되어야 함
        $categories = $this->categoryService->getAllCategories(false);
        $categoryArray = $categories->toArray(request());
        
        $createdCategory = collect($categoryArray)->firstWhere('id', $cate4->id);
        expect($createdCategory)->not->toBeNull();
        expect($createdCategory['name'])->toBe('Observer 테스트');
    });
});

describe('에러 처리 테스트', function () {
    
    it('잘못된 모델 이벤트 처리 시 예외가 발생하지 않는다', function () {
        // Given: EventManager 인스턴스
        $eventManager = $this->eventManager;
        
        // When & Then: 잘못된 이벤트 타입으로 호출해도 예외가 발생하지 않아야 함
        $cate4 = Cate4::create(['name' => '에러 테스트']);
        
        expect(function () use ($eventManager, $cate4) {
            $eventManager->handleModelEvent('invalid_event', $cate4);
        })->not->toThrow(Exception::class);
    });

    it('데이터 직렬화 실패 시 빈 데이터를 반환한다', function () {
        // Given: CategoryService Mock (실패 상황 시뮬레이션)
        $mockCategoryService = Mockery::mock(CategoryService::class);
        $mockCategoryService->shouldReceive('getAllCategories')
            ->andThrow(new Exception('데이터베이스 연결 실패'));
        
        $dataSerializer = new DataSerializationService($mockCategoryService);
        
        // When: 직렬화 시도
        $result = $dataSerializer->serializeCategoryData();
        
        // Then: 빈 데이터와 타임스탬프가 반환되어야 함
        expect($result)->toHaveKeys(['data', 'timestamp', 'serialized_at']);
        expect($result['data'])->toBeArray();
        expect($result['data'])->toBeEmpty();
    });
});