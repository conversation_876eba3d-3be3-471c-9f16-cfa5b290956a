<?php

use App\Models\RepairCostPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 테스트용 데이터 생성
    RepairCostPolicy::create([
        'name' => 'monitor',
        'display_name' => '모니터',
        'description' => '모니터 제품의 크기별 수리비 관리',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_SIZE,
        'is_active' => true
    ]);

    RepairCostPolicy::create([
        'name' => 'apple',
        'display_name' => '애플',
        'description' => '애플 제품의 모델별 수리비 관리',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
        'is_active' => true
    ]);

    RepairCostPolicy::create([
        'name' => 'inactive_system',
        'display_name' => '비활성 시스템',
        'description' => '테스트용 비활성 시스템',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
        'is_active' => false
    ]);
});

test('수리비 정책을 생성할 수 있다', function () {
    $system = RepairCostPolicy::create([
        'name' => 'test_system',
        'display_name' => '테스트 시스템',
        'description' => '테스트용 시스템',
        'pricing_type' => RepairCostPolicy::PRICING_TYPE_PRICE,
        'is_active' => true
    ]);

    expect($system)->toBeInstanceOf(RepairCostPolicy::class);
    expect($system->name)->toBe('test_system');
    expect($system->display_name)->toBe('테스트 시스템');
    expect($system->pricing_type)->toBe(RepairCostPolicy::PRICING_TYPE_PRICE);
    expect($system->is_active)->toBeTrue();
});

test('활성화된 시스템만 조회할 수 있다', function () {
    $activeSystems = RepairCostPolicy::active()->get();

    expect($activeSystems)->toHaveCount(2);
    expect($activeSystems->pluck('name')->toArray())->toContain('monitor', 'apple');
    expect($activeSystems->pluck('name')->toArray())->not->toContain('inactive_system');
});

test('시스템명으로 조회할 수 있다', function () {
    $monitorSystem = RepairCostPolicy::byName('monitor')->first();

    expect($monitorSystem)->not->toBeNull();
    expect($monitorSystem->name)->toBe('monitor');
    expect($monitorSystem->display_name)->toBe('모니터');
});

test('가격 결정 방식으로 조회할 수 있다', function () {
    $sizeBased = RepairCostPolicy::byPricingType(RepairCostPolicy::PRICING_TYPE_SIZE)->get();

    expect($sizeBased)->toHaveCount(1);
    expect($sizeBased->first()->name)->toBe('monitor');
});

test('크기 기준 시스템인지 확인할 수 있다', function () {
    $monitorSystem = RepairCostPolicy::byName('monitor')->first();
    $appleSystem = RepairCostPolicy::byName('apple')->first();

    expect($monitorSystem->isSizeBased())->toBeTrue();
    expect($appleSystem->isSizeBased())->toBeFalse();
});

test('판매가 기준 시스템인지 확인할 수 있다', function () {
    $monitorSystem = RepairCostPolicy::byName('monitor')->first();

    expect($monitorSystem->isPriceBased())->toBeFalse();
});

test('모델별 시스템인지 확인할 수 있다', function () {
    $appleSystem = RepairCostPolicy::byName('apple')->first();
    $monitorSystem = RepairCostPolicy::byName('monitor')->first();

    expect($appleSystem->isModelBased())->toBeTrue();
    expect($monitorSystem->isModelBased())->toBeFalse();
});

test('사용 가능한 가격 결정 방식 목록을 반환할 수 있다', function () {
    $pricingTypes = RepairCostPolicy::getPricingTypes();

    expect($pricingTypes)->toBeArray();
    expect($pricingTypes)->toHaveKey(RepairCostPolicy::PRICING_TYPE_SIZE);
    expect($pricingTypes)->toHaveKey(RepairCostPolicy::PRICING_TYPE_PRICE);
    expect($pricingTypes)->toHaveKey(RepairCostPolicy::PRICING_TYPE_PRICE);
    expect($pricingTypes)->toHaveKey(RepairCostPolicy::PRICING_TYPE_PRICE);
    expect($pricingTypes[RepairCostPolicy::PRICING_TYPE_SIZE])->toBe('크기 기준');
});

test('사용 가능한 시스템명 목록을 반환할 수 있다', function () {
    $policyNames = RepairCostPolicy::getPolicyNames();

    expect($policyNames)->toBeArray();
    expect($policyNames)->toHaveKey(RepairCostPolicy::POLICY_MONITOR_GENERAL);
    expect($policyNames)->toHaveKey(RepairCostPolicy::POLICY_APPLE);
    expect($policyNames)->toHaveKey(RepairCostPolicy::POLICY_GENERAL_PRICE);
    expect($policyNames)->toHaveKey(RepairCostPolicy::POLICY_DEFAULT);
    expect($policyNames[RepairCostPolicy::POLICY_MONITOR_GENERAL])->toBe('모니터');
});
