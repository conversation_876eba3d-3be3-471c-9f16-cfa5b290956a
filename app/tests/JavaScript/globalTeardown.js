/**
 * Jest 전역 정리 파일
 * 모든 테스트 실행 후에 한 번 실행됩니다.
 */

const fetch = require('node-fetch');

/**
 * 전역 정리 함수
 */
module.exports = async () => {
  console.log('🧹 SSE E2E 테스트 전역 정리 시작...');
  
  const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:8000';
  
  try {
    // 테스트 환경 정리
    console.log('🗑️ 테스트 데이터 정리 중...');
    await cleanupTestData(baseUrl);
    
    // 활성 연결 정리
    console.log('🔌 활성 연결 정리 중...');
    await cleanupActiveConnections(baseUrl);
    
    // 성능 메트릭 수집 및 출력
    console.log('📊 최종 성능 메트릭 수집 중...');
    await collectFinalMetrics(baseUrl);
    
    console.log('✅ SSE E2E 테스트 전역 정리 완료');
    
  } catch (error) {
    console.warn('⚠️ 전역 정리 중 오류 발생:', error.message);
    // 정리 과정의 오류는 치명적이지 않으므로 경고만 출력
  }
};

/**
 * 테스트 데이터 정리
 */
async function cleanupTestData(baseUrl) {
  try {
    const response = await fetch(`${baseUrl}/api/test/reset-environment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      timeout: 10000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 테스트 데이터 정리 완료');
      
      if (data.cleaned_keys) {
        console.log(`   - 정리된 Redis 키: ${data.cleaned_keys}개`);
      }
    } else {
      console.warn(`⚠️ 테스트 데이터 정리 실패: ${response.status}`);
    }
  } catch (error) {
    console.warn('⚠️ 테스트 데이터 정리 오류:', error.message);
  }
}

/**
 * 활성 연결 정리
 */
async function cleanupActiveConnections(baseUrl) {
  try {
    const response = await fetch(`${baseUrl}/api/test/connection-status`, {
      method: 'GET',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      
      if (data.success && data.active_connections > 0) {
        console.log(`⚠️ 정리되지 않은 활성 연결: ${data.active_connections}개`);
        console.log('   - 이는 테스트 중 연결이 제대로 정리되지 않았음을 의미합니다.');
      } else {
        console.log('✅ 모든 연결이 정리되었습니다.');
      }
    }
  } catch (error) {
    console.warn('⚠️ 활성 연결 확인 오류:', error.message);
  }
}

/**
 * 최종 성능 메트릭 수집
 */
async function collectFinalMetrics(baseUrl) {
  try {
    const response = await fetch(`${baseUrl}/api/test/performance-metrics`, {
      method: 'GET',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      
      if (data.success && data.metrics) {
        console.log('📊 최종 성능 메트릭:');
        console.log(`   - 활성 연결: ${data.metrics.active_connections}개`);
        console.log(`   - 메모리 사용량: ${formatBytes(data.metrics.memory_usage.current)}`);
        console.log(`   - 최대 메모리 사용량: ${formatBytes(data.metrics.memory_usage.peak)}`);
        
        if (data.metrics.redis_info && !data.metrics.redis_info.error) {
          console.log(`   - Redis 연결 클라이언트: ${data.metrics.redis_info.connected_clients}개`);
          console.log(`   - Redis 메모리 사용량: ${data.metrics.redis_info.used_memory_human}`);
        }
        
        // 성능 경고 확인
        checkPerformanceWarnings(data.metrics);
      }
    }
  } catch (error) {
    console.warn('⚠️ 성능 메트릭 수집 오류:', error.message);
  }
}

/**
 * 성능 경고 확인
 */
function checkPerformanceWarnings(metrics) {
  const warnings = [];
  
  // 메모리 사용량 경고 (100MB 이상)
  if (metrics.memory_usage.current > 100 * 1024 * 1024) {
    warnings.push(`높은 메모리 사용량: ${formatBytes(metrics.memory_usage.current)}`);
  }
  
  // 활성 연결 경고 (테스트 후에는 0이어야 함)
  if (metrics.active_connections > 0) {
    warnings.push(`정리되지 않은 연결: ${metrics.active_connections}개`);
  }
  
  // Redis 연결 경고
  if (metrics.redis_info && !metrics.redis_info.error) {
    if (metrics.redis_info.connected_clients > 10) {
      warnings.push(`높은 Redis 연결 수: ${metrics.redis_info.connected_clients}개`);
    }
  }
  
  if (warnings.length > 0) {
    console.log('⚠️ 성능 경고:');
    warnings.forEach(warning => console.log(`   - ${warning}`));
  }
}

/**
 * 바이트를 읽기 쉬운 형태로 변환
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 테스트 실행 통계 출력
 */
function printTestStatistics() {
  const endTime = new Date();
  const startTime = global.testStartTime || endTime;
  const duration = endTime - startTime;
  
  console.log('📈 테스트 실행 통계:');
  console.log(`   - 총 실행 시간: ${Math.round(duration / 1000)}초`);
  console.log(`   - 종료 시간: ${endTime.toISOString()}`);
}

// 테스트 실행 통계 출력
printTestStatistics();