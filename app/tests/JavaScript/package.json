{"name": "sse-e2e-tests", "version": "1.0.0", "description": "SSE E2E 통합 테스트 패키지", "main": "index.js", "scripts": {"test": "jest --config=jest.config.js", "test:watch": "jest --config=jest.config.js --watch", "test:coverage": "jest --config=jest.config.js --coverage", "test:verbose": "jest --config=jest.config.js --verbose", "test:debug": "node --inspect-brk node_modules/.bin/jest --config=jest.config.js --runInBand", "test:single": "jest --config=jest.config.js --testNamePattern", "test:performance": "jest --config=jest.config.js --testPathPattern=performance", "test:network": "jest --config=jest.config.js --testPathPattern=network", "setup": "npm install && npm run test:setup", "test:setup": "node globalSetup.js", "test:teardown": "node globalTeardown.js", "lint": "eslint *.js", "lint:fix": "eslint *.js --fix"}, "keywords": ["sse", "server-sent-events", "e2e", "integration-test", "laravel", "real-time"], "author": "SSE Development Team", "license": "MIT", "dependencies": {"eventsource": "^2.0.2", "node-fetch": "^2.7.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "jest-html-reporters": "^3.1.5"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/setup.js"], "testTimeout": 60000, "maxWorkers": 1, "verbose": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "current"}}]]}, "eslintConfig": {"env": {"node": true, "jest": true, "es2021": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-console": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "prefer-const": "error", "no-var": "error"}, "globals": {"EventSource": "readonly", "fetch": "readonly", "testUtils": "readonly", "TEST_CONFIG": "readonly"}}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-repo/sse-system.git"}, "bugs": {"url": "https://github.com/your-repo/sse-system/issues"}, "homepage": "https://github.com/your-repo/sse-system#readme"}