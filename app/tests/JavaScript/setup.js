/**
 * Jest 테스트 설정 파일
 * 각 테스트 실행 전후에 필요한 설정을 수행합니다.
 */

// EventSource 폴리필 (Node.js 환경용)
if (typeof EventSource === 'undefined') {
  global.EventSource = require('eventsource');
}

// Fetch 폴리필 (Node.js 환경용)
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

// 전역 테스트 설정
global.TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:8000',
  sseEndpoint: '/api/sse/stream',
  timeout: 30000,
  reconnectDelay: 1000,
  maxRetries: 3
};

// 테스트 유틸리티 함수들
global.testUtils = {
  /**
   * 지정된 시간만큼 대기
   */
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  /**
   * 조건이 참이 될 때까지 대기
   */
  waitFor: async (condition, timeout = 10000, interval = 100) => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return true;
      }
      await global.testUtils.sleep(interval);
    }
    
    throw new Error(`조건이 ${timeout}ms 내에 충족되지 않았습니다.`);
  },
  
  /**
   * SSE 연결 생성 헬퍼
   */
  createSSEConnection: (endpoint = TEST_CONFIG.sseEndpoint) => {
    const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
    return new EventSource(url, { withCredentials: true });
  },
  
  /**
   * 테스트 메시지 전송 헬퍼
   */
  sendTestMessage: async (data) => {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/test/send-notification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'include',
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`테스트 메시지 전송 실패: ${response.status}`);
    }
    
    return response.json();
  },
  
  /**
   * 테스트 환경 초기화 헬퍼
   */
  resetTestEnvironment: async () => {
    try {
      const response = await fetch(`${TEST_CONFIG.baseUrl}/api/test/reset-environment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        console.warn('테스트 환경 초기화 실패:', response.status);
      }
      
      return response.json();
    } catch (error) {
      console.warn('테스트 환경 초기화 오류:', error.message);
    }
  },
  
  /**
   * 연결 상태 확인 헬퍼
   */
  getConnectionStatus: async () => {
    try {
      const response = await fetch(`${TEST_CONFIG.baseUrl}/api/test/connection-status`, {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`연결 상태 확인 실패: ${response.status}`);
      }
      
      return response.json();
    } catch (error) {
      console.warn('연결 상태 확인 오류:', error.message);
      return { success: false, error: error.message };
    }
  },
  
  /**
   * 성능 메트릭 수집 헬퍼
   */
  getPerformanceMetrics: async () => {
    try {
      const response = await fetch(`${TEST_CONFIG.baseUrl}/api/test/performance-metrics`, {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`성능 메트릭 수집 실패: ${response.status}`);
      }
      
      return response.json();
    } catch (error) {
      console.warn('성능 메트릭 수집 오류:', error.message);
      return { success: false, error: error.message };
    }
  }
};

// 각 테스트 스위트 실행 전 설정
beforeAll(async () => {
  console.log('🚀 SSE E2E 테스트 시작');
  console.log(`📡 테스트 서버: ${TEST_CONFIG.baseUrl}`);
  
  // 테스트 환경 초기화
  await global.testUtils.resetTestEnvironment();
  
  // 서버 연결 확인
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/test/connection-status`);
    if (!response.ok) {
      throw new Error(`서버 연결 실패: ${response.status}`);
    }
    console.log('✅ 테스트 서버 연결 확인됨');
  } catch (error) {
    console.error('❌ 테스트 서버 연결 실패:', error.message);
    throw error;
  }
});

// 각 테스트 스위트 실행 후 정리
afterAll(async () => {
  console.log('🧹 SSE E2E 테스트 정리 중...');
  
  // 테스트 환경 정리
  await global.testUtils.resetTestEnvironment();
  
  console.log('✅ SSE E2E 테스트 완료');
});

// 각 테스트 케이스 실행 전 설정
beforeEach(async () => {
  // 테스트 간 간격 (서버 부하 방지)
  await global.testUtils.sleep(100);
});

// 각 테스트 케이스 실행 후 정리
afterEach(async () => {
  // 활성 연결 정리 (메모리 누수 방지)
  if (global.activeConnections) {
    global.activeConnections.forEach(connection => {
      if (connection && connection.close) {
        connection.close();
      }
    });
    global.activeConnections = [];
  }
});

// 전역 오류 핸들러
process.on('unhandledRejection', (reason, promise) => {
  console.error('처리되지 않은 Promise 거부:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('처리되지 않은 예외:', error);
});

// Jest 매처 확장
expect.extend({
  /**
   * SSE 메시지 형식 검증 매처
   */
  toBeValidSseMessage(received) {
    const pass = (
      typeof received === 'object' &&
      received !== null &&
      typeof received.type === 'string' &&
      received.data !== undefined &&
      typeof received.timestamp === 'string'
    );
    
    if (pass) {
      return {
        message: () => `예상: 유효하지 않은 SSE 메시지 형식, 실제: 유효한 SSE 메시지 형식`,
        pass: true
      };
    } else {
      return {
        message: () => `예상: 유효한 SSE 메시지 형식, 실제: 유효하지 않은 SSE 메시지 형식`,
        pass: false
      };
    }
  },
  
  /**
   * 연결 상태 검증 매처
   */
  toBeConnected(received) {
    const pass = (
      received &&
      typeof received.readyState === 'number' &&
      received.readyState === EventSource.OPEN
    );
    
    if (pass) {
      return {
        message: () => `예상: 연결되지 않은 상태, 실제: 연결된 상태`,
        pass: true
      };
    } else {
      return {
        message: () => `예상: 연결된 상태, 실제: 연결되지 않은 상태`,
        pass: false
      };
    }
  }
});

// 테스트 환경 정보 출력
console.log('🔧 테스트 환경 설정 완료');
console.log(`   - Node.js 버전: ${process.version}`);
console.log(`   - 테스트 타임아웃: ${TEST_CONFIG.timeout}ms`);
console.log(`   - 재연결 지연: ${TEST_CONFIG.reconnectDelay}ms`);
console.log(`   - 최대 재시도: ${TEST_CONFIG.maxRetries}회`);