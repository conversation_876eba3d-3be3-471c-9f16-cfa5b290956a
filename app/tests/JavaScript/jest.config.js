/**
 * Jest 설정 파일
 * SSE E2E 테스트를 위한 Jest 환경 설정
 */

module.exports = {
  // 테스트 환경 설정
  testEnvironment: 'node',
  
  // 테스트 파일 패턴
  testMatch: [
    '**/tests/JavaScript/**/*.test.js'
  ],
  
  // 테스트 타임아웃 (SSE 테스트는 시간이 오래 걸릴 수 있음)
  testTimeout: 60000,
  
  // 설정 파일
  setupFilesAfterEnv: ['<rootDir>/tests/JavaScript/setup.js'],
  
  // 모듈 경로 매핑
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/resources/js/$1'
  },
  
  // 커버리지 설정
  collectCoverage: false,
  collectCoverageFrom: [
    'resources/js/**/*.{js,ts}',
    '!resources/js/**/*.d.ts',
    '!resources/js/**/*.test.{js,ts}'
  ],
  
  // 리포터 설정
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './tests/JavaScript/reports',
      filename: 'sse-e2e-report.html',
      expand: true
    }]
  ],
  
  // 전역 변수 설정
  globals: {
    'process.env.NODE_ENV': 'test',
    'process.env.TEST_BASE_URL': 'http://localhost:8000'
  },
  
  // 모듈 변환 설정
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // 테스트 실행 전 설정
  globalSetup: '<rootDir>/tests/JavaScript/globalSetup.js',
  globalTeardown: '<rootDir>/tests/JavaScript/globalTeardown.js',
  
  // 병렬 실행 설정 (SSE 테스트는 순차 실행 권장)
  maxWorkers: 1,
  
  // 테스트 재시도 설정
  retry: 2,
  
  // 상세 출력
  verbose: true
};