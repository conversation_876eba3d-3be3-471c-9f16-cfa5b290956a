/**
 * Jest 전역 설정 파일
 * 모든 테스트 실행 전에 한 번 실행됩니다.
 */

const { spawn } = require('child_process');
const fetch = require('node-fetch');

// 전역 설정
global.fetch = fetch;

/**
 * 전역 설정 함수
 */
module.exports = async () => {
  console.log('🔧 SSE E2E 테스트 전역 설정 시작...');
  
  const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:8000';
  
  try {
    // 서버 상태 확인
    console.log('📡 테스트 서버 상태 확인 중...');
    await checkServerStatus(baseUrl);
    
    // 테스트 데이터베이스 마이그레이션 실행
    console.log('🗄️ 테스트 데이터베이스 설정 중...');
    await runDatabaseMigrations();
    
    // Redis 연결 확인
    console.log('🔴 Redis 연결 확인 중...');
    await checkRedisConnection(baseUrl);
    
    // 테스트 라우트 등록 확인
    console.log('🛣️ 테스트 라우트 확인 중...');
    await checkTestRoutes(baseUrl);
    
    console.log('✅ SSE E2E 테스트 전역 설정 완료');
    
  } catch (error) {
    console.error('❌ 전역 설정 실패:', error.message);
    process.exit(1);
  }
};

/**
 * 서버 상태 확인
 */
async function checkServerStatus(baseUrl, maxRetries = 10, delay = 2000) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(`${baseUrl}/api/health`, {
        timeout: 5000
      });
      
      if (response.ok) {
        console.log('✅ 테스트 서버 응답 확인');
        return;
      }
    } catch (error) {
      console.log(`⏳ 서버 응답 대기 중... (${i + 1}/${maxRetries})`);
      
      if (i === maxRetries - 1) {
        throw new Error(`서버가 응답하지 않습니다: ${baseUrl}`);
      }
      
      await sleep(delay);
    }
  }
}

/**
 * 데이터베이스 마이그레이션 실행
 */
async function runDatabaseMigrations() {
  return new Promise((resolve, reject) => {
    const artisan = spawn('php', ['artisan', 'migrate:fresh', '--env=testing'], {
      cwd: process.cwd(),
      stdio: 'pipe'
    });
    
    let output = '';
    let errorOutput = '';
    
    artisan.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    artisan.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    artisan.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 데이터베이스 마이그레이션 완료');
        resolve();
      } else {
        console.error('❌ 데이터베이스 마이그레이션 실패:', errorOutput);
        reject(new Error(`마이그레이션 실패 (코드: ${code})`));
      }
    });
    
    artisan.on('error', (error) => {
      reject(new Error(`마이그레이션 실행 오류: ${error.message}`));
    });
  });
}

/**
 * Redis 연결 확인
 */
async function checkRedisConnection(baseUrl) {
  try {
    const response = await fetch(`${baseUrl}/api/test/connection-status`, {
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Redis 연결 확인됨');
      return data;
    } else {
      throw new Error(`Redis 연결 확인 실패: ${response.status}`);
    }
  } catch (error) {
    console.warn('⚠️ Redis 연결 확인 실패:', error.message);
    // Redis 연결 실패는 치명적이지 않으므로 경고만 출력
  }
}

/**
 * 테스트 라우트 확인
 */
async function checkTestRoutes(baseUrl) {
  const testRoutes = [
    '/api/test/send-notification',
    '/api/test/connection-status',
    '/api/test/performance-metrics'
  ];
  
  for (const route of testRoutes) {
    try {
      const response = await fetch(`${baseUrl}${route}`, {
        method: route.includes('send-') ? 'POST' : 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: route.includes('send-') ? JSON.stringify({
          title: '테스트',
          message: '라우트 확인용'
        }) : undefined,
        timeout: 5000
      });
      
      // 404가 아니면 라우트가 등록된 것으로 간주
      if (response.status !== 404) {
        console.log(`✅ 테스트 라우트 확인: ${route}`);
      } else {
        throw new Error(`라우트가 등록되지 않음: ${route}`);
      }
    } catch (error) {
      console.warn(`⚠️ 테스트 라우트 확인 실패: ${route} - ${error.message}`);
    }
  }
}

/**
 * 지연 함수
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 환경 변수 검증
 */
function validateEnvironment() {
  const requiredEnvVars = [
    'DB_CONNECTION',
    'REDIS_HOST'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('⚠️ 누락된 환경 변수:', missingVars.join(', '));
  }
}

// 환경 변수 검증 실행
validateEnvironment();