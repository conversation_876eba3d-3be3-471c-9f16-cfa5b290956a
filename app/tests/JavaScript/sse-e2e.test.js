/**
 * SSE 클라이언트 E2E 테스트
 * 
 * 실제 브라우저 환경에서 SSE 연결 및 메시지 수신을 테스트합니다.
 * Jest 또는 Vitest 환경에서 실행 가능합니다.
 */

// 테스트 환경 설정
const TEST_CONFIG = {
    baseUrl: process.env.TEST_BASE_URL || 'http://localhost:8000',
    sseEndpoint: '/api/sse/stream',
    timeout: 30000,
    reconnectDelay: 1000
};

// EventSource 폴리필 (Node.js 환경용)
if (typeof EventSource === 'undefined') {
    global.EventSource = require('eventsource');
}

describe('SSE E2E 통합 테스트', () => {
    let eventSource;
    let receivedMessages;
    let connectionStatus;

    beforeEach(() => {
        receivedMessages = [];
        connectionStatus = 'disconnected';
        
        // 각 테스트 전에 정리
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }
    });

    afterEach(() => {
        // 테스트 후 정리
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }
    });

    /**
     * SSE 연결 기본 테스트
     */
    test('SSE 연결이 성공적으로 설정되어야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = (event) => {
            connectionStatus = 'connected';
            expect(eventSource.readyState).toBe(EventSource.OPEN);
            done();
        };

        eventSource.onerror = (error) => {
            connectionStatus = 'error';
            done(new Error('SSE 연결 실패: ' + error.message));
        };

        // 타임아웃 설정
        setTimeout(() => {
            if (connectionStatus !== 'connected') {
                done(new Error('SSE 연결 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 메시지 수신 테스트
     */
    test('서버에서 전송된 메시지를 정상적으로 수신해야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let messageReceived = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            connectionStatus = 'connected';
            
            // 연결 후 테스트 메시지 요청
            fetch(`${TEST_CONFIG.baseUrl}/api/test/send-notification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'include',
                body: JSON.stringify({
                    title: 'E2E 테스트 알림',
                    message: '메시지 수신 테스트입니다.'
                })
            }).catch(err => {
                console.warn('테스트 메시지 전송 실패:', err);
            });
        };

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                receivedMessages.push(data);
                
                if (data.type === 'notification' && data.data.title === 'E2E 테스트 알림') {
                    messageReceived = true;
                    expect(data.data.message).toBe('메시지 수신 테스트입니다.');
                    done();
                }
            } catch (error) {
                console.error('메시지 파싱 오류:', error);
            }
        };

        eventSource.onerror = (error) => {
            if (!messageReceived) {
                done(new Error('메시지 수신 실패: ' + error.message));
            }
        };

        // 타임아웃 설정
        setTimeout(() => {
            if (!messageReceived) {
                done(new Error('메시지 수신 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 하트비트 메시지 테스트
     */
    test('하트비트 메시지를 정상적으로 수신해야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let heartbeatReceived = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.addEventListener('heartbeat', (event) => {
            heartbeatReceived = true;
            expect(event.data).toBeDefined();
            
            try {
                const data = JSON.parse(event.data);
                expect(data.timestamp).toBeDefined();
                done();
            } catch (error) {
                done(new Error('하트비트 데이터 파싱 오류: ' + error.message));
            }
        });

        eventSource.onerror = (error) => {
            if (!heartbeatReceived) {
                done(new Error('하트비트 수신 실패: ' + error.message));
            }
        };

        // 하트비트는 보통 30초 간격이므로 충분한 시간 대기
        setTimeout(() => {
            if (!heartbeatReceived) {
                done(new Error('하트비트 수신 타임아웃'));
            }
        }, 35000);
    }, 40000); // Jest 타임아웃 연장

    /**
     * 카테고리 데이터 업데이트 테스트
     */
    test('카테고리 변경 시 실시간 업데이트를 수신해야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let updateReceived = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            // 연결 후 카테고리 생성 요청
            fetch(`${TEST_CONFIG.baseUrl}/api/test/create-category`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'include',
                body: JSON.stringify({
                    name: 'E2E 테스트 카테고리',
                    description: '실시간 업데이트 테스트용'
                })
            }).catch(err => {
                console.warn('테스트 카테고리 생성 실패:', err);
            });
        };

        eventSource.addEventListener('data_update', (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.model === 'categories' && data.action === 'created') {
                    updateReceived = true;
                    expect(data.payload).toBeDefined();
                    expect(Array.isArray(data.payload.cate4)).toBe(true);
                    done();
                }
            } catch (error) {
                done(new Error('데이터 업데이트 파싱 오류: ' + error.message));
            }
        });

        eventSource.onerror = (error) => {
            if (!updateReceived) {
                done(new Error('데이터 업데이트 수신 실패: ' + error.message));
            }
        };

        setTimeout(() => {
            if (!updateReceived) {
                done(new Error('데이터 업데이트 수신 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 연결 재시도 테스트
     */
    test('연결 실패 시 자동 재연결이 작동해야 함', (done) => {
        let connectionAttempts = 0;
        let reconnected = false;
        
        const attemptConnection = () => {
            connectionAttempts++;
            const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
            
            eventSource = new EventSource(sseUrl, {
                withCredentials: true
            });

            eventSource.onopen = () => {
                if (connectionAttempts > 1) {
                    reconnected = true;
                    expect(connectionAttempts).toBeGreaterThan(1);
                    done();
                } else {
                    // 첫 번째 연결을 강제로 종료하여 재연결 테스트
                    setTimeout(() => {
                        eventSource.close();
                    }, 1000);
                }
            };

            eventSource.onerror = () => {
                if (connectionAttempts < 3 && !reconnected) {
                    setTimeout(attemptConnection, TEST_CONFIG.reconnectDelay);
                } else if (!reconnected) {
                    done(new Error('재연결 실패'));
                }
            };
        };

        attemptConnection();

        setTimeout(() => {
            if (!reconnected) {
                done(new Error('재연결 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 다중 메시지 순서 보장 테스트
     */
    test('다중 메시지가 순서대로 수신되어야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        const expectedMessages = 5;
        let receivedCount = 0;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            // 연결 후 순차적으로 메시지 전송 요청
            for (let i = 1; i <= expectedMessages; i++) {
                setTimeout(() => {
                    fetch(`${TEST_CONFIG.baseUrl}/api/test/send-notification`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            title: `순서 테스트 ${i}`,
                            message: `메시지 번호 ${i}`,
                            sequence: i
                        })
                    }).catch(err => {
                        console.warn(`메시지 ${i} 전송 실패:`, err);
                    });
                }, i * 100); // 100ms 간격으로 전송
            }
        };

        eventSource.addEventListener('notification', (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.title && data.title.startsWith('순서 테스트')) {
                    receivedCount++;
                    receivedMessages.push(data);
                    
                    if (receivedCount === expectedMessages) {
                        // 순서 확인
                        for (let i = 0; i < expectedMessages; i++) {
                            expect(receivedMessages[i].sequence).toBe(i + 1);
                        }
                        done();
                    }
                }
            } catch (error) {
                done(new Error('메시지 파싱 오류: ' + error.message));
            }
        });

        eventSource.onerror = (error) => {
            done(new Error('순서 테스트 실패: ' + error.message));
        };

        setTimeout(() => {
            if (receivedCount < expectedMessages) {
                done(new Error(`메시지 수신 불완전: ${receivedCount}/${expectedMessages}`));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 대용량 메시지 처리 테스트
     */
    test('대용량 메시지를 정상적으로 처리해야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let largeMessageReceived = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            // 대용량 데이터 포함한 메시지 전송 요청
            const largeData = {
                title: '대용량 데이터 테스트',
                message: '대용량 메시지 처리 테스트입니다.',
                payload: {
                    categories: Array.from({ length: 1000 }, (_, i) => ({
                        id: i + 1,
                        name: `카테고리 ${i + 1}`,
                        description: `테스트용 카테고리 ${i + 1}의 상세 설명입니다.`.repeat(10)
                    }))
                }
            };

            fetch(`${TEST_CONFIG.baseUrl}/api/test/send-large-notification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'include',
                body: JSON.stringify(largeData)
            }).catch(err => {
                console.warn('대용량 메시지 전송 실패:', err);
            });
        };

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.type === 'notification' && data.data.title === '대용량 데이터 테스트') {
                    largeMessageReceived = true;
                    expect(data.data.payload).toBeDefined();
                    expect(data.data.payload.categories).toBeDefined();
                    expect(data.data.payload.categories.length).toBe(1000);
                    done();
                }
            } catch (error) {
                done(new Error('대용량 메시지 파싱 오류: ' + error.message));
            }
        };

        eventSource.onerror = (error) => {
            if (!largeMessageReceived) {
                done(new Error('대용량 메시지 수신 실패: ' + error.message));
            }
        };

        setTimeout(() => {
            if (!largeMessageReceived) {
                done(new Error('대용량 메시지 수신 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 네트워크 단절 시뮬레이션 테스트
     */
    test('네트워크 단절 후 재연결이 정상적으로 작동해야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let initialConnection = false;
        let reconnectionSuccess = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            if (!initialConnection) {
                initialConnection = true;
                connectionStatus = 'connected';
                
                // 3초 후 연결 강제 종료 (네트워크 단절 시뮬레이션)
                setTimeout(() => {
                    eventSource.close();
                    connectionStatus = 'disconnected';
                    
                    // 재연결 시도
                    setTimeout(() => {
                        eventSource = new EventSource(sseUrl, {
                            withCredentials: true
                        });
                        
                        eventSource.onopen = () => {
                            reconnectionSuccess = true;
                            connectionStatus = 'connected';
                            expect(reconnectionSuccess).toBe(true);
                            done();
                        };
                        
                        eventSource.onerror = (error) => {
                            done(new Error('재연결 실패: ' + error.message));
                        };
                    }, TEST_CONFIG.reconnectDelay);
                }, 3000);
            }
        };

        eventSource.onerror = (error) => {
            if (!initialConnection) {
                done(new Error('초기 연결 실패: ' + error.message));
            }
        };

        setTimeout(() => {
            if (!reconnectionSuccess) {
                done(new Error('재연결 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });

    /**
     * 브라우저 탭 전환 시 연결 유지 테스트
     */
    test('브라우저 탭 전환 시 연결이 유지되어야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let connectionMaintained = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            connectionStatus = 'connected';
            
            // 페이지 가시성 변경 시뮬레이션
            if (typeof document !== 'undefined') {
                // 브라우저 환경에서만 실행
                Object.defineProperty(document, 'visibilityState', {
                    writable: true,
                    value: 'hidden'
                });
                
                document.dispatchEvent(new Event('visibilitychange'));
                
                setTimeout(() => {
                    Object.defineProperty(document, 'visibilityState', {
                        writable: true,
                        value: 'visible'
                    });
                    
                    document.dispatchEvent(new Event('visibilitychange'));
                    
                    // 연결 상태 확인
                    if (eventSource.readyState === EventSource.OPEN) {
                        connectionMaintained = true;
                        done();
                    } else {
                        done(new Error('탭 전환 후 연결이 끊어짐'));
                    }
                }, 2000);
            } else {
                // Node.js 환경에서는 연결 유지만 확인
                setTimeout(() => {
                    if (eventSource.readyState === EventSource.OPEN) {
                        connectionMaintained = true;
                        done();
                    } else {
                        done(new Error('연결이 유지되지 않음'));
                    }
                }, 3000);
            }
        };

        eventSource.onerror = (error) => {
            done(new Error('연결 유지 테스트 실패: ' + error.message));
        };

        setTimeout(() => {
            if (!connectionMaintained) {
                done(new Error('연결 유지 테스트 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });
});

/**
 * 성능 테스트 스위트
 */
describe('SSE 성능 테스트', () => {
    let eventSource;
    let performanceMetrics;

    beforeEach(() => {
        performanceMetrics = {
            connectionTime: 0,
            messageLatency: [],
            throughput: 0
        };
        
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }
    });

    afterEach(() => {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }
    });

    /**
     * 연결 시간 성능 테스트
     */
    test('SSE 연결 시간이 3초 이내여야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        const startTime = Date.now();
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            const connectionTime = Date.now() - startTime;
            performanceMetrics.connectionTime = connectionTime;
            
            expect(connectionTime).toBeLessThan(3000);
            console.log(`연결 시간: ${connectionTime}ms`);
            done();
        };

        eventSource.onerror = (error) => {
            done(new Error('연결 성능 테스트 실패: ' + error.message));
        };
    });

    /**
     * 메시지 처리량 성능 테스트
     */
    test('초당 100개 이상의 메시지를 처리할 수 있어야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        const messageCount = 100;
        let receivedCount = 0;
        const startTime = Date.now();
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            // 100개 메시지 빠르게 전송 요청
            for (let i = 0; i < messageCount; i++) {
                fetch(`${TEST_CONFIG.baseUrl}/api/test/send-notification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        title: `성능 테스트 ${i}`,
                        message: `처리량 테스트 메시지 ${i}`,
                        timestamp: Date.now()
                    })
                }).catch(err => {
                    console.warn(`메시지 ${i} 전송 실패:`, err);
                });
            }
        };

        eventSource.addEventListener('notification', (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.title && data.title.startsWith('성능 테스트')) {
                    receivedCount++;
                    
                    if (receivedCount === messageCount) {
                        const endTime = Date.now();
                        const totalTime = (endTime - startTime) / 1000; // 초 단위
                        const throughput = messageCount / totalTime;
                        
                        performanceMetrics.throughput = throughput;
                        
                        expect(throughput).toBeGreaterThan(100);
                        console.log(`처리량: ${throughput.toFixed(2)} 메시지/초`);
                        done();
                    }
                }
            } catch (error) {
                done(new Error('성능 테스트 메시지 파싱 오류: ' + error.message));
            }
        });

        eventSource.onerror = (error) => {
            done(new Error('처리량 성능 테스트 실패: ' + error.message));
        };

        setTimeout(() => {
            if (receivedCount < messageCount) {
                done(new Error(`처리량 테스트 불완전: ${receivedCount}/${messageCount}`));
            }
        }, 30000);
    }, 35000);

    /**
     * 메시지 지연 시간 테스트
     */
    test('메시지 지연 시간이 100ms 이내여야 함', (done) => {
        const sseUrl = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.sseEndpoint}`;
        let latencyTestCompleted = false;
        
        eventSource = new EventSource(sseUrl, {
            withCredentials: true
        });

        eventSource.onopen = () => {
            const sendTime = Date.now();
            
            fetch(`${TEST_CONFIG.baseUrl}/api/test/send-notification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'include',
                body: JSON.stringify({
                    title: '지연 시간 테스트',
                    message: '메시지 지연 시간 측정용',
                    sendTime: sendTime
                })
            }).catch(err => {
                console.warn('지연 시간 테스트 메시지 전송 실패:', err);
            });
        };

        eventSource.addEventListener('notification', (event) => {
            try {
                const data = JSON.parse(event.data);
                
                if (data.title === '지연 시간 테스트') {
                    const receiveTime = Date.now();
                    const latency = receiveTime - data.sendTime;
                    
                    performanceMetrics.messageLatency.push(latency);
                    latencyTestCompleted = true;
                    
                    expect(latency).toBeLessThan(100);
                    console.log(`메시지 지연 시간: ${latency}ms`);
                    done();
                }
            } catch (error) {
                done(new Error('지연 시간 테스트 파싱 오류: ' + error.message));
            }
        });

        eventSource.onerror = (error) => {
            if (!latencyTestCompleted) {
                done(new Error('지연 시간 테스트 실패: ' + error.message));
            }
        };

        setTimeout(() => {
            if (!latencyTestCompleted) {
                done(new Error('지연 시간 테스트 타임아웃'));
            }
        }, TEST_CONFIG.timeout);
    });
});