<?php

namespace Tests\Unit;

use App\Listeners\DeliverOfflineNotifications;
use App\Models\User;
use App\Services\NotificationManager;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

/**
 * DeliverOfflineNotifications 리스너 단위 테스트
 */
class DeliverOfflineNotificationsListenerTest extends TestCase
{
    use RefreshDatabase;

    private DeliverOfflineNotifications $listener;
    private NotificationManager $notificationManager;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock NotificationManager
        $this->notificationManager = Mockery::mock(NotificationManager::class);
        
        // 리스너 인스턴스 생성
        $this->listener = new DeliverOfflineNotifications($this->notificationManager);

        // 테스트용 사용자 생성
        $this->user = User::factory()->create();

        // Log 파사드 모킹
        Log::spy();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 로그인 이벤트 처리 시 오프라인 알림 전송 테스트
     */
    public function test_handles_login_event_and_delivers_notifications(): void
    {
        // NotificationManager 모킹
        $this->notificationManager
            ->shouldReceive('deliverOfflineNotifications')
            ->once()
            ->with($this->user->id)
            ->andReturn([
                'total_notifications' => 3,
                'delivered_count' => 3,
                'failed_count' => 0
            ]);

        // 로그인 이벤트 생성
        $loginEvent = new Login('web', $this->user, false);

        // 리스너 실행
        $this->listener->handle($loginEvent);

        // 로그 기록 확인
        Log::shouldHaveReceived('info')
            ->with('사용자 로그인 감지, 오프라인 알림 전송 시작', [
                'user_id' => $this->user->id,
                'username' => $this->user->username ?? 'unknown'
            ]);

        Log::shouldHaveReceived('info')
            ->with('로그인 시 오프라인 알림 전송 완료', [
                'user_id' => $this->user->id,
                'total_notifications' => 3,
                'delivered_count' => 3,
                'failed_count' => 0
            ]);
    }

    /**
     * 알림이 없는 경우 처리 테스트
     */
    public function test_handles_login_event_with_no_notifications(): void
    {
        // NotificationManager 모킹 (알림 없음)
        $this->notificationManager
            ->shouldReceive('deliverOfflineNotifications')
            ->once()
            ->with($this->user->id)
            ->andReturn([
                'total_notifications' => 0,
                'delivered_count' => 0,
                'failed_count' => 0
            ]);

        // 로그인 이벤트 생성
        $loginEvent = new Login('web', $this->user, false);

        // 리스너 실행
        $this->listener->handle($loginEvent);

        // 시작 로그만 기록되고 완료 로그는 기록되지 않아야 함
        Log::shouldHaveReceived('info')
            ->with('사용자 로그인 감지, 오프라인 알림 전송 시작', Mockery::any());

        Log::shouldNotHaveReceived('info')
            ->with('로그인 시 오프라인 알림 전송 완료', Mockery::any());
    }

    /**
     * 유효하지 않은 사용자 처리 테스트
     */
    public function test_handles_invalid_user(): void
    {
        // 유효하지 않은 사용자로 로그인 이벤트 생성
        $invalidUser = new User();
        $loginEvent = new Login('web', $invalidUser, false);

        // NotificationManager는 호출되지 않아야 함
        $this->notificationManager
            ->shouldNotReceive('deliverOfflineNotifications');

        // 리스너 실행
        $this->listener->handle($loginEvent);

        // 경고 로그 확인
        Log::shouldHaveReceived('warning')
            ->with('로그인 이벤트에서 유효하지 않은 사용자 정보');
    }

    /**
     * 알림 전송 중 예외 발생 처리 테스트
     */
    public function test_handles_exception_during_notification_delivery(): void
    {
        $exception = new \Exception('알림 전송 실패');

        // NotificationManager에서 예외 발생
        $this->notificationManager
            ->shouldReceive('deliverOfflineNotifications')
            ->once()
            ->with($this->user->id)
            ->andThrow($exception);

        // 로그인 이벤트 생성
        $loginEvent = new Login('web', $this->user, false);

        // 리스너 실행 (예외가 발생해도 중단되지 않아야 함)
        $this->listener->handle($loginEvent);

        // 오류 로그 확인
        Log::shouldHaveReceived('error')
            ->with('로그인 시 오프라인 알림 전송 중 오류 발생', [
                'user_id' => $this->user->id,
                'error' => '알림 전송 실패',
                'trace' => $exception->getTraceAsString()
            ]);
    }

    /**
     * 작업 실패 처리 테스트
     */
    public function test_handles_job_failure(): void
    {
        $exception = new \Exception('큐 작업 실패');
        $loginEvent = new Login('web', $this->user, false);

        // failed 메서드 실행
        $this->listener->failed($loginEvent, $exception);

        // 실패 로그 확인
        Log::shouldHaveReceived('error')
            ->with('오프라인 알림 전송 작업 실패', [
                'user_id' => $this->user->id,
                'error' => '큐 작업 실패'
            ]);
    }

    /**
     * 사용자명이 없는 경우 처리 테스트
     */
    public function test_handles_user_without_username(): void
    {
        // 사용자명이 없는 사용자 생성
        $userWithoutUsername = User::factory()->create(['username' => null]);

        // NotificationManager 모킹
        $this->notificationManager
            ->shouldReceive('deliverOfflineNotifications')
            ->once()
            ->with($userWithoutUsername->id)
            ->andReturn([
                'total_notifications' => 1,
                'delivered_count' => 1,
                'failed_count' => 0
            ]);

        // 로그인 이벤트 생성
        $loginEvent = new Login('web', $userWithoutUsername, false);

        // 리스너 실행
        $this->listener->handle($loginEvent);

        // 로그에서 'unknown'이 사용되는지 확인
        Log::shouldHaveReceived('info')
            ->with('사용자 로그인 감지, 오프라인 알림 전송 시작', [
                'user_id' => $userWithoutUsername->id,
                'username' => 'unknown'
            ]);
    }

    /**
     * 부분적 전송 실패 처리 테스트
     */
    public function test_handles_partial_delivery_failure(): void
    {
        // NotificationManager 모킹 (일부 실패)
        $this->notificationManager
            ->shouldReceive('deliverOfflineNotifications')
            ->once()
            ->with($this->user->id)
            ->andReturn([
                'total_notifications' => 5,
                'delivered_count' => 3,
                'failed_count' => 2
            ]);

        // 로그인 이벤트 생성
        $loginEvent = new Login('web', $this->user, false);

        // 리스너 실행
        $this->listener->handle($loginEvent);

        // 완료 로그에 실패 정보도 포함되는지 확인
        Log::shouldHaveReceived('info')
            ->with('로그인 시 오프라인 알림 전송 완료', [
                'user_id' => $this->user->id,
                'total_notifications' => 5,
                'delivered_count' => 3,
                'failed_count' => 2
            ]);
    }
}