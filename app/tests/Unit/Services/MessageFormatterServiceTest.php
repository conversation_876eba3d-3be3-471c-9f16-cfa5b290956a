<?php

namespace Tests\Unit\Services;

use App\Services\MessageFormatterService;
use Carbon\Carbon;
use Tests\TestCase;

/**
 * MessageFormatterService 단위 테스트
 */
class MessageFormatterServiceTest extends TestCase
{
    private MessageFormatterService $messageFormatter;

    protected function setUp(): void
    {
        parent::setUp();
        $this->messageFormatter = new MessageFormatterService();
        
        // 테스트용 고정 시간 설정 (UTC)
        Carbon::setTestNow(Carbon::parse('2025-01-11 10:00:00', 'UTC'));
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        parent::tearDown();
    }

    /** @test */
    public function 알림_메시지를_표준_형식으로_포맷팅할_수_있다()
    {
        // Given
        $data = [
            'id' => 'test_notification_1',
            'title' => '테스트 알림',
            'message' => '테스트 메시지입니다.',
            'category' => 'system',
            'metadata' => ['source' => 'test']
        ];
        $priority = 'high';
        $actionUrl = '/notifications/1';

        // When
        $result = $this->messageFormatter->formatNotificationMessage($data, $priority, $actionUrl);

        // Then
        $this->assertEquals('notification', $result['type']);
        $this->assertEquals('2025-01-11T10:00:00.000000Z', $result['timestamp']);
        $this->assertEquals('test_notification_1', $result['data']['id']);
        $this->assertEquals('테스트 알림', $result['data']['title']);
        $this->assertEquals('테스트 메시지입니다.', $result['data']['message']);
        $this->assertEquals('high', $result['data']['priority']);
        $this->assertEquals('system', $result['data']['category']);
        $this->assertEquals('/notifications/1', $result['data']['action_url']);
        $this->assertEquals(['source' => 'test'], $result['data']['metadata']);
    }

    /** @test */
    public function 알림_메시지_기본값이_적용된다()
    {
        // Given
        $data = ['message' => '기본값 테스트'];

        // When
        $result = $this->messageFormatter->formatNotificationMessage($data);

        // Then
        $this->assertEquals('notification', $result['type']);
        $this->assertEquals('알림', $result['data']['title']);
        $this->assertEquals('normal', $result['data']['priority']);
        $this->assertEquals('general', $result['data']['category']);
        $this->assertArrayNotHasKey('action_url', $result['data']); // null 값은 제거됨
        $this->assertArrayHasKey('id', $result['data']);
        $this->assertStringStartsWith('msg_', $result['data']['id']);
    }

    /** @test */
    public function 잘못된_우선순위가_기본값으로_변경된다()
    {
        // Given
        $data = ['message' => '우선순위 테스트'];
        $invalidPriority = 'invalid_priority';

        // When
        $result = $this->messageFormatter->formatNotificationMessage($data, $invalidPriority);

        // Then
        $this->assertEquals('normal', $result['data']['priority']);
    }

    /** @test */
    public function 데이터_업데이트_메시지를_표준_형식으로_포맷팅할_수_있다()
    {
        // Given
        $model = 'categories';
        $action = 'updated';
        $payload = [
            'cate4' => [
                ['id' => 1, 'name' => '카테고리 1'],
                ['id' => 2, 'name' => '카테고리 2']
            ],
            'cate5' => [
                ['id' => 1, 'name' => '하위 카테고리 1']
            ]
        ];
        $metadata = ['user_id' => 123];

        // When
        $result = $this->messageFormatter->formatDataUpdateMessage($model, $action, $payload, $metadata);

        // Then
        $this->assertEquals('data_update', $result['type']);
        $this->assertEquals('2025-01-11T10:00:00.000000Z', $result['timestamp']);
        $this->assertEquals('categories', $result['data']['model']);
        $this->assertEquals('updated', $result['data']['action']);
        $this->assertEquals($payload, $result['data']['payload']);
        $this->assertEquals(2, $result['data']['affected_count']); // cate4 배열의 개수
        $this->assertEquals(['user_id' => 123], $result['data']['metadata']);
        $this->assertArrayHasKey('checksum', $result['data']);
    }

    /** @test */
    public function 잘못된_액션이_기본값으로_변경된다()
    {
        // Given
        $model = 'test_model';
        $invalidAction = 'invalid_action';
        $payload = ['data' => 'test'];

        // When
        $result = $this->messageFormatter->formatDataUpdateMessage($model, $invalidAction, $payload);

        // Then
        $this->assertEquals('updated', $result['data']['action']);
    }

    /** @test */
    public function 시스템_상태_메시지를_표준_형식으로_포맷팅할_수_있다()
    {
        // Given
        $status = 'maintenance';
        $data = [
            'message' => '시스템 점검 중입니다.',
            'estimated_duration' => '30분',
            'affected_services' => ['api', 'web'],
            'contact_info' => '<EMAIL>'
        ];

        // When
        $result = $this->messageFormatter->formatSystemStatusMessage($status, $data);

        // Then
        $this->assertEquals('system_status', $result['type']);
        $this->assertEquals('2025-01-11T10:00:00.000000Z', $result['timestamp']);
        $this->assertEquals('maintenance', $result['data']['status']);
        $this->assertEquals('시스템 점검 중입니다.', $result['data']['message']);
        $this->assertEquals('30분', $result['data']['estimated_duration']);
        $this->assertEquals(['api', 'web'], $result['data']['affected_services']);
        $this->assertEquals('<EMAIL>', $result['data']['contact_info']);
    }

    /** @test */
    public function 잘못된_시스템_상태가_기본값으로_변경된다()
    {
        // Given
        $invalidStatus = 'invalid_status';

        // When
        $result = $this->messageFormatter->formatSystemStatusMessage($invalidStatus);

        // Then
        $this->assertEquals('online', $result['data']['status']);
    }

    /** @test */
    public function 하트비트_메시지를_포맷팅할_수_있다()
    {
        // Given
        $serverInfo = [
            'connection_count' => 150,
            'server_load' => 0.75
        ];

        // When
        $result = $this->messageFormatter->formatHeartbeatMessage($serverInfo);

        // Then
        $this->assertEquals('heartbeat', $result['type']);
        $this->assertEquals('2025-01-11T10:00:00.000000Z', $result['timestamp']);
        $this->assertEquals('2025-01-11T10:00:00.000000Z', $result['data']['server_time']);
        $this->assertEquals(150, $result['data']['connection_count']);
        $this->assertEquals(0.75, $result['data']['server_load']);
    }

    /** @test */
    public function 사용자_정의_메시지를_포맷팅할_수_있다()
    {
        // Given
        $type = 'custom_event';
        $data = ['event_data' => 'test_value'];
        $options = [
            'ttl' => 3600,
            'priority' => 'urgent'
        ];

        // When
        $result = $this->messageFormatter->formatCustomMessage($type, $data, $options);

        // Then
        $this->assertEquals('custom_event', $result['type']);
        $this->assertEquals('2025-01-11T10:00:00.000000Z', $result['timestamp']);
        $this->assertEquals(['event_data' => 'test_value'], $result['data']);
        $this->assertEquals('urgent', $result['priority']);
        $this->assertArrayHasKey('expires_at', $result);
    }

    /** @test */
    public function 메시지_타입이_정리된다()
    {
        // Given
        $invalidType = 'invalid-type!@#';
        $data = ['test' => 'data'];

        // When
        $result = $this->messageFormatter->formatCustomMessage($invalidType, $data);

        // Then
        $this->assertEquals('invalidtype', $result['type']);
    }

    /** @test */
    public function 메시지를_JSON으로_직렬화할_수_있다()
    {
        // Given
        $message = [
            'type' => 'notification',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => [
                'title' => '테스트 알림',
                'message' => '직렬화 테스트'
            ]
        ];

        // When
        $result = $this->messageFormatter->serializeMessage($message);

        // Then
        $this->assertIsString($result);
        $decoded = json_decode($result, true);
        $this->assertEquals($message, $decoded);
    }

    /** @test */
    public function JSON_문자열을_메시지로_역직렬화할_수_있다()
    {
        // Given
        $message = [
            'type' => 'notification',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => [
                'title' => '테스트 알림',
                'message' => '역직렬화 테스트'
            ]
        ];
        $jsonString = json_encode($message);

        // When
        $result = $this->messageFormatter->deserializeMessage($jsonString);

        // Then
        $this->assertEquals($message, $result);
    }

    /** @test */
    public function 잘못된_JSON_문자열_역직렬화_시_예외가_발생한다()
    {
        // Given
        $invalidJson = '{"invalid": json}';

        // When & Then
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('JSON 역직렬화 실패');
        
        $this->messageFormatter->deserializeMessage($invalidJson);
    }

    /** @test */
    public function 필수_필드가_없는_메시지_직렬화_시_예외가_발생한다()
    {
        // Given
        $invalidMessage = [
            'data' => ['test' => 'data']
            // type과 timestamp 누락
        ];

        // When & Then
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('메시지 타입이 누락되었습니다');
        
        $this->messageFormatter->serializeMessage($invalidMessage);
    }

    /** @test */
    public function null_값이_제거된다()
    {
        // Given
        $data = [
            'title' => '테스트 알림',
            'message' => '메시지',
            'action_url' => null,
            'metadata' => [
                'key1' => 'value1',
                'key2' => null
            ]
        ];

        // When
        $result = $this->messageFormatter->formatNotificationMessage($data);

        // Then
        $this->assertArrayNotHasKey('action_url', $result['data']);
        $this->assertArrayNotHasKey('key2', $result['data']['metadata']);
        $this->assertArrayHasKey('key1', $result['data']['metadata']);
    }

    /** @test */
    public function 긴_문자열이_압축된다()
    {
        // Given
        $longString = str_repeat('A', 1500); // 1.5KB 문자열
        $data = [
            'title' => '긴 메시지 테스트',
            'message' => $longString
        ];

        // When
        $result = $this->messageFormatter->formatNotificationMessage($data);

        // Then
        $this->assertStringContainsString('[truncated]', $result['data']['message']);
        $this->assertLessThan(600, strlen($result['data']['message'])); // 500자 + "[truncated]"
    }

    /** @test */
    public function 체크섬이_올바르게_생성된다()
    {
        // Given
        $payload1 = ['data' => 'test1'];
        $payload2 = ['data' => 'test2'];

        // When
        $result1 = $this->messageFormatter->formatDataUpdateMessage('model', 'updated', $payload1);
        $result2 = $this->messageFormatter->formatDataUpdateMessage('model', 'updated', $payload2);
        $result3 = $this->messageFormatter->formatDataUpdateMessage('model', 'updated', $payload1);

        // Then
        $this->assertNotEquals($result1['data']['checksum'], $result2['data']['checksum']);
        $this->assertEquals($result1['data']['checksum'], $result3['data']['checksum']);
    }

    /** @test */
    public function 영향받은_항목_수가_올바르게_계산된다()
    {
        // Given
        $singleItemPayload = ['id' => 1, 'name' => 'test'];
        $multipleItemsPayload = [
            ['id' => 1, 'name' => 'test1'],
            ['id' => 2, 'name' => 'test2'],
            ['id' => 3, 'name' => 'test3']
        ];

        // When
        $result1 = $this->messageFormatter->formatDataUpdateMessage('model', 'updated', $singleItemPayload);
        $result2 = $this->messageFormatter->formatDataUpdateMessage('model', 'updated', $multipleItemsPayload);

        // Then
        $this->assertEquals(1, $result1['data']['affected_count']);
        $this->assertEquals(3, $result2['data']['affected_count']);
    }
}