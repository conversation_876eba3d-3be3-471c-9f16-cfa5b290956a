<?php

namespace Tests\Unit\Services;

use App\Services\ErrorRecoveryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Exception;
use Mockery;

/**
 * ErrorRecoveryService 단위 테스트
 */
class ErrorRecoveryServiceTest extends TestCase
{
    use RefreshDatabase;

    private ErrorRecoveryService $errorRecoveryService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->errorRecoveryService = new ErrorRecoveryService();
        
        // Redis 기본 모킹
        Redis::shouldReceive('del')->byDefault()->andReturn(1);
        Redis::shouldReceive('srem')->byDefault()->andReturn(1);
        Redis::shouldReceive('get')->byDefault()->andReturn(null);
        Redis::shouldReceive('setex')->byDefault()->andReturn(true);
        Redis::shouldReceive('sadd')->byDefault()->andReturn(1);
        Redis::shouldReceive('ping')->byDefault()->andReturn('PONG');
        
        // Cache 기본 모킹
        Cache::shouldReceive('put')->byDefault()->andReturn(true);
        Cache::shouldReceive('get')->byDefault()->andReturn(false);
        Cache::shouldReceive('forget')->byDefault()->andReturn(true);
    }

    protected function tearDown(): void
    {
        // 메모리 저장소 정리
        $this->errorRecoveryService->clearMemoryStorage();
        $this->errorRecoveryService->resetErrorCounters();
        
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     * 연결 오류 처리 기본 기능 테스트
     */
    public function 연결_오류_처리_기본_기능_테스트(): void
    {
        // Given: 연결 오류 상황
        $connectionId = 'test_connection_123';
        $exception = new Exception('Connection timeout');

        // Redis 연결 복구 성공 시뮬레이션
        Redis::shouldReceive('get')
            ->with("sse:connections:{$connectionId}")
            ->once()
            ->andReturn(json_encode(['id' => $connectionId, 'status' => 'active']));

        // When: 연결 오류 처리
        $result = $this->errorRecoveryService->handleConnectionError($connectionId, $exception);

        // Then: 복구가 성공했는지 확인
        $this->assertTrue($result);
    }

    /**
     * @test
     * 연결 오류 처리 실패 시 폴백 테스트
     */
    public function 연결_오류_처리_실패_시_폴백_테스트(): void
    {
        // Given: 연결 복구 실패 상황
        $connectionId = 'failed_connection_456';
        $exception = new Exception('Connection permanently lost');

        // Redis 연결 복구 실패 시뮬레이션
        Redis::shouldReceive('get')
            ->with("sse:connections:{$connectionId}")
            ->andReturn(null);

        // When: 연결 오류 처리
        $result = $this->errorRecoveryService->handleConnectionError($connectionId, $exception);

        // Then: 복구가 실패했는지 확인
        $this->assertFalse($result);
    }

    /**
     * @test
     * 메시지 전송 오류 처리 테스트
     */
    public function 메시지_전송_오류_처리_테스트(): void
    {
        // Given: 메시지 전송 오류 상황
        $message = [
            'type' => 'notification',
            'data' => ['title' => 'Test Message', 'content' => 'Test Content']
        ];
        $exception = new Exception('Message send failed');

        // When: 메시지 전송 오류 처리
        $result = $this->errorRecoveryService->handleMessageSendError($message, $exception);

        // Then: 메시지가 처리되었는지 확인 (재전송 성공)
        $this->assertTrue($result);
    }

    /**
     * @test
     * 잘못된 메시지 전송 오류 처리 테스트
     */
    public function 잘못된_메시지_전송_오류_처리_테스트(): void
    {
        // Given: 잘못된 메시지 데이터
        $invalidMessage = []; // type 필드 없음
        $exception = new Exception('Invalid message format');

        // When: 잘못된 메시지 전송 오류 처리
        $result = $this->errorRecoveryService->handleMessageSendError($invalidMessage, $exception);

        // Then: 처리가 실패했는지 확인
        $this->assertFalse($result);
    }

    /**
     * @test
     * Redis 오류 처리 및 복구 테스트
     */
    public function Redis_오류_처리_및_복구_테스트(): void
    {
        // Given: Redis 연결 오류 상황
        $exception = new Exception('Redis connection lost');

        // Redis 복구 성공 시뮬레이션
        Redis::shouldReceive('ping')
            ->once()
            ->andReturn('PONG');

        // When: Redis 오류 처리
        $result = $this->errorRecoveryService->handleRedisError($exception);

        // Then: 복구가 성공했는지 확인
        $this->assertTrue($result);
    }

    /**
     * @test
     * Redis 오류 처리 실패 테스트
     */
    public function Redis_오류_처리_실패_테스트(): void
    {
        // Given: Redis 연결 복구 실패 상황
        $exception = new Exception('Redis server down');

        // Redis 복구 실패 시뮬레이션
        Redis::shouldReceive('ping')
            ->andThrow(new Exception('Redis still unavailable'));

        // When: Redis 오류 처리
        $result = $this->errorRecoveryService->handleRedisError($exception);

        // Then: 복구가 실패했는지 확인
        $this->assertFalse($result);
    }

    /**
     * @test
     * 폴백 모드 활성화 테스트
     */
    public function 폴백_모드_활성화_테스트(): void
    {
        // Given: 폴백 모드 비활성 상태
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->once()
            ->andReturn(false);

        $this->assertFalse($this->errorRecoveryService->isFallbackModeActive());

        // When: 폴백 모드 활성화
        Cache::shouldReceive('put')
            ->with('sse_fallback_mode', true, Mockery::any())
            ->once()
            ->andReturn(true);

        $this->errorRecoveryService->activateFallbackMode();

        // Then: 폴백 모드가 활성화되었는지 확인
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->once()
            ->andReturn(true);

        $this->assertTrue($this->errorRecoveryService->isFallbackModeActive());
    }

    /**
     * @test
     * 폴백 모드 해제 테스트
     */
    public function 폴백_모드_해제_테스트(): void
    {
        // Given: 폴백 모드 활성 상태
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->andReturn(true, true, false); // 첫 번째: 확인, 두 번째: deactivate 내부, 세 번째: 최종 확인

        $this->assertTrue($this->errorRecoveryService->isFallbackModeActive());

        // When: 폴백 모드 해제
        Cache::shouldReceive('forget')
            ->with('sse_fallback_mode')
            ->once()
            ->andReturn(true);

        $this->errorRecoveryService->deactivateFallbackMode();

        // Then: 폴백 모드가 해제되었는지 확인
        $this->assertFalse($this->errorRecoveryService->isFallbackModeActive());
    }

    /**
     * @test
     * 메모리 저장소 연결 정보 저장 테스트
     */
    public function 메모리_저장소_연결_정보_저장_테스트(): void
    {
        // Given: 폴백 모드 활성화
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->andReturn(true);

        $connectionId = 'memory_test_connection';
        $connectionData = [
            'user_id' => 123,
            'ip_address' => '***********',
            'connected_at' => '2025-01-11T10:00:00Z'
        ];

        // When: 메모리 저장소에 연결 정보 저장
        $result = $this->errorRecoveryService->storeConnectionInMemory($connectionId, $connectionData);

        // Then: 저장이 성공했는지 확인
        $this->assertTrue($result);

        // 저장된 데이터 조회 확인
        $storedData = $this->errorRecoveryService->getConnectionFromMemory($connectionId);
        $this->assertNotNull($storedData);
        $this->assertEquals(123, $storedData['user_id']);
        $this->assertEquals('***********', $storedData['ip_address']);
        $this->assertArrayHasKey('stored_at', $storedData);
    }

    /**
     * @test
     * 폴백 모드 비활성 시 메모리 저장소 사용 불가 테스트
     */
    public function 폴백_모드_비활성_시_메모리_저장소_사용_불가_테스트(): void
    {
        // Given: 폴백 모드 비활성 상태
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->andReturn(false);

        $connectionId = 'inactive_test_connection';
        $connectionData = ['user_id' => 456];

        // When: 메모리 저장소에 연결 정보 저장 시도
        $storeResult = $this->errorRecoveryService->storeConnectionInMemory($connectionId, $connectionData);
        $retrieveResult = $this->errorRecoveryService->getConnectionFromMemory($connectionId);

        // Then: 저장 및 조회가 실패했는지 확인
        $this->assertFalse($storeResult);
        $this->assertNull($retrieveResult);
    }

    /**
     * @test
     * 복구 상태 조회 테스트
     */
    public function 복구_상태_조회_테스트(): void
    {
        // Given: 다양한 상태 설정
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->once()
            ->andReturn(true);

        Cache::shouldReceive('get')
            ->with('last_recovery_check')
            ->once()
            ->andReturn('2025-01-11T10:00:00Z');

        Redis::shouldReceive('ping')
            ->once()
            ->andReturn('PONG');

        // When: 복구 상태 조회
        $status = $this->errorRecoveryService->getRecoveryStatus();

        // Then: 상태 정보가 올바르게 반환되었는지 확인
        $this->assertIsArray($status);
        $this->assertArrayHasKey('fallback_mode_active', $status);
        $this->assertArrayHasKey('error_counters', $status);
        $this->assertArrayHasKey('memory_storage_size', $status);
        $this->assertArrayHasKey('redis_status', $status);
        $this->assertArrayHasKey('last_recovery_check', $status);
        $this->assertArrayHasKey('generated_at', $status);

        $this->assertTrue($status['fallback_mode_active']);
        $this->assertEquals('connected', $status['redis_status']);
        $this->assertIsArray($status['error_counters']);
        $this->assertIsArray($status['memory_storage_size']);
    }

    /**
     * @test
     * 복구 상태 조회 오류 처리 테스트
     */
    public function 복구_상태_조회_오류_처리_테스트(): void
    {
        // Given: Cache 오류 상황
        Cache::shouldReceive('get')
            ->andThrow(new Exception('Cache error'));

        // When: 복구 상태 조회
        $status = $this->errorRecoveryService->getRecoveryStatus();

        // Then: 오류 상태가 반환되었는지 확인
        $this->assertIsArray($status);
        $this->assertArrayHasKey('error', $status);
        $this->assertFalse($status['fallback_mode_active']);
        $this->assertEquals('error', $status['redis_status']);
    }

    /**
     * @test
     * 오류 카운터 리셋 테스트
     */
    public function 오류_카운터_리셋_테스트(): void
    {
        // Given: 오류 발생으로 카운터 증가 시뮬레이션
        $exception = new Exception('Test error');
        
        // Redis 오류 여러 번 발생
        Redis::shouldReceive('ping')->andThrow($exception);
        $this->errorRecoveryService->handleRedisError($exception);
        $this->errorRecoveryService->handleRedisError($exception);

        // When: 특정 타입 오류 카운터 리셋
        $this->errorRecoveryService->resetErrorCounters('redis');

        // Then: 해당 타입 카운터가 리셋되었는지 확인
        $status = $this->errorRecoveryService->getRecoveryStatus();
        $this->assertEquals(0, $status['error_counters']['redis']);
    }

    /**
     * @test
     * 전체 오류 카운터 리셋 테스트
     */
    public function 전체_오류_카운터_리셋_테스트(): void
    {
        // Given: 다양한 오류 발생
        $exception = new Exception('Test error');
        
        Redis::shouldReceive('ping')->andThrow($exception);
        Redis::shouldReceive('get')->andReturn(null);
        
        $this->errorRecoveryService->handleRedisError($exception);
        $this->errorRecoveryService->handleConnectionError('test_conn', $exception);
        $this->errorRecoveryService->handleMessageSendError([], $exception);

        // When: 전체 오류 카운터 리셋
        $this->errorRecoveryService->resetErrorCounters();

        // Then: 모든 카운터가 리셋되었는지 확인
        $status = $this->errorRecoveryService->getRecoveryStatus();
        foreach ($status['error_counters'] as $count) {
            $this->assertEquals(0, $count);
        }
    }

    /**
     * @test
     * 메모리 저장소 정리 테스트
     */
    public function 메모리_저장소_정리_테스트(): void
    {
        // Given: 폴백 모드 활성화 및 데이터 저장
        Cache::shouldReceive('get')
            ->with('sse_fallback_mode', false)
            ->andReturn(true);

        $this->errorRecoveryService->storeConnectionInMemory('test_conn', ['data' => 'test']);

        // 데이터가 저장되었는지 확인
        $this->assertNotNull($this->errorRecoveryService->getConnectionFromMemory('test_conn'));

        // When: 메모리 저장소 정리
        $this->errorRecoveryService->clearMemoryStorage();

        // Then: 데이터가 정리되었는지 확인
        $this->assertNull($this->errorRecoveryService->getConnectionFromMemory('test_conn'));
    }

    /**
     * @test
     * 다중 Redis 오류 시 폴백 모드 자동 활성화 테스트
     */
    public function 다중_Redis_오류_시_폴백_모드_자동_활성화_테스트(): void
    {
        // Given: Redis 연결 실패 상황
        $exception = new Exception('Redis connection failed');
        Redis::shouldReceive('ping')->andThrow($exception);

        // 폴백 모드 활성화 모킹 (여러 번 호출될 수 있음)
        Cache::shouldReceive('put')
            ->with('sse_fallback_mode', true, Mockery::any())
            ->atLeast()->once();

        // When: 임계값(10회) 이상의 Redis 오류 발생
        for ($i = 0; $i < 12; $i++) {
            $this->errorRecoveryService->handleRedisError($exception);
        }

        // Then: 폴백 모드가 활성화되었는지 확인 (모킹을 통해 검증)
        // 실제로는 activateFallbackMode가 호출되었는지 확인
        $this->assertTrue(true); // 모킹이 올바르게 호출되면 테스트 통과
    }

    /**
     * @test
     * 재시도 메커니즘 테스트
     */
    public function 재시도_메커니즘_테스트(): void
    {
        // Given: 처음 2번은 실패, 3번째는 성공하는 상황
        $connectionId = 'retry_test_connection';
        $exception = new Exception('Temporary failure');

        Redis::shouldReceive('get')
            ->with("sse:connections:{$connectionId}")
            ->andReturn(null, null, json_encode(['id' => $connectionId, 'status' => 'active']));

        // When: 연결 오류 처리 (재시도 포함)
        $result = $this->errorRecoveryService->handleConnectionError($connectionId, $exception);

        // Then: 재시도를 통해 복구가 성공했는지 확인
        $this->assertTrue($result);
    }
}