<?php

namespace Tests\Unit\Services;

use App\Services\ConnectionManager;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Carbon\Carbon;
use Exception;

class ConnectionManagerTest extends TestCase
{
    use RefreshDatabase;

    private ConnectionManager $connectionManager;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->connectionManager = new ConnectionManager();
        $this->user = User::factory()->create();
        
        // Redis 초기화
        Redis::flushdb();
    }

    protected function tearDown(): void
    {
        // 테스트 후 Redis 정리 (모킹되지 않은 경우에만)
        try {
            Redis::flushdb();
        } catch (\Exception $e) {
            // 모킹된 경우 무시
        }
        parent::tearDown();
    }

    /** @test */
    public function 연결을_추가할_수_있다(): void
    {
        $connectionId = 'test-connection-1';
        $metadata = [
            'ip_address' => '***********',
            'user_agent' => 'Mozilla/5.0 Test Browser',
            'subscriptions' => ['notifications', 'categories']
        ];

        $this->connectionManager->addConnection($connectionId, $this->user->id, $metadata);

        // 연결이 활성 연결 목록에 추가되었는지 확인
        $activeConnections = $this->connectionManager->getActiveConnections();
        $this->assertContains($connectionId, $activeConnections);

        // 사용자별 연결 매핑이 생성되었는지 확인
        $userConnections = $this->connectionManager->getUserConnections($this->user->id);
        $this->assertContains($connectionId, $userConnections);

        // 연결 정보가 올바르게 저장되었는지 확인
        $connectionData = $this->connectionManager->getConnection($connectionId);
        $this->assertNotNull($connectionData);
        $this->assertEquals($connectionId, $connectionData['connection_id']);
        $this->assertEquals($this->user->id, $connectionData['user_id']);
        $this->assertEquals('***********', $connectionData['ip_address']);
        $this->assertTrue($connectionData['is_authenticated']);
        $this->assertEquals(['notifications', 'categories'], $connectionData['subscriptions']);
    }

    /** @test */
    public function 게스트_연결을_추가할_수_있다(): void
    {
        $connectionId = 'guest-connection-1';
        $metadata = [
            'ip_address' => '***********',
            'user_agent' => 'Mozilla/5.0 Guest Browser'
        ];

        $this->connectionManager->addConnection($connectionId, null, $metadata);

        // 연결이 활성 연결 목록에 추가되었는지 확인
        $activeConnections = $this->connectionManager->getActiveConnections();
        $this->assertContains($connectionId, $activeConnections);

        // 연결 정보 확인
        $connectionData = $this->connectionManager->getConnection($connectionId);
        $this->assertNotNull($connectionData);
        $this->assertNull($connectionData['user_id']);
        $this->assertFalse($connectionData['is_authenticated']);
        $this->assertEquals(['notifications'], $connectionData['subscriptions']);
    }

    /** @test */
    public function 연결을_제거할_수_있다(): void
    {
        $connectionId = 'test-connection-2';
        
        // 연결 추가
        $this->connectionManager->addConnection($connectionId, $this->user->id);
        
        // 연결이 추가되었는지 확인
        $this->assertContains($connectionId, $this->connectionManager->getActiveConnections());
        $this->assertContains($connectionId, $this->connectionManager->getUserConnections($this->user->id));

        // 연결 제거
        $this->connectionManager->removeConnection($connectionId);

        // 연결이 제거되었는지 확인
        $this->assertNotContains($connectionId, $this->connectionManager->getActiveConnections());
        $this->assertNotContains($connectionId, $this->connectionManager->getUserConnections($this->user->id));
        $this->assertNull($this->connectionManager->getConnection($connectionId));
    }

    /** @test */
    public function 사용자별_다중_디바이스_연결을_관리할_수_있다(): void
    {
        $connection1 = 'user-device-1';
        $connection2 = 'user-device-2';
        $connection3 = 'user-device-3';

        // 같은 사용자의 여러 디바이스 연결 추가
        $this->connectionManager->addConnection($connection1, $this->user->id, ['ip_address' => '***********0']);
        $this->connectionManager->addConnection($connection2, $this->user->id, ['ip_address' => '************']);
        $this->connectionManager->addConnection($connection3, $this->user->id, ['ip_address' => '************']);

        // 사용자의 모든 연결이 매핑되었는지 확인
        $userConnections = $this->connectionManager->getUserConnections($this->user->id);
        $this->assertCount(3, $userConnections);
        $this->assertContains($connection1, $userConnections);
        $this->assertContains($connection2, $userConnections);
        $this->assertContains($connection3, $userConnections);

        // 사용자가 온라인 상태인지 확인
        $this->assertTrue($this->connectionManager->isUserOnline($this->user->id));

        // 하나의 연결 제거
        $this->connectionManager->removeConnection($connection2);
        
        $userConnections = $this->connectionManager->getUserConnections($this->user->id);
        $this->assertCount(2, $userConnections);
        $this->assertNotContains($connection2, $userConnections);
        
        // 여전히 온라인 상태여야 함
        $this->assertTrue($this->connectionManager->isUserOnline($this->user->id));

        // 모든 연결 제거
        $this->connectionManager->removeConnection($connection1);
        $this->connectionManager->removeConnection($connection3);

        // 오프라인 상태가 되어야 함
        $this->assertFalse($this->connectionManager->isUserOnline($this->user->id));
    }

    /** @test */
    public function 하트비트를_업데이트할_수_있다(): void
    {
        $connectionId = 'heartbeat-test';
        
        // 연결 추가
        $this->connectionManager->addConnection($connectionId, $this->user->id);
        
        // 초기 하트비트 시간 확인
        $initialData = $this->connectionManager->getConnection($connectionId);
        $initialHeartbeat = $initialData['last_heartbeat'];

        // 잠시 대기
        sleep(1);

        // 하트비트 업데이트
        $result = $this->connectionManager->updateHeartbeat($connectionId);
        $this->assertTrue($result);

        // 하트비트 시간이 업데이트되었는지 확인
        $updatedData = $this->connectionManager->getConnection($connectionId);
        $updatedHeartbeat = $updatedData['last_heartbeat'];
        
        $this->assertNotEquals($initialHeartbeat, $updatedHeartbeat);
        $this->assertGreaterThan($initialHeartbeat, $updatedHeartbeat);
    }

    /** @test */
    public function 존재하지_않는_연결의_하트비트_업데이트는_실패한다(): void
    {
        $result = $this->connectionManager->updateHeartbeat('non-existent-connection');
        $this->assertFalse($result);
    }

    /** @test */
    public function 만료된_연결을_정리할_수_있다(): void
    {
        $activeConnection = 'active-connection';
        $expiredConnection = 'expired-connection';

        // 연결 추가
        $this->connectionManager->addConnection($activeConnection, $this->user->id);
        $this->connectionManager->addConnection($expiredConnection, $this->user->id);

        // 만료된 연결의 하트비트를 과거로 설정
        $expiredData = $this->connectionManager->getConnection($expiredConnection);
        $expiredData['last_heartbeat'] = Carbon::now()->subMinutes(5)->toISOString();
        
        // Redis에 직접 저장하여 만료된 상태로 만들기
        $connectionKey = 'sse:connections:' . $expiredConnection;
        Redis::setex($connectionKey, 3600, json_encode($expiredData));

        // 정리 전 상태 확인
        $this->assertCount(2, $this->connectionManager->getActiveConnections());

        // 만료된 연결 정리
        $cleanedCount = $this->connectionManager->cleanupExpiredConnections();

        // 정리 결과 확인
        $this->assertEquals(1, $cleanedCount);
        $activeConnections = $this->connectionManager->getActiveConnections();
        $this->assertCount(1, $activeConnections);
        $this->assertContains($activeConnection, $activeConnections);
        $this->assertNotContains($expiredConnection, $activeConnections);
    }

    /** @test */
    public function 연결_통계를_조회할_수_있다(): void
    {
        $user2 = User::factory()->create();

        // 인증된 연결 2개 추가
        $this->connectionManager->addConnection('auth-1', $this->user->id);
        $this->connectionManager->addConnection('auth-2', $user2->id);

        // 게스트 연결 1개 추가
        $this->connectionManager->addConnection('guest-1', null);

        $stats = $this->connectionManager->getConnectionStats();

        $this->assertEquals(3, $stats['total_connections']);
        $this->assertEquals(2, $stats['authenticated_connections']);
        $this->assertEquals(1, $stats['guest_connections']);
        $this->assertArrayHasKey('timestamp', $stats);
    }

    /** @test */
    public function Redis_연결_실패_시_예외가_발생한다(): void
    {
        // Redis 파사드를 모킹하여 예외 상황 시뮬레이션
        Redis::shouldReceive('setex')->andThrow(new Exception('Redis connection failed'));
        Redis::shouldReceive('sadd')->andThrow(new Exception('Redis connection failed'));

        $this->expectException(Exception::class);

        $this->connectionManager->addConnection('test-connection', $this->user->id);
    }

    /** @test */
    public function Redis_오류_시_빈_배열을_반환한다(): void
    {
        // 새로운 ConnectionManager 인스턴스로 테스트
        $connectionManager = new ConnectionManager();
        
        // Redis 파사드를 모킹하여 예외 상황 시뮬레이션
        Redis::shouldReceive('smembers')->andThrow(new Exception('Redis connection failed'));

        $result = $connectionManager->getActiveConnections();
        $this->assertEquals([], $result);

        $result = $connectionManager->getUserConnections($this->user->id);
        $this->assertEquals([], $result);
    }

    /** @test */
    public function 잘못된_JSON_데이터_처리_시_예외가_발생한다(): void
    {
        // 순환 참조를 만들어 JSON 인코딩 실패 시뮬레이션
        $circularData = [];
        $circularData['self'] = &$circularData;

        $this->expectException(Exception::class);

        // ConnectionManager의 private 메서드를 테스트하기 위해 리플렉션 사용
        $reflection = new \ReflectionClass($this->connectionManager);
        $method = $reflection->getMethod('storeConnectionInRedis');
        $method->setAccessible(true);
        
        $method->invoke($this->connectionManager, 'test-connection', $circularData);
    }
}