<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\MonitorSizeExtractionService;
use App\Services\TelegramService;
use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Mockery;
use stdClass;

class MonitorSizeExtractionServiceTest extends TestCase
{
    protected MonitorSizeExtractionService $service;
    protected $telegramMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        // TelegramService 모킹
        $this->telegramMock = Mockery::mock(TelegramService::class);
        $this->telegramMock->shouldReceive('sendMessageToTeam')->andReturn(true);
        
        // 캐시 초기화
        Cache::flush();
        
        // 서비스 생성 및 규칙 모킹
        $this->createServiceWithMockedRules();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 서비스와 규칙 모킹 생성
     */
    protected function createServiceWithMockedRules(): void
    {
        // 브랜드 규칙 생성
        $brandRule1 = new stdClass();
        $brandRule1->pattern = 'LG';
        $brandRule1->description = '테스트 브랜드 LG';
        
        $brandRule2 = new stdClass();
        $brandRule2->pattern = '삼성';
        $brandRule2->description = '테스트 브랜드 삼성';
        
        $brandRules = new Collection([$brandRule1, $brandRule2]);
        
        // 제외 규칙 생성
        $excludeRule = new stdClass();
        $excludeRule->pattern = 'LG패널';
        $excludeRule->description = '테스트 제외 LG패널';
        
        $excludeRules = new Collection([$excludeRule]);
        
        // 크기 패턴 규칙 생성
        $sizeRule1 = new stdClass();
        $sizeRule1->pattern = '/\bULTRON\s?(\d{2})\d{2}\b/i';
        $sizeRule1->description = '한성 ULTRON 패턴';
        
        $sizeRule2 = new stdClass();
        $sizeRule2->pattern = '/휴대용|포터블/i';
        $sizeRule2->description = '휴대용 패턴';
        
        $sizePatternRules = new Collection([$sizeRule1, $sizeRule2]);
        
        // 서비스 모킹
        $this->service = Mockery::mock(MonitorSizeExtractionService::class, [$this->telegramMock])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();
            
        $this->service->shouldReceive('getBrandRules')->andReturn($brandRules);
        $this->service->shouldReceive('getExcludeRules')->andReturn($excludeRules);
        $this->service->shouldReceive('getSizePatternRules')->andReturn($sizePatternRules);
    }

    /**
     * 테스트용 제품 모킹 생성
     */
    protected function createMockProduct(string $name, string $categoryType = 'monitor'): Product
    {
        $product = Mockery::mock(Product::class);
        
        // 카테고리 모킹
        $cate4 = new stdClass();
        $cate5 = new stdClass();
        
        if ($categoryType === 'monitor') {
            $cate4->name = '컴퓨터주변기기';
            $cate5->name = '모니터';
        } elseif ($categoryType === 'tv') {
            $cate4->name = 'TV';
            $cate5->name = 'TV';
        } else {
            $cate4->name = '기타';
            $cate5->name = '기타';
        }
        
        $product->name = $name;
        $product->qaid = 'TEST' . rand(1000, 9999);
        $product->id = rand(1, 1000);
        $product->cate4 = $cate4;
        $product->cate5 = $cate5;
        
        return $product;
    }

    /** @test */
    public function 모니터_제품_확인_테스트()
    {
        $monitorProduct = $this->createMockProduct('LG 27인치 모니터', 'monitor');
        $tvProduct = $this->createMockProduct('삼성 55인치 TV', 'tv');
        $otherProduct = $this->createMockProduct('일반 제품', 'other');

        $this->assertTrue($this->service->isMonitorProduct($monitorProduct));
        $this->assertTrue($this->service->isMonitorProduct($tvProduct));
        $this->assertFalse($this->service->isMonitorProduct($otherProduct));
    }

    /** @test */
    public function 모니터_모델_결정_테스트()
    {
        // 브랜드 제품
        $lgProduct = $this->createMockProduct('LG 27인치 모니터');
        $this->assertEquals('brand', $this->service->getMonitorModel($lgProduct));

        $samsungProduct = $this->createMockProduct('삼성 32인치 모니터');
        $this->assertEquals('brand', $this->service->getMonitorModel($samsungProduct));

        // 제외 키워드가 포함된 제품
        $lgPanelProduct = $this->createMockProduct('LG패널 27인치 모니터');
        $this->assertEquals('etc', $this->service->getMonitorModel($lgPanelProduct));

        // 기타 제품
        $otherProduct = $this->createMockProduct('한성 27인치 모니터');
        $this->assertEquals('etc', $this->service->getMonitorModel($otherProduct));
    }

    /** @test */
    public function 직접_크기_단위_추출_테스트()
    {
        // CM 단위
        $cmProduct = $this->createMockProduct('모니터 68.6cm');
        $result = $this->service->extractSizeFromName($cmProduct);
        $this->assertEquals(['size' => 68.6, 'unit' => 'CM'], $result);

        // 인치 단위
        $inchProduct = $this->createMockProduct('LG 27인치 모니터');
        $result = $this->service->extractSizeFromName($inchProduct);
        $this->assertEquals(['size' => 27.0, 'unit' => 'INCH'], $result);

        // 형 단위
        $hyungProduct = $this->createMockProduct('삼성 32형 TV');
        $result = $this->service->extractSizeFromName($hyungProduct);
        $this->assertEquals(['size' => 32.0, 'unit' => 'INCH'], $result);
    }

    /** @test */
    public function ULTRON_패턴_추출_테스트()
    {
        $ultronProduct = $this->createMockProduct('한성 ULTRON2711 모니터');
        $result = $this->service->extractSizeFromName($ultronProduct);
        $this->assertEquals(['size' => 27, 'unit' => 'INCH'], $result);
    }

    /** @test */
    public function 휴대용_패턴_추출_테스트()
    {
        $portableProduct = $this->createMockProduct('휴대용 모니터');
        $result = $this->service->extractSizeFromName($portableProduct);
        $this->assertEquals(['size' => 23.8, 'unit' => 'INCH'], $result);

        $portableProduct2 = $this->createMockProduct('포터블 모니터');
        $result2 = $this->service->extractSizeFromName($portableProduct2);
        $this->assertEquals(['size' => 23.8, 'unit' => 'INCH'], $result2);
    }

    /** @test */
    public function 추출_실패_시_기본값_반환_테스트()
    {
        $unknownProduct = $this->createMockProduct('알 수 없는 제품명');
        
        // 텔레그램 알림 호출 확인
        $this->telegramMock->shouldReceive('sendMessageToTeam')
            ->once()
            ->with(Mockery::pattern('/알 수 없는 제품명/'));

        $result = $this->service->extractSizeFromName($unknownProduct);
        $this->assertEquals(['size' => 48, 'unit' => 'INCH'], $result);
    }

    /** @test */
    public function 크기_추출_테스트_기능_테스트()
    {
        $testResult = $this->service->testExtraction('LG 27인치 모니터');
        
        $this->assertEquals('LG 27인치 모니터', $testResult['product_name']);
        $this->assertEquals(['size' => 27.0, 'unit' => 'INCH'], $testResult['extracted_size']);
        $this->assertIsArray($testResult['applied_rules']);
    }

    /** @test */
    public function 다양한_모델명_패턴_테스트()
    {
        $testCases = [
            ['name' => 'TFG34Q10W 모니터', 'expected_size' => 34],
            ['name' => '27GL850 모니터', 'expected_size' => 27],
            ['name' => 'U2720Q 모니터', 'expected_size' => 27],
            ['name' => 'S24F350 모니터', 'expected_size' => 24],
        ];

        foreach ($testCases as $testCase) {
            $product = $this->createMockProduct($testCase['name']);
            $result = $this->service->extractSizeFromName($product);
            
            $this->assertEquals($testCase['expected_size'], $result['size'], 
                "제품명 '{$testCase['name']}'에서 크기 {$testCase['expected_size']}를 추출해야 합니다.");
        }
    }

    /** @test */
    public function 소수점_크기_추출_테스트()
    {
        $product = $this->createMockProduct('모니터 23.8인치');
        $result = $this->service->extractSizeFromName($product);
        $this->assertEquals(['size' => 23.8, 'unit' => 'INCH'], $result);
    }

    /** @test */
    public function 유효하지_않은_크기_필터링_테스트()
    {
        // 너무 작은 크기
        $smallProduct = $this->createMockProduct('모니터 5인치');
        $result = $this->service->extractSizeFromName($smallProduct);
        $this->assertEquals(48, $result['size']); // 기본값

        // 너무 큰 크기
        $largeProduct = $this->createMockProduct('모니터 150인치');
        $result = $this->service->extractSizeFromName($largeProduct);
        $this->assertEquals(48, $result['size']); // 기본값
    }

    /** @test */
    public function 캐시_관련_메서드_테스트()
    {
        // 실제 서비스 인스턴스로 캐시 테스트
        $realService = new MonitorSizeExtractionService($this->telegramMock);
        
        // 캐시 초기화 메서드 테스트
        $realService->clearRulesCache();
        
        $this->assertFalse(Cache::has('monitor_rules_brand'));
        $this->assertFalse(Cache::has('monitor_rules_exclude'));
        $this->assertFalse(Cache::has('monitor_rules_size_pattern'));
    }

    /** @test */
    public function 기본_규칙_생성_메서드_테스트()
    {
        // 실제 서비스 인스턴스로 테스트
        $realService = new MonitorSizeExtractionService($this->telegramMock);
        
        // 메서드 존재 확인
        $this->assertTrue(method_exists($realService, 'createDefaultRules'));
    }

    /** @test */
    public function 서비스_상수_테스트()
    {
        $this->assertEquals(48, MonitorSizeExtractionService::DEFAULT_SIZE);
        $this->assertEquals('INCH', MonitorSizeExtractionService::DEFAULT_UNIT);
        $this->assertEquals('monitor_rules_', MonitorSizeExtractionService::CACHE_PREFIX);
        $this->assertEquals(60, MonitorSizeExtractionService::CACHE_TTL);
    }
}