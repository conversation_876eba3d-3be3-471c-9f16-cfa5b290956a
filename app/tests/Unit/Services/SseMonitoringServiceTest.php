<?php

namespace Tests\Unit\Services;

use App\Models\SseStatistics;
use App\Services\SseMonitoringService;
use App\Services\ConnectionManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Carbon\Carbon;
use Mockery;

/**
 * SseMonitoringService 단위 테스트
 */
class SseMonitoringServiceTest extends TestCase
{
    use RefreshDatabase;

    private SseMonitoringService $monitoringService;
    private $connectionManagerMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        // ConnectionManager 모킹
        $this->connectionManagerMock = Mockery::mock(ConnectionManager::class);
        $this->monitoringService = new SseMonitoringService($this->connectionManagerMock);
        
        // Redis 기본 모킹
        Redis::shouldReceive('lpush')->byDefault()->andReturn(1);
        Redis::shouldReceive('ltrim')->byDefault()->andReturn(true);
        Redis::shouldReceive('expire')->byDefault()->andReturn(true);
        Redis::shouldReceive('lrange')->byDefault()->andReturn([]);
        Redis::shouldReceive('ping')->byDefault()->andReturn('PONG');
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     * 연결 통계 기록 기본 기능 테스트
     */
    public function 연결_통계_기록_기본_기능_테스트(): void
    {
        // Given: 활성 연결 수 모킹
        $this->connectionManagerMock
            ->shouldReceive('getActiveConnections')
            ->andReturn(['conn1', 'conn2', 'conn3']);

        // When: 인증된 연결 통계 기록
        $this->monitoringService->recordConnection(true, 15.5);

        // Then: 오늘 통계가 생성되고 업데이트되었는지 확인
        $todayStats = SseStatistics::where('date', Carbon::today()->toDateString())->first();
        
        $this->assertNotNull($todayStats);
        $this->assertEquals(1, $todayStats->total_connections);
        $this->assertEquals(1, $todayStats->authenticated_connections);
        $this->assertEquals(0, $todayStats->guest_connections);
        $this->assertEquals(3, $todayStats->peak_concurrent_connections);
        $this->assertEquals(15.5, $todayStats->average_connection_duration);
    }

    /**
     * @test
     * 게스트 연결 통계 기록 테스트
     */
    public function 게스트_연결_통계_기록_테스트(): void
    {
        // Given: 활성 연결 수 모킹
        $this->connectionManagerMock
            ->shouldReceive('getActiveConnections')
            ->andReturn(['conn1', 'conn2']);

        // When: 게스트 연결 통계 기록
        $this->monitoringService->recordConnection(false);

        // Then: 게스트 연결 통계가 기록되었는지 확인
        $todayStats = SseStatistics::where('date', Carbon::today()->toDateString())->first();
        
        $this->assertNotNull($todayStats);
        $this->assertEquals(1, $todayStats->total_connections);
        $this->assertEquals(0, $todayStats->authenticated_connections);
        $this->assertEquals(1, $todayStats->guest_connections);
        $this->assertEquals(2, $todayStats->peak_concurrent_connections);
    }

    /**
     * @test
     * 메시지 통계 기록 테스트
     */
    public function 메시지_통계_기록_테스트(): void
    {
        // Given: 기존 통계 데이터 생성
        $todayStats = SseStatistics::create([
            'date' => Carbon::today()->toDateString(),
            'total_connections' => 5,
            'authenticated_connections' => 3,
            'guest_connections' => 2,
            'messages_sent' => 10,
            'failed_messages' => 1,
            'peak_concurrent_connections' => 5,
            'average_connection_duration' => 10.0
        ]);

        // When: 메시지 통계 기록
        $this->monitoringService->recordMessageStats(15, 2);

        // Then: 메시지 통계가 업데이트되었는지 확인
        $todayStats->refresh();
        $this->assertEquals(25, $todayStats->messages_sent); // 10 + 15
        $this->assertEquals(3, $todayStats->failed_messages); // 1 + 2
    }

    /**
     * @test
     * 빈 메시지 통계 기록 테스트
     */
    public function 빈_메시지_통계_기록_테스트(): void
    {
        // Given: 기존 통계 데이터 생성
        $originalStats = SseStatistics::create([
            'date' => Carbon::today()->toDateString(),
            'messages_sent' => 10,
            'failed_messages' => 1
        ]);

        // When: 빈 메시지 통계 기록 (0, 0)
        $this->monitoringService->recordMessageStats(0, 0);

        // Then: 통계가 변경되지 않았는지 확인
        $originalStats->refresh();
        $this->assertEquals(10, $originalStats->messages_sent);
        $this->assertEquals(1, $originalStats->failed_messages);
    }

    /**
     * @test
     * 실시간 모니터링 데이터 조회 테스트
     */
    public function 실시간_모니터링_데이터_조회_테스트(): void
    {
        // Given: ConnectionManager 모킹
        $this->connectionManagerMock
            ->shouldReceive('getConnectionStats')
            ->andReturn([
                'total_connections' => 5,
                'authenticated_connections' => 3,
                'guest_connections' => 2
            ]);

        // 오늘 통계 데이터 생성
        SseStatistics::create([
            'date' => Carbon::today()->toDateString(),
            'total_connections' => 10,
            'messages_sent' => 100,
            'failed_messages' => 5,
            'peak_concurrent_connections' => 8,
            'average_connection_duration' => 12.5
        ]);

        // When: 실시간 모니터링 데이터 조회
        $data = $this->monitoringService->getRealtimeMonitoringData();

        // Then: 올바른 구조의 데이터가 반환되었는지 확인
        $this->assertIsArray($data);
        $this->assertArrayHasKey('current_connections', $data);
        $this->assertArrayHasKey('realtime_stats', $data);
        $this->assertArrayHasKey('today_cumulative', $data);
        $this->assertArrayHasKey('message_stats', $data);
        $this->assertArrayHasKey('connection_history', $data);
        $this->assertArrayHasKey('system_health', $data);
        $this->assertArrayHasKey('generated_at', $data);

        // 오늘 누적 통계 확인 (기본 구조만 확인)
        $this->assertArrayHasKey('today_cumulative', $data);
        $this->assertIsArray($data['today_cumulative']);
        
        // 실제 데이터가 있는 경우에만 값 확인
        if (!empty($data['today_cumulative'])) {
            $this->assertArrayHasKey('total_connections', $data['today_cumulative']);
            $this->assertArrayHasKey('messages_sent', $data['today_cumulative']);
            $this->assertArrayHasKey('failed_messages', $data['today_cumulative']);
            $this->assertArrayHasKey('success_rate', $data['today_cumulative']);
        }
    }

    /**
     * @test
     * 일별 기간별 통계 조회 테스트
     */
    public function 일별_기간별_통계_조회_테스트(): void
    {
        // Given: 테스트 통계 데이터 생성
        $dates = [
            Carbon::today()->toDateString(),
            Carbon::yesterday()->toDateString(),
            Carbon::today()->subDays(2)->toDateString()
        ];

        foreach ($dates as $index => $date) {
            SseStatistics::create([
                'date' => $date,
                'total_connections' => ($index + 1) * 10,
                'messages_sent' => ($index + 1) * 100,
                'failed_messages' => ($index + 1) * 5,
                'peak_concurrent_connections' => ($index + 1) * 8
            ]);
        }

        // When: 일별 통계 조회
        $result = $this->monitoringService->getHistoricalStats('daily', 3);

        // Then: 올바른 데이터가 반환되었는지 확인
        $this->assertIsArray($result);
        $this->assertEquals('daily', $result['period']);
        $this->assertEquals(3, $result['limit']);
        $this->assertCount(3, $result['data']);
        $this->assertArrayHasKey('summary', $result);

        // 데이터가 올바르게 반환되었는지 확인
        $this->assertNotEmpty($result['data']);
        $firstItem = $result['data'][0];
        $this->assertArrayHasKey('date', $firstItem);
        
        // 데이터가 최신 순으로 정렬되었는지 확인 (첫 번째 항목이 가장 최신)
        $dates = collect($result['data'])->pluck('date')->map(function($date) {
            if ($date instanceof Carbon) {
                return $date->toDateString();
            } elseif (is_string($date) && strlen($date) > 10) {
                return substr($date, 0, 10);
            }
            return $date;
        })->toArray();
        
        // 날짜가 내림차순으로 정렬되었는지 확인
        $sortedDates = collect($dates)->sort()->reverse()->values()->toArray();
        $this->assertEquals($sortedDates, array_values($dates));
        
        // 성공률이 계산되었는지 확인
        $this->assertArrayHasKey('success_rate', $result['data'][0]);
    }

    /**
     * @test
     * 주별 기간별 통계 조회 테스트
     */
    public function 주별_기간별_통계_조회_테스트(): void
    {
        // Given: 여러 날짜의 통계 데이터 생성 (같은 주)
        $thisWeekDates = [
            Carbon::now()->startOfWeek()->toDateString(),
            Carbon::now()->startOfWeek()->addDay()->toDateString(),
            Carbon::now()->startOfWeek()->addDays(2)->toDateString()
        ];

        foreach ($thisWeekDates as $date) {
            SseStatistics::create([
                'date' => $date,
                'total_connections' => 10,
                'messages_sent' => 100,
                'failed_messages' => 5,
                'peak_concurrent_connections' => 8
            ]);
        }

        // When: 주별 통계 조회
        $result = $this->monitoringService->getHistoricalStats('weekly', 1);

        // Then: 주별로 집계된 데이터가 반환되었는지 확인
        $this->assertIsArray($result);
        $this->assertEquals('weekly', $result['period']);
        $this->assertCount(1, $result['data']);
        
        // 주별 집계 확인
        $weekData = $result['data'][0];
        $this->assertEquals(30, $weekData['total_connections']); // 10 * 3
        $this->assertEquals(300, $weekData['messages_sent']); // 100 * 3
        $this->assertEquals(15, $weekData['failed_messages']); // 5 * 3
    }

    /**
     * @test
     * 시스템 알림 조회 테스트
     */
    public function 시스템_알림_조회_테스트(): void
    {
        // Given: 높은 연결 수 시뮬레이션
        $this->connectionManagerMock
            ->shouldReceive('getActiveConnections')
            ->andReturn(array_fill(0, 1500, 'connection')); // 1500개 연결

        // 높은 실패율 통계 생성
        SseStatistics::create([
            'date' => Carbon::today()->toDateString(),
            'messages_sent' => 90,
            'failed_messages' => 10 // 10% 실패율
        ]);

        // When: 시스템 알림 조회
        $alerts = $this->monitoringService->getSystemAlerts();

        // Then: 알림이 생성되었는지 확인
        $this->assertIsArray($alerts);
        $this->assertArrayHasKey('alerts', $alerts);
        $this->assertArrayHasKey('alert_count', $alerts);
        $this->assertGreaterThan(0, $alerts['alert_count']);

        // 높은 연결 수 알림 확인
        $connectionAlert = collect($alerts['alerts'])->firstWhere('title', '높은 연결 수');
        $this->assertNotNull($connectionAlert);
        $this->assertEquals('warning', $connectionAlert['type']);

        // 높은 실패율 알림 확인
        $failureAlert = collect($alerts['alerts'])->firstWhere('title', '높은 메시지 실패율');
        $this->assertNotNull($failureAlert);
        $this->assertEquals('error', $failureAlert['type']);
    }

    /**
     * @test
     * 정상 상태에서 시스템 알림 조회 테스트
     */
    public function 정상_상태에서_시스템_알림_조회_테스트(): void
    {
        // Given: 정상적인 연결 수
        $this->connectionManagerMock
            ->shouldReceive('getActiveConnections')
            ->andReturn(['conn1', 'conn2', 'conn3']); // 3개 연결

        // 정상적인 통계 생성
        SseStatistics::create([
            'date' => Carbon::today()->toDateString(),
            'messages_sent' => 95,
            'failed_messages' => 2 // 2% 실패율
        ]);

        // When: 시스템 알림 조회
        $alerts = $this->monitoringService->getSystemAlerts();

        // Then: 알림이 없거나 최소한인지 확인
        $this->assertIsArray($alerts);
        $this->assertArrayHasKey('alerts', $alerts);
        $this->assertArrayHasKey('alert_count', $alerts);
        
        // 심각한 알림이 없는지 확인
        $criticalAlerts = collect($alerts['alerts'])->where('type', 'critical');
        $this->assertCount(0, $criticalAlerts);
    }

    /**
     * @test
     * 오래된 통계 데이터 정리 테스트
     */
    public function 오래된_통계_데이터_정리_테스트(): void
    {
        // Given: 다양한 날짜의 통계 데이터 생성
        $dates = [
            Carbon::today()->toDateString(), // 오늘 (유지)
            Carbon::today()->subDays(30)->toDateString(), // 30일 전 (유지)
            Carbon::today()->subDays(100)->toDateString(), // 100일 전 (삭제 대상)
            Carbon::today()->subDays(200)->toDateString() // 200일 전 (삭제 대상)
        ];

        foreach ($dates as $date) {
            SseStatistics::create([
                'date' => $date,
                'total_connections' => 10,
                'messages_sent' => 100
            ]);
        }

        $this->assertEquals(4, SseStatistics::count());

        // When: 90일 이상 된 데이터 정리
        $deletedCount = $this->monitoringService->cleanupOldStats(90);

        // Then: 오래된 데이터가 삭제되었는지 확인
        $this->assertEquals(2, $deletedCount);
        $this->assertEquals(2, SseStatistics::count());
        
        // 남은 데이터가 올바른지 확인
        $remainingDates = SseStatistics::pluck('date')->map(function($date) {
            return $date instanceof Carbon ? $date->toDateString() : $date;
        })->toArray();
        
        $this->assertContains(Carbon::today()->toDateString(), $remainingDates);
        $this->assertContains(Carbon::today()->subDays(30)->toDateString(), $remainingDates);
    }

    /**
     * @test
     * 연결 지속 시간 평균 계산 테스트
     */
    public function 연결_지속_시간_평균_계산_테스트(): void
    {
        // Given: 활성 연결 수 모킹
        $this->connectionManagerMock
            ->shouldReceive('getActiveConnections')
            ->andReturn(['conn1']);

        // When: 여러 연결의 지속 시간 기록
        $this->monitoringService->recordConnection(true, 10.0); // 첫 번째 연결: 10분
        $this->monitoringService->recordConnection(true, 20.0); // 두 번째 연결: 20분
        $this->monitoringService->recordConnection(true, 30.0); // 세 번째 연결: 30분

        // Then: 평균 연결 지속 시간이 올바르게 계산되었는지 확인
        $todayStats = SseStatistics::where('date', Carbon::today()->toDateString())->first();
        
        $this->assertNotNull($todayStats);
        $this->assertEquals(3, $todayStats->total_connections);
        
        // 평균 계산: (10 + 20 + 30) / 3 = 20.0
        $this->assertEquals(20.0, $todayStats->average_connection_duration);
    }

    /**
     * @test
     * 최대 동시 연결 수 업데이트 테스트
     */
    public function 최대_동시_연결_수_업데이트_테스트(): void
    {
        // Given: 다양한 연결 수 시뮬레이션
        $connectionCounts = [
            ['conn1', 'conn2'], // 2개
            ['conn1', 'conn2', 'conn3', 'conn4', 'conn5'], // 5개 (최대)
            ['conn1', 'conn2', 'conn3'] // 3개
        ];

        // When: 각각의 연결 수로 통계 기록
        foreach ($connectionCounts as $connections) {
            $this->connectionManagerMock
                ->shouldReceive('getActiveConnections')
                ->once()
                ->andReturn($connections);
                
            $this->monitoringService->recordConnection(true);
        }

        // Then: 최대 동시 연결 수가 올바르게 기록되었는지 확인
        $todayStats = SseStatistics::where('date', Carbon::today()->toDateString())->first();
        
        $this->assertNotNull($todayStats);
        $this->assertEquals(5, $todayStats->peak_concurrent_connections); // 최대값
    }
}