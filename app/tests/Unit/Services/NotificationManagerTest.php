<?php

namespace Tests\Unit\Services;

use App\Models\SseOfflineNotification;
use App\Models\User;
use App\Services\ConnectionManager;
use App\Services\MessageFormatterService;
use App\Services\NotificationManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Mockery;

/**
 * NotificationManager 서비스 단위 테스트
 */
class NotificationManagerTest extends TestCase
{
    use RefreshDatabase;

    private NotificationManager $notificationManager;
    private ConnectionManager $connectionManager;
    private MessageFormatterService $messageFormatter;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock 객체 생성
        $this->connectionManager = Mockery::mock(ConnectionManager::class);
        $this->messageFormatter = Mockery::mock(MessageFormatterService::class);
        
        $this->notificationManager = new NotificationManager(
            $this->connectionManager,
            $this->messageFormatter
        );

        // Redis 모킹
        Redis::shouldReceive('publish')->andReturn(1)->byDefault();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function 전체_사용자에게_알림을_브로드캐스트할_수_있다()
    {
        // Given
        $testData = [
            'title' => '테스트 알림',
            'message' => '전체 사용자 대상 테스트 메시지입니다.'
        ];
        
        $activeConnections = ['conn_1', 'conn_2', 'conn_3'];
        $formattedMessage = [
            'type' => 'notification',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => $testData
        ];
        $serializedMessage = json_encode($formattedMessage);

        // Mock 설정
        $this->connectionManager
            ->shouldReceive('getActiveConnections')
            ->once()
            ->andReturn($activeConnections);

        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->once()
            ->with($testData)
            ->andReturn($formattedMessage);

        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->once()
            ->with($formattedMessage)
            ->andReturn($serializedMessage);

        Redis::shouldReceive('publish')
            ->times(3)
            ->andReturn(1);

        // When
        $result = $this->notificationManager->sendToAll($testData);

        // Then
        $this->assertEquals(3, $result['total_connections']);
        $this->assertEquals(3, $result['successful_sends']);
        $this->assertEquals(0, $result['failed_sends']);
    }

    /** @test */
    public function 활성_연결이_없을_때_전체_브로드캐스트가_적절히_처리된다()
    {
        // Given
        $testData = ['title' => '테스트 알림'];

        $this->connectionManager
            ->shouldReceive('getActiveConnections')
            ->once()
            ->andReturn([]);

        // When
        $result = $this->notificationManager->sendToAll($testData);

        // Then
        $this->assertEquals(0, $result['total_connections']);
        $this->assertEquals(0, $result['successful_sends']);
        $this->assertEquals(0, $result['failed_sends']);
    }

    /** @test */
    public function 온라인_사용자에게_개별_알림을_전송할_수_있다()
    {
        // Given
        $userId = 1;
        $testData = [
            'title' => '개별 알림',
            'message' => '개별 사용자 대상 메시지입니다.'
        ];
        
        $userConnections = ['conn_user_1', 'conn_user_2'];
        $formattedMessage = [
            'type' => 'notification',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => $testData
        ];
        $serializedMessage = json_encode($formattedMessage);

        // Mock 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->once()
            ->with($userId)
            ->andReturn($userConnections);

        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->once()
            ->with($testData)
            ->andReturn($formattedMessage);

        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->once()
            ->with($formattedMessage)
            ->andReturn($serializedMessage);

        Redis::shouldReceive('publish')
            ->times(2)
            ->andReturn(1);

        // When
        $result = $this->notificationManager->sendToUser($userId, $testData);

        // Then
        $this->assertEquals(2, $result['total_connections']);
        $this->assertEquals(2, $result['successful_sends']);
        $this->assertEquals(0, $result['failed_sends']);
        $this->assertTrue($result['delivered_online']);
    }

    /** @test */
    public function 오프라인_사용자에게_알림을_데이터베이스에_저장할_수_있다()
    {
        // Given
        $user = User::factory()->create();
        $testData = [
            'title' => '오프라인 알림',
            'message' => '오프라인 사용자 대상 메시지입니다.'
        ];

        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->once()
            ->with($user->id)
            ->andReturn([]);

        // When
        $result = $this->notificationManager->sendToUser($user->id, $testData);

        // Then
        $this->assertEquals(0, $result['total_connections']);
        $this->assertFalse($result['delivered_online']);
        $this->assertTrue($result['stored_offline']);

        // 데이터베이스에 저장되었는지 확인
        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $user->id,
            'type' => 'notification'
        ]);

        $notification = SseOfflineNotification::where('user_id', $user->id)->first();
        $this->assertEquals($testData, $notification->data);
        $this->assertNull($notification->delivered_at);
    }

    /** @test */
    public function 여러_사용자에게_일괄_알림을_전송할_수_있다()
    {
        // Given
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();
        
        $userIds = [$user1->id, $user2->id, $user3->id];
        $testData = ['title' => '일괄 알림'];

        $formattedMessage = [
            'type' => 'notification',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => $testData
        ];
        $serializedMessage = json_encode($formattedMessage);

        // Mock 설정 - user1은 온라인, user2와 user3는 오프라인
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($user1->id)
            ->andReturn(['conn_1']);
        
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($user2->id)
            ->andReturn([]);
            
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($user3->id)
            ->andReturn([]);

        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->once()
            ->with($testData)
            ->andReturn($formattedMessage);

        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->once()
            ->with($formattedMessage)
            ->andReturn($serializedMessage);

        Redis::shouldReceive('publish')
            ->once()
            ->andReturn(1);

        // When
        $result = $this->notificationManager->sendToUsers($userIds, $testData);

        // Then
        $this->assertEquals(3, $result['total_users']);
        $this->assertEquals(1, $result['online_users']);
        $this->assertEquals(2, $result['offline_users']);
        $this->assertEquals(1, $result['successful_sends']);
        $this->assertEquals(2, $result['stored_offline']);

        // 오프라인 알림이 저장되었는지 확인
        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $user2->id,
            'type' => 'notification'
        ]);
        
        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $user3->id,
            'type' => 'notification'
        ]);
    }

    /** @test */
    public function 오프라인_알림을_조회할_수_있다()
    {
        // Given
        $user = User::factory()->create();
        
        // 테스트 알림 생성
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '알림 1', 'message' => '첫 번째 알림'],
            'created_at' => now()->subMinutes(10)
        ]);
        
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '알림 2', 'message' => '두 번째 알림'],
            'created_at' => now()->subMinutes(5)
        ]);

        // 이미 전달된 알림 (조회되지 않아야 함)
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '전달된 알림'],
            'created_at' => now()->subMinutes(15),
            'delivered_at' => now()->subMinutes(1)
        ]);

        // When
        $notifications = $this->notificationManager->getOfflineNotifications($user->id);

        // Then
        $this->assertCount(2, $notifications);
        $this->assertEquals('알림 1', $notifications[0]['data']['title']);
        $this->assertEquals('알림 2', $notifications[1]['data']['title']);
    }

    /** @test */
    public function 사용자_로그인_시_미전달_알림을_전송할_수_있다()
    {
        // Given
        $user = User::factory()->create();
        
        // 미전달 알림 생성
        $notification1 = SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '미전달 알림 1'],
            'created_at' => now()->subMinutes(10)
        ]);
        
        $notification2 = SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '미전달 알림 2'],
            'created_at' => now()->subMinutes(5)
        ]);

        $userConnections = ['conn_user_1'];
        $formattedMessage = [
            'type' => 'notification',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => ['title' => '미전달 알림 1']
        ];
        $serializedMessage = json_encode($formattedMessage);

        // Mock 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($user->id)
            ->andReturn($userConnections);

        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->times(2)
            ->andReturn($formattedMessage);

        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->times(2)
            ->andReturn($serializedMessage);

        Redis::shouldReceive('publish')
            ->times(2)
            ->andReturn(1);

        // When
        $result = $this->notificationManager->deliverOfflineNotifications($user->id);

        // Then
        $this->assertEquals(2, $result['total_notifications']);
        $this->assertEquals(2, $result['delivered_count']);
        $this->assertEquals(0, $result['failed_count']);

        // 알림이 전달 완료로 표시되었는지 확인
        $notification1->refresh();
        $notification2->refresh();
        
        $this->assertNotNull($notification1->delivered_at);
        $this->assertNotNull($notification2->delivered_at);
    }

    /** @test */
    public function 사용자가_오프라인일_때_미전달_알림_전송이_적절히_처리된다()
    {
        // Given
        $user = User::factory()->create();
        
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '미전달 알림'],
            'created_at' => now()->subMinutes(10)
        ]);

        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($user->id)
            ->andReturn([]);

        // When
        $result = $this->notificationManager->deliverOfflineNotifications($user->id);

        // Then
        $this->assertEquals(1, $result['total_notifications']);
        $this->assertEquals(0, $result['delivered_count']);
        $this->assertTrue($result['user_offline']);
    }

    /** @test */
    public function 오래된_전달_완료_알림을_정리할_수_있다()
    {
        // Given
        $user = User::factory()->create();
        
        // 오래된 전달 완료 알림 (삭제되어야 함)
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '오래된 알림'],
            'created_at' => now()->subDays(10),
            'delivered_at' => now()->subDays(8)
        ]);
        
        // 최근 전달 완료 알림 (유지되어야 함)
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '최근 알림'],
            'created_at' => now()->subDays(2),
            'delivered_at' => now()->subDays(1)
        ]);
        
        // 미전달 알림 (유지되어야 함)
        SseOfflineNotification::create([
            'user_id' => $user->id,
            'type' => 'notification',
            'data' => ['title' => '미전달 알림'],
            'created_at' => now()->subDays(5)
        ]);

        // When
        $deletedCount = $this->notificationManager->cleanupDeliveredNotifications(7);

        // Then
        $this->assertEquals(1, $deletedCount);
        $this->assertEquals(2, SseOfflineNotification::count());
    }

    /** @test */
    public function 데이터_업데이트_메시지를_브로드캐스트할_수_있다()
    {
        // Given
        $model = 'categories';
        $action = 'updated';
        $payload = [
            'cate4' => [['id' => 1, 'name' => '카테고리 1']],
            'cate5' => [['id' => 1, 'name' => '하위 카테고리 1']]
        ];
        
        $activeConnections = ['conn_1', 'conn_2'];
        $formattedMessage = [
            'type' => 'data_update',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => [
                'model' => $model,
                'action' => $action,
                'payload' => $payload
            ]
        ];
        $serializedMessage = json_encode($formattedMessage);

        // Mock 설정
        $this->connectionManager
            ->shouldReceive('getActiveConnections')
            ->once()
            ->andReturn($activeConnections);

        $this->messageFormatter
            ->shouldReceive('formatDataUpdateMessage')
            ->once()
            ->with($model, $action, $payload)
            ->andReturn($formattedMessage);

        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->once()
            ->with($formattedMessage)
            ->andReturn($serializedMessage);

        Redis::shouldReceive('publish')
            ->times(2)
            ->andReturn(1);

        // When
        $result = $this->notificationManager->sendDataUpdate($model, $action, $payload);

        // Then
        $this->assertEquals(2, $result['total_connections']);
        $this->assertEquals(2, $result['successful_sends']);
        $this->assertEquals(0, $result['failed_sends']);
    }

    /** @test */
    public function 시스템_상태_메시지를_브로드캐스트할_수_있다()
    {
        // Given
        $status = 'maintenance';
        $data = [
            'message' => '시스템 점검 중입니다.',
            'estimated_duration' => '30분'
        ];
        
        $activeConnections = ['conn_1'];
        $formattedMessage = [
            'type' => 'system_status',
            'timestamp' => '2025-01-11T10:00:00Z',
            'data' => [
                'status' => $status,
                'message' => $data['message'],
                'estimated_duration' => $data['estimated_duration']
            ]
        ];
        $serializedMessage = json_encode($formattedMessage);

        // Mock 설정
        $this->connectionManager
            ->shouldReceive('getActiveConnections')
            ->once()
            ->andReturn($activeConnections);

        $this->messageFormatter
            ->shouldReceive('formatSystemStatusMessage')
            ->once()
            ->with($status, $data)
            ->andReturn($formattedMessage);

        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->once()
            ->with($formattedMessage)
            ->andReturn($serializedMessage);

        Redis::shouldReceive('publish')
            ->once()
            ->andReturn(1);

        // When
        $result = $this->notificationManager->sendSystemStatus($status, $data);

        // Then
        $this->assertEquals(1, $result['total_connections']);
        $this->assertEquals(1, $result['successful_sends']);
        $this->assertEquals(0, $result['failed_sends']);
    }

    /** @test */
    public function 오프라인_알림_수_제한이_적용된다()
    {
        // Given
        $user = User::factory()->create();
        
        // MAX_OFFLINE_NOTIFICATIONS 상수를 테스트용으로 조정하기 위해 리플렉션 사용
        $reflection = new \ReflectionClass($this->notificationManager);
        $maxNotificationsProperty = $reflection->getConstant('MAX_OFFLINE_NOTIFICATIONS');
        
        // 제한 수만큼 알림 생성 (실제로는 1000개이지만 테스트에서는 적은 수로)
        for ($i = 0; $i < 5; $i++) {
            SseOfflineNotification::create([
                'user_id' => $user->id,
                'type' => 'notification',
                'data' => ['title' => "알림 {$i}"],
                'created_at' => now()->subMinutes(10 - $i)
            ]);
        }

        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($user->id)
            ->andReturn([]);

        // When - 새로운 알림 추가 시도
        $result = $this->notificationManager->sendToUser($user->id, [
            'title' => '새 알림',
            'message' => '제한 테스트용 알림'
        ]);

        // Then
        $this->assertTrue($result['stored_offline']);
        
        // 총 알림 수가 여전히 제한 내에 있는지 확인
        $totalNotifications = SseOfflineNotification::where('user_id', $user->id)->count();
        $this->assertLessThanOrEqual(6, $totalNotifications); // 5개 기존 + 1개 새로운
    }
}