<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\MonitorSizeExtractionService;
use App\Services\TelegramService;
use Mockery;

/**
 * 간단한 MonitorSizeExtractionService 테스트
 * 실제 서비스 로직만 테스트하고 데이터베이스 의존성은 제거
 */
class MonitorSizeExtractionServiceSimpleTest extends TestCase
{
    protected MonitorSizeExtractionService $service;
    protected $telegramMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        // TelegramService 모킹
        $this->telegramMock = Mockery::mock(TelegramService::class);
        $this->telegramMock->shouldReceive('sendMessageToTeam')->andReturn(true);
        
        $this->service = new MonitorSizeExtractionService($this->telegramMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function 서비스_상수_테스트()
    {
        $this->assertEquals(48, MonitorSizeExtractionService::DEFAULT_SIZE);
        $this->assertEquals('INCH', MonitorSizeExtractionService::DEFAULT_UNIT);
        $this->assertEquals('monitor_rules_', MonitorSizeExtractionService::CACHE_PREFIX);
        $this->assertEquals(60, MonitorSizeExtractionService::CACHE_TTL);
    }

    /** @test */
    public function 기본_규칙_생성_메서드_존재_테스트()
    {
        $this->assertTrue(method_exists($this->service, 'createDefaultRules'));
        $this->assertTrue(method_exists($this->service, 'clearRulesCache'));
        $this->assertTrue(method_exists($this->service, 'testExtraction'));
    }

    /** @test */
    public function 크기_추출_테스트_기능_테스트()
    {
        $testResult = $this->service->testExtraction('LG 27인치 모니터');
        
        $this->assertEquals('LG 27인치 모니터', $testResult['product_name']);
        $this->assertIsArray($testResult['extracted_size']);
        $this->assertIsArray($testResult['applied_rules']);
        
        // 크기 정보 확인
        $this->assertArrayHasKey('size', $testResult['extracted_size']);
        $this->assertArrayHasKey('unit', $testResult['extracted_size']);
    }

    /** @test */
    public function 직접_크기_단위_추출_패턴_테스트()
    {
        // 인치 패턴 테스트
        $inchResult = $this->service->testExtraction('LG 27인치 모니터');
        $this->assertEquals(27.0, $inchResult['extracted_size']['size']);
        $this->assertEquals('INCH', $inchResult['extracted_size']['unit']);

        // 형 패턴 테스트
        $hyungResult = $this->service->testExtraction('삼성 32형 TV');
        $this->assertEquals(32.0, $hyungResult['extracted_size']['size']);
        $this->assertEquals('INCH', $hyungResult['extracted_size']['unit']);

        // CM 패턴 테스트
        $cmResult = $this->service->testExtraction('모니터 68.6cm');
        $this->assertEquals(68.6, $cmResult['extracted_size']['size']);
        $this->assertEquals('CM', $cmResult['extracted_size']['unit']);
    }

    /** @test */
    public function 소수점_크기_추출_테스트()
    {
        $result = $this->service->testExtraction('모니터 23.8인치');
        $this->assertEquals(23.8, $result['extracted_size']['size']);
        $this->assertEquals('INCH', $result['extracted_size']['unit']);
    }

    /** @test */
    public function 다양한_모델명_패턴_테스트()
    {
        $testCases = [
            ['name' => 'TFG34Q10W 모니터', 'expected_size' => 34],
            ['name' => '27GL850 모니터', 'expected_size' => 27],
            ['name' => 'U2720Q 모니터', 'expected_size' => 27],
            ['name' => 'S24F350 모니터', 'expected_size' => 24],
        ];

        foreach ($testCases as $testCase) {
            $result = $this->service->testExtraction($testCase['name']);
            
            $this->assertEquals($testCase['expected_size'], $result['extracted_size']['size'], 
                "제품명 '{$testCase['name']}'에서 크기 {$testCase['expected_size']}를 추출해야 합니다.");
        }
    }

    /** @test */
    public function 추출_실패_시_기본값_반환_테스트()
    {
        // 숫자가 전혀 없는 제품명으로 기본값 반환 테스트
        $result = $this->service->testExtraction('완전히알수없는제품');
        $this->assertEquals(48, $result['extracted_size']['size']);
        $this->assertEquals('INCH', $result['extracted_size']['unit']);
    }

    /** @test */
    public function 유효하지_않은_크기_필터링_테스트()
    {
        // 너무 작은 크기
        $smallResult = $this->service->testExtraction('모니터 5인치');
        $this->assertEquals(48, $smallResult['extracted_size']['size']); // 기본값

        // 너무 큰 크기
        $largeResult = $this->service->testExtraction('모니터 150인치');
        $this->assertEquals(48, $largeResult['extracted_size']['size']); // 기본값
    }

    /** @test */
    public function 캐시_관련_메서드_테스트()
    {
        // 캐시 초기화 메서드 실행 (오류 없이 실행되는지 확인)
        $this->service->clearRulesCache();
        
        // 메서드가 정상적으로 실행되었다면 테스트 통과
        $this->assertTrue(true);
    }

    /** @test */
    public function 특수_패턴_테스트()
    {
        // 휴대용/포터블 패턴
        $portableResult = $this->service->testExtraction('휴대용 모니터');
        $this->assertEquals(23.8, $portableResult['extracted_size']['size']);

        $portableResult2 = $this->service->testExtraction('포터블 모니터');
        $this->assertEquals(23.8, $portableResult2['extracted_size']['size']);

        // ULTRON 패턴
        $ultronResult = $this->service->testExtraction('한성 ULTRON2711 모니터');
        $this->assertEquals(27, $ultronResult['extracted_size']['size']);
    }

    /** @test */
    public function 실제_상품명_패턴_테스트()
    {
        // 실제 상품명 패턴 테스트
        $thinkVisionResult = $this->service->testExtraction('ThinkVision M14d 63AAUAR6WW 휴대용모니터/포터블모니터/IPS/2.2K/WLED/4.9mm 베젤');
        
        // 휴대용/포터블 키워드가 있으므로 23.8이 되거나, M14d에서 14를 추출할 수 있음
        $this->assertTrue(
            $thinkVisionResult['extracted_size']['size'] == 23.8 || 
            $thinkVisionResult['extracted_size']['size'] == 14,
            "ThinkVision M14d에서 크기를 올바르게 추출해야 합니다. 실제 값: " . $thinkVisionResult['extracted_size']['size']
        );

        // 다른 실제 패턴들
        $testCases = [
            ['name' => 'LG 27UP850 27인치 4K UHD', 'expected_size' => 27],
            ['name' => 'Samsung C24F390FH 24인치 커브드', 'expected_size' => 24],
            ['name' => 'ASUS VG248QE 24인치 144Hz', 'expected_size' => 24],
            ['name' => 'Dell U2720Q 27인치 4K', 'expected_size' => 27],
            ['name' => 'BenQ EW3270U 32인치 4K HDR', 'expected_size' => 32],
        ];

        foreach ($testCases as $testCase) {
            $result = $this->service->testExtraction($testCase['name']);
            $this->assertEquals($testCase['expected_size'], $result['extracted_size']['size'], 
                "제품명 '{$testCase['name']}'에서 크기 {$testCase['expected_size']}를 추출해야 합니다.");
        }
    }

    /** @test */
    public function 센치미터_한글_단위_테스트()
    {
        $result1 = $this->service->testExtraction('모니터 68센치');
        $this->assertEquals(68.0, $result1['extracted_size']['size']);
        $this->assertEquals('CM', $result1['extracted_size']['unit']);

        $result2 = $this->service->testExtraction('모니터 68센티미터');
        $this->assertEquals(68.0, $result2['extracted_size']['size']);
        $this->assertEquals('CM', $result2['extracted_size']['unit']);
    }

    /** @test */
    public function Hz_제외_패턴_테스트()
    {
        // Hz가 포함된 경우 크기로 인식하지 않아야 함
        $result = $this->service->testExtraction('모니터 144Hz 27인치');
        $this->assertEquals(27, $result['extracted_size']['size']);
        $this->assertNotEquals(144, $result['extracted_size']['size']);
    }
}