<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Services\SseLogger;
use App\Services\SseSecurityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SseSecurityServiceTest extends TestCase
{
    use RefreshDatabase;
    
    private SseSecurityService $securityService;
    private SseLogger $mockLogger;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // SseLogger 모킹
        $this->mockLogger = $this->createMock(SseLogger::class);
        $this->securityService = new SseSecurityService($this->mockLogger);
        
        // 캐시 초기화
        Cache::flush();
    }
    
    public function test_민감_정보_필터링(): void
    {
        // Given
        $data = [
            'username' => 'testuser',
            'password' => 'secret123',
            'api_token' => 'abc123token',
            'normal_field' => 'normal_value',
            'nested' => [
                'secret_key' => 'hidden_secret',
                'public_info' => 'visible_info'
            ]
        ];
        
        // When
        $filtered = $this->securityService->filterSensitiveData($data);
        
        // Then
        $this->assertEquals('testuser', $filtered['username']);
        $this->assertNotEquals('secret123', $filtered['password']);
        $this->assertStringContainsString('*', $filtered['password']);
        $this->assertNotEquals('abc123token', $filtered['api_token']);
        $this->assertEquals('normal_value', $filtered['normal_field']);
        $this->assertNotEquals('hidden_secret', $filtered['nested']['secret_key']);
        $this->assertEquals('visible_info', $filtered['nested']['public_info']);
    }
    
    public function test_XSS_공격_패턴_검증(): void
    {
        // Given - XSS 공격 시도
        $maliciousInputs = [
            '<script>alert("xss")</script>',
            '<iframe src="javascript:alert(1)"></iframe>',
            'javascript:alert(1)',
            '<img onerror="alert(1)" src="x">',
            '<object data="data:text/html,<script>alert(1)</script>"></object>'
        ];
        
        $this->mockLogger->expects($this->exactly(count($maliciousInputs)))
            ->method('logSecurityEvent')
            ->with('xss_attempt_detected', 'XSS 공격 시도 감지');
        
        // When & Then
        foreach ($maliciousInputs as $input) {
            $result = $this->securityService->validateAgainstXss($input);
            $this->assertFalse($result, "XSS 패턴이 감지되지 않음: {$input}");
        }
    }
    
    public function test_정상_입력_XSS_검증_통과(): void
    {
        // Given - 정상적인 입력
        $normalInputs = [
            'Hello World',
            'This is a normal message',
            'User clicked button',
            '사용자가 메시지를 전송했습니다'
        ];
        
        // When & Then
        foreach ($normalInputs as $input) {
            $result = $this->securityService->validateAgainstXss($input);
            $this->assertTrue($result, "정상 입력이 XSS로 오탐지됨: {$input}");
        }
    }
    
    public function test_SQL_Injection_공격_패턴_검증(): void
    {
        // Given - SQL Injection 공격 시도
        $maliciousInputs = [
            "'; DROP TABLE users; --",
            "1 OR 1=1",
            "UNION SELECT * FROM users",
            "INSERT INTO users VALUES",
            "exec sp_executesql"
        ];
        
        $this->mockLogger->expects($this->exactly(count($maliciousInputs)))
            ->method('logSecurityEvent')
            ->with('sql_injection_attempt_detected', 'SQL Injection 공격 시도 감지');
        
        // When & Then
        foreach ($maliciousInputs as $input) {
            $result = $this->securityService->validateAgainstSqlInjection($input);
            $this->assertFalse($result, "SQL Injection 패턴이 감지되지 않음: {$input}");
        }
    }
    
    public function test_메시지_내용_전체_검증(): void
    {
        // Given - 정상적인 메시지
        $validMessage = [
            'type' => 'notification',
            'title' => '알림 제목',
            'message' => '정상적인 메시지 내용'
        ];
        
        // When
        $result = $this->securityService->validateMessageContent($validMessage);
        
        // Then
        $this->assertTrue($result);
    }
    
    public function test_대용량_페이로드_검증_실패(): void
    {
        // Given - 매우 큰 메시지
        $largeMessage = [
            'type' => 'notification',
            'data' => str_repeat('A', 2000000) // 2MB 데이터
        ];
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('large_payload_detected', '비정상적으로 큰 페이로드 감지');
        
        // When
        $result = $this->securityService->validateMessageContent($largeMessage);
        
        // Then
        $this->assertFalse($result);
    }
    
    public function test_빠른_연속_요청_탐지(): void
    {
        // Given
        $request = Request::create('/sse/stream', 'GET');
        $request->server->set('REMOTE_ADDR', '***********');
        $request->headers->set('User-Agent', 'Mozilla/5.0 Chrome/91.0');
        
        // 빠른 요청을 시뮬레이션하기 위해 캐시에 높은 값 설정
        Cache::put('sse_rapid_requests:***********', 150, 60);
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('rapid_requests_detected', '빠른 연속 요청 감지');
        
        // When
        $suspiciousActivities = $this->securityService->detectSuspiciousActivity($request);
        
        // Then
        $this->assertContains('rapid_requests', $suspiciousActivities);
    }
    
    public function test_비정상적인_User_Agent_탐지(): void
    {
        // Given
        $request = Request::create('/sse/stream', 'GET');
        $request->server->set('REMOTE_ADDR', '***********');
        $request->headers->set('User-Agent', 'curl/7.68.0'); // 의심스러운 User-Agent
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('unusual_user_agent_detected', '비정상적인 User-Agent 감지');
        
        // When
        $suspiciousActivities = $this->securityService->detectSuspiciousActivity($request);
        
        // Then
        $this->assertContains('unusual_user_agent', $suspiciousActivities);
    }
    
    public function test_사용자_다중_IP_접근_탐지(): void
    {
        // Given
        $user = User::factory()->create();
        $request = Request::create('/sse/stream', 'GET');
        $request->server->set('REMOTE_ADDR', '***********');
        $request->headers->set('User-Agent', 'Mozilla/5.0 Chrome/91.0');
        
        // 이미 여러 IP에서 접근한 것으로 시뮬레이션
        Cache::put("sse_user_ips:{$user->id}", [
            '***********',
            '***********',
            '***********'
        ], 3600);
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('multiple_ips_detected', '사용자 다중 IP 접근 감지');
        
        // When
        $suspiciousActivities = $this->securityService->detectSuspiciousActivity($request, $user);
        
        // Then
        $this->assertContains('multiple_ips_per_user', $suspiciousActivities);
    }
    
    public function test_인증_실패_기록_및_패턴_탐지(): void
    {
        // Given
        $ip = '***********';
        
        $this->mockLogger->expects($this->exactly(5)) // 5번 실패 기록
            ->method('logAuthenticationFailure')
            ->with('session_invalid');
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('auth_failure_pattern_detected', '인증 실패 패턴 감지');
        
        // When - 5번 인증 실패 기록
        for ($i = 0; $i < 5; $i++) {
            $this->securityService->recordAuthFailure($ip);
        }
        
        // 패턴 탐지 요청
        $request = Request::create('/sse/stream', 'GET');
        $request->server->set('REMOTE_ADDR', $ip);
        $request->headers->set('User-Agent', 'Mozilla/5.0 Chrome/91.0');
        
        $suspiciousActivities = $this->securityService->detectSuspiciousActivity($request);
        
        // Then
        $this->assertContains('auth_failure_pattern', $suspiciousActivities);
    }
    
    public function test_IP_차단_및_확인(): void
    {
        // Given
        $ip = '***********';
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('ip_blocked', 'IP 주소 차단');
        
        // When
        $this->assertFalse($this->securityService->isIpBlocked($ip));
        
        $this->securityService->blockIp($ip, 30);
        
        // Then
        $this->assertTrue($this->securityService->isIpBlocked($ip));
    }
    
    public function test_보안_헤더_검증_성공(): void
    {
        // Given
        $request = Request::create('/sse/stream', 'GET');
        $request->headers->set('Origin', config('app.url'));
        $request->headers->set('Referer', config('app.url') . '/dashboard');
        
        // When
        $result = $this->securityService->validateSecurityHeaders($request);
        
        // Then
        $this->assertTrue($result);
    }
    
    public function test_보안_헤더_검증_실패_잘못된_Origin(): void
    {
        // Given
        $request = Request::create('/sse/stream', 'GET');
        $request->headers->set('Origin', 'https://malicious-site.com');
        $request->server->set('REMOTE_ADDR', '***********');
        
        $this->mockLogger->expects($this->once())
            ->method('logSecurityEvent')
            ->with('invalid_origin', '허용되지 않은 Origin 헤더');
        
        // When
        $result = $this->securityService->validateSecurityHeaders($request);
        
        // Then
        $this->assertFalse($result);
    }
    
    public function test_보안_이벤트_요약_생성(): void
    {
        // Given
        $ip = '***********';
        $userId = 123;
        
        // 일부 보안 데이터 설정
        Cache::put("sse_auth_failures:{$ip}", 3, 3600);
        Cache::put("sse_rapid_requests:{$ip}", 50, 60);
        Cache::put("sse_user_ips:{$userId}", ['***********', '***********'], 3600);
        
        // When
        $summary = $this->securityService->generateSecuritySummary($ip, $userId);
        
        // Then
        $this->assertEquals($ip, $summary['ip']);
        $this->assertEquals($userId, $summary['user_id']);
        $this->assertFalse($summary['is_blocked']);
        $this->assertEquals(3, $summary['auth_failures']);
        $this->assertEquals(50, $summary['rapid_requests']);
        $this->assertCount(2, $summary['user_ips']);
        $this->assertArrayHasKey('timestamp', $summary);
    }
    
    public function test_보안_카운터_초기화(): void
    {
        // Given
        $ip = '***********';
        $userId = 123;
        
        // 보안 데이터 설정
        Cache::put("sse_auth_failures:{$ip}", 5, 3600);
        Cache::put("sse_rapid_requests:{$ip}", 100, 60);
        Cache::put("sse_blocked_ip:{$ip}", true, 3600);
        Cache::put("sse_user_ips:{$userId}", ['***********'], 3600);
        
        // When
        $this->securityService->resetSecurityCounters($ip, $userId);
        
        // Then
        $this->assertFalse(Cache::has("sse_auth_failures:{$ip}"));
        $this->assertFalse(Cache::has("sse_rapid_requests:{$ip}"));
        $this->assertFalse(Cache::has("sse_blocked_ip:{$ip}"));
        $this->assertFalse(Cache::has("sse_user_ips:{$userId}"));
    }
}