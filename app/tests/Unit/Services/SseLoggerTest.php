<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Services\SseLogger;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class SseLoggerTest extends TestCase
{
    use RefreshDatabase;
    
    private SseLogger $sseLogger;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->sseLogger = new SseLogger();
        
        // 로그 채널 모킹
        Log::shouldReceive('channel')
            ->with('sse')
            ->andReturnSelf();
    }
    
    public function test_연결_로그_기록(): void
    {
        // Given
        $connectionId = 'test-connection-123';
        $user = User::factory()->create([
            'name' => '테스트 사용자',
        ]);
        
        // Mock log expectation
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 SSE 연결 설정',
                \Mockery::on(function ($context) use ($connectionId, $user) {
                    return $context['connection_id'] === $connectionId
                        && $context['user_id'] === $user->id
                        && $context['user_name'] === $user->name;
                })
            );
        
        // When
        $this->sseLogger->logConnection($connectionId, $user);
        
        // Then - Mock 검증은 자동으로 수행됨
        $this->assertTrue(true);
    }
    
    public function test_연결_해제_로그_기록(): void
    {
        // Given
        $connectionId = 'test-connection-123';
        $user = User::factory()->create();
        $reason = 'error';
        
        // Mock log expectation
        Log::shouldReceive('warning')
            ->once()
            ->with(
                '🔗 SSE 연결 해제',
                \Mockery::on(function ($context) use ($connectionId, $reason) {
                    return $context['connection_id'] === $connectionId
                        && $context['disconnect_reason'] === $reason;
                })
            );
        
        // When
        $this->sseLogger->logDisconnection($connectionId, $user, $reason);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_메시지_전송_성공_로그_기록(): void
    {
        // Given
        $type = 'notification';
        $data = ['title' => '테스트 알림', 'message' => '테스트 메시지'];
        $recipients = ['conn-1', 'conn-2'];
        
        // Mock log expectation
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 SSE 메시지 전송 성공',
                \Mockery::on(function ($context) use ($type, $recipients) {
                    return $context['message_type'] === $type
                        && $context['recipient_count'] === count($recipients)
                        && $context['success'] === true;
                })
            );
        
        // When
        $this->sseLogger->logMessage($type, $data, $recipients, true);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_메시지_전송_실패_로그_기록(): void
    {
        // Given
        $type = 'notification';
        $data = ['title' => '테스트 알림'];
        $recipients = ['conn-1'];
        
        // Mock log expectation
        Log::shouldReceive('warning')
            ->once()
            ->with(
                '🔗 SSE 메시지 전송 실패',
                \Mockery::on(function ($context) use ($type) {
                    return $context['message_type'] === $type
                        && $context['success'] === false;
                })
            );
        
        // When
        $this->sseLogger->logMessage($type, $data, $recipients, false);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_브로드캐스트_로그_기록(): void
    {
        // Given
        $type = 'data_update';
        $data = ['model' => 'categories', 'action' => 'updated'];
        $connectionCount = 50;
        
        // Mock log expectation
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 SSE 브로드캐스트 전송',
                \Mockery::on(function ($context) use ($type, $connectionCount) {
                    return $context['broadcast_type'] === $type
                        && $context['connection_count'] === $connectionCount;
                })
            );
        
        // When
        $this->sseLogger->logBroadcast($type, $data, $connectionCount);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_오프라인_알림_저장_로그_기록(): void
    {
        // Given
        $userId = 123;
        $data = ['title' => '오프라인 알림', 'message' => '테스트'];
        
        // Mock log expectation
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 SSE 오프라인 알림 저장',
                \Mockery::on(function ($context) use ($userId) {
                    return $context['user_id'] === $userId;
                })
            );
        
        // When
        $this->sseLogger->logOfflineNotification($userId, $data);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_하트비트_실패_로그_기록(): void
    {
        // Given
        $connectionId = 'test-connection-123';
        
        // Mock log expectation
        Log::shouldReceive('warning')
            ->once()
            ->with(
                '🔗 SSE 하트비트 실패',
                \Mockery::on(function ($context) use ($connectionId) {
                    return $context['connection_id'] === $connectionId;
                })
            );
        
        // When
        $this->sseLogger->logHeartbeatFailure($connectionId);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_인증_실패_로그_기록(): void
    {
        // Given
        $reason = 'invalid_user';
        
        // Mock log expectation - 마스킹 로직을 고려하여 더 유연하게 검증
        Log::shouldReceive('warning')
            ->once()
            ->with(
                '🔗 SSE 인증 실패',
                \Mockery::on(function ($context) use ($reason) {
                    return isset($context['auth_failure_reason'])
                        && str_contains($context['auth_failure_reason'], $reason);
                })
            );
        
        // When
        $this->sseLogger->logAuthenticationFailure($reason);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_Rate_Limit_초과_로그_기록(): void
    {
        // Given
        $ip = '***********';
        $attemptCount = 10;
        
        // Mock log expectation
        Log::shouldReceive('warning')
            ->once()
            ->with(
                '🔗 SSE Rate Limit 초과',
                \Mockery::on(function ($context) use ($ip, $attemptCount) {
                    return $context['ip_address'] === $ip
                        && $context['attempt_count'] === $attemptCount;
                })
            );
        
        // When
        $this->sseLogger->logRateLimitExceeded($ip, $attemptCount);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_시스템_오류_로그_기록(): void
    {
        // Given
        $message = '테스트 오류';
        $exception = new Exception('테스트 예외', 500);
        
        // Mock log expectation
        Log::shouldReceive('error')
            ->once()
            ->with(
                '🔗 SSE 시스템 오류',
                \Mockery::on(function ($context) use ($message, $exception) {
                    return $context['error_message'] === $message
                        && $context['exception']['message'] === $exception->getMessage()
                        && $context['exception']['code'] === $exception->getCode();
                })
            );
        
        // When
        $this->sseLogger->logError($message, $exception);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_크리티컬_오류_로그_기록(): void
    {
        // Given
        $message = '크리티컬 오류';
        $exception = new Exception('심각한 오류');
        
        // Mock log expectation
        Log::shouldReceive('critical')
            ->once()
            ->with(
                '🔗 SSE 크리티컬 오류',
                \Mockery::on(function ($context) use ($message) {
                    return $context['critical_error_message'] === $message;
                })
            );
        
        // When
        $this->sseLogger->logCriticalError($message, $exception);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_성능_메트릭_로그_기록(): void
    {
        // Given
        $metrics = [
            'active_connections' => 100,
            'messages_per_second' => 50,
            'memory_usage' => '128MB',
        ];
        
        // Mock log expectation
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 SSE 성능 메트릭',
                \Mockery::on(function ($context) use ($metrics) {
                    return $context['metrics'] === $metrics;
                })
            );
        
        // When
        $this->sseLogger->logPerformanceMetrics($metrics);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_보안_이벤트_로그_기록(): void
    {
        // Given
        $eventType = 'suspicious_activity';
        $description = '의심스러운 연결 시도';
        
        // Mock log expectation
        Log::shouldReceive('warning')
            ->once()
            ->with(
                '🔗 SSE 보안 이벤트',
                \Mockery::on(function ($context) use ($eventType, $description) {
                    return $context['security_event_type'] === $eventType
                        && $context['description'] === $description;
                })
            );
        
        // When
        $this->sseLogger->logSecurityEvent($eventType, $description);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_민감_정보_마스킹(): void
    {
        // Given
        $sensitiveData = [
            'password' => 'secret123',
            'token' => 'abc123token',
            'normal_field' => 'normal_value',
        ];
        
        // Mock log expectation
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 테스트 메시지',
                \Mockery::on(function ($context) {
                    // 민감 정보가 마스킹되었는지 확인
                    return isset($context['test_data']['password'])
                        && $context['test_data']['password'] === '***'
                        && isset($context['test_data']['token'])
                        && $context['test_data']['token'] === '***'
                        && $context['test_data']['normal_field'] === 'normal_value';
                })
            );
        
        // When
        $this->sseLogger->info('테스트 메시지', ['test_data' => $sensitiveData]);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_대용량_데이터_잘림(): void
    {
        // Given
        $largeData = str_repeat('A', 3000); // 3KB 데이터
        
        // Mock log expectation - 데이터가 잘린 형태로 전달됨
        Log::shouldReceive('info')
            ->once()
            ->with(
                '🔗 대용량 데이터 테스트',
                \Mockery::on(function ($context) {
                    // 데이터가 잘렸는지 확인 (더 유연한 검증)
                    return isset($context['large_data'])
                        && is_string($context['large_data'])
                        && (str_contains($context['large_data'], '...[잘림]') 
                            || strlen($context['large_data']) < 3000);
                })
            );
        
        // When
        $this->sseLogger->info('대용량 데이터 테스트', ['large_data' => $largeData]);
        
        // Then
        $this->assertTrue(true);
    }
    
    public function test_디버그_로그는_개발환경에서만_기록(): void
    {
        // Given - 프로덕션 환경 시뮬레이션
        app()->detectEnvironment(function () {
            return 'production';
        });
        
        // Mock - 디버그 로그가 호출되지 않아야 함
        Log::shouldNotReceive('debug');
        
        // When
        $this->sseLogger->debug('디버그 메시지');
        
        // Then
        $this->assertTrue(true);
    }
}