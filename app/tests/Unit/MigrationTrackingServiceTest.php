<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\MigrationTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class MigrationTrackingServiceTest extends TestCase
{
    use RefreshDatabase;

    private MigrationTrackingService $trackingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->trackingService = new MigrationTrackingService();
    }

    /** @test */
    public function SSE_전환율_기록_테스트()
    {
        // Given
        $userId = 1;
        $successful = true;
        $date = now()->format('Y-m-d');
        $hour = now()->format('H');

        // When
        $this->trackingService->recordSSEConversion($userId, $successful);

        // Then
        // 일별 통계 확인
        $dailyKey = "sse_conversion_daily:{$date}";
        $dailyStats = Cache::get($dailyKey);
        
        $this->assertNotNull($dailyStats);
        $this->assertEquals(1, $dailyStats['total_attempts']);
        $this->assertEquals(1, $dailyStats['successful_conversions']);
        $this->assertEquals(0, $dailyStats['failed_conversions']);
        $this->assertContains($userId, $dailyStats['unique_users']);
        
        // 시간별 통계 확인
        $hourlyKey = "sse_conversion_hourly:{$date}:{$hour}";
        $hourlyStats = Cache::get($hourlyKey);
        
        $this->assertNotNull($hourlyStats);
        $this->assertEquals(1, $hourlyStats['total_attempts']);
        $this->assertEquals(1, $hourlyStats['successful_conversions']);
        
        // 사용자별 통계 확인
        $userKey = "user_sse_conversion:{$userId}";
        $userStats = Cache::get($userKey);
        
        $this->assertNotNull($userStats);
        $this->assertEquals(1, $userStats['total_attempts']);
        $this->assertEquals(1, $userStats['successful_attempts']);
        $this->assertEquals('converted', $userStats['conversion_status']);
    }

    /** @test */
    public function 실패한_SSE_전환_기록_테스트()
    {
        // Given
        $userId = 2;
        $successful = false;
        $date = now()->format('Y-m-d');

        // When
        $this->trackingService->recordSSEConversion($userId, $successful);

        // Then
        $dailyKey = "sse_conversion_daily:{$date}";
        $dailyStats = Cache::get($dailyKey);
        
        $this->assertEquals(1, $dailyStats['total_attempts']);
        $this->assertEquals(0, $dailyStats['successful_conversions']);
        $this->assertEquals(1, $dailyStats['failed_conversions']);
        
        $userKey = "user_sse_conversion:{$userId}";
        $userStats = Cache::get($userKey);
        
        $this->assertEquals(1, $userStats['total_attempts']);
        $this->assertEquals(0, $userStats['successful_attempts']);
        $this->assertEquals('pending', $userStats['conversion_status']);
    }

    /** @test */
    public function 마이그레이션_진행률_계산_테스트()
    {
        // Given
        $days = 3;
        
        // 테스트 데이터 설정
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_conversion_daily:{$date}";
            
            $dailyData = [
                'total_attempts' => 100,
                'successful_conversions' => 80,
                'failed_conversions' => 20,
                'unique_users' => range(1, 50) // 50명의 고유 사용자
            ];
            
            Cache::put($dailyKey, $dailyData, 86400);
        }

        // When
        $progress = $this->trackingService->calculateMigrationProgress($days);

        // Then
        $this->assertEquals($days, $progress['period']['days']);
        $this->assertEquals(300, $progress['conversion_stats']['total_attempts']); // 100 * 3
        $this->assertEquals(240, $progress['conversion_stats']['successful_conversions']); // 80 * 3
        $this->assertEquals(60, $progress['conversion_stats']['failed_conversions']); // 20 * 3
        $this->assertEquals(80.0, $progress['conversion_stats']['conversion_rate']); // 240/300 * 100
        $this->assertEquals(50, $progress['conversion_stats']['unique_users']); // 중복 제거된 고유 사용자
        
        // 일별 분석 확인
        $this->assertCount($days, $progress['daily_breakdown']);
        
        foreach ($progress['daily_breakdown'] as $date => $dayData) {
            $this->assertEquals(100, $dayData['total_attempts']);
            $this->assertEquals(80, $dayData['successful_conversions']);
            $this->assertEquals(80.0, $dayData['success_rate']);
        }
    }

    /** @test */
    public function 마이그레이션_리포트_생성_테스트()
    {
        // Given
        $options = [
            'days' => 7,
            'include_details' => true,
            'include_recommendations' => true
        ];
        
        // 기본 데이터 설정
        $date = now()->format('Y-m-d');
        $dailyKey = "sse_conversion_daily:{$date}";
        $dailyData = [
            'total_attempts' => 100,
            'successful_conversions' => 85,
            'failed_conversions' => 15,
            'unique_users' => range(1, 50)
        ];
        Cache::put($dailyKey, $dailyData, 86400);

        // When
        $report = $this->trackingService->generateMigrationReport($options);

        // Then
        $this->assertArrayHasKey('generated_at', $report);
        $this->assertEquals(7, $report['report_period']);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('system_health', $report);
        $this->assertArrayHasKey('performance_analysis', $report);
        $this->assertArrayHasKey('detailed_metrics', $report);
        $this->assertArrayHasKey('recommendations', $report);
        
        // 시스템 건강 상태 확인
        $this->assertArrayHasKey('sse_service_status', $report['system_health']);
        $this->assertArrayHasKey('pusher_service_status', $report['system_health']);
        $this->assertArrayHasKey('redis_status', $report['system_health']);
    }

    /** @test */
    public function 사용자_채택_분석_테스트()
    {
        // Given
        $days = 7;
        
        // 현재 기간 사용자 (1-30)
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_conversion_daily:{$date}";
            
            Cache::put($dailyKey, [
                'total_attempts' => 50,
                'successful_conversions' => 40,
                'failed_conversions' => 10,
                'unique_users' => range(1, 30)
            ], 86400);
        }
        
        // 이전 기간 사용자 (21-50) - 10명은 겹침, 20명은 새로움
        for ($i = $days; $i < $days * 2; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyKey = "sse_conversion_daily:{$date}";
            
            Cache::put($dailyKey, [
                'total_attempts' => 40,
                'successful_conversions' => 30,
                'failed_conversions' => 10,
                'unique_users' => range(21, 50)
            ], 86400);
        }

        // When
        $progress = $this->trackingService->calculateMigrationProgress($days);

        // Then
        $adoption = $progress['user_adoption'];
        
        $this->assertEquals(30, $adoption['active_users']); // 현재 기간 활성 사용자
        $this->assertEquals(20, $adoption['new_sse_users']); // 새로운 사용자 (1-20)
        $this->assertEquals(10, $adoption['returning_users']); // 재방문 사용자 (21-30)
        $this->assertEquals(20, $adoption['churned_users']); // 이탈 사용자 (31-50)
    }

    /** @test */
    public function 메트릭_정리_테스트()
    {
        // Given
        $retentionDays = 30;
        
        // 오래된 데이터 생성 (35일 전)
        $oldDate = now()->subDays(35)->format('Y-m-d');
        $oldKey = "sse_conversion_daily:{$oldDate}";
        Cache::put($oldKey, ['test' => 'data'], 86400);
        
        // 최근 데이터 생성 (5일 전)
        $recentDate = now()->subDays(5)->format('Y-m-d');
        $recentKey = "sse_conversion_daily:{$recentDate}";
        Cache::put($recentKey, ['test' => 'data'], 86400);

        // When
        $cleanedCount = $this->trackingService->cleanupOldMetrics();

        // Then
        $this->assertGreaterThanOrEqual(0, $cleanedCount);
        
        // 최근 데이터는 남아있어야 함
        $this->assertNotNull(Cache::get($recentKey));
    }

    /** @test */
    public function 빈_데이터로_진행률_계산_테스트()
    {
        // Given
        $days = 7;
        // 데이터 없이 테스트

        // When
        $progress = $this->trackingService->calculateMigrationProgress($days);

        // Then
        $this->assertEquals(0, $progress['conversion_stats']['total_attempts']);
        $this->assertEquals(0, $progress['conversion_stats']['successful_conversions']);
        $this->assertEquals(0, $progress['conversion_stats']['conversion_rate']);
        $this->assertEquals(0, $progress['conversion_stats']['unique_users']);
        
        // 일별 분석도 모두 0이어야 함
        foreach ($progress['daily_breakdown'] as $dayData) {
            $this->assertEquals(0, $dayData['total_attempts']);
            $this->assertEquals(0, $dayData['success_rate']);
        }
    }

    /** @test */
    public function 여러_사용자_전환_기록_테스트()
    {
        // Given
        $users = [1, 2, 3, 4, 5];
        $date = now()->format('Y-m-d');

        // When - 여러 사용자의 전환 기록
        foreach ($users as $userId) {
            $successful = $userId % 2 === 0; // 짝수 ID는 성공, 홀수는 실패
            $this->trackingService->recordSSEConversion($userId, $successful);
        }

        // Then
        $dailyKey = "sse_conversion_daily:{$date}";
        $dailyStats = Cache::get($dailyKey);
        
        $this->assertEquals(5, $dailyStats['total_attempts']);
        $this->assertEquals(2, $dailyStats['successful_conversions']); // 2, 4번 사용자
        $this->assertEquals(3, $dailyStats['failed_conversions']); // 1, 3, 5번 사용자
        $this->assertEquals(5, count($dailyStats['unique_users']));
        
        // 각 사용자별 상태 확인
        foreach ($users as $userId) {
            $userKey = "user_sse_conversion:{$userId}";
            $userStats = Cache::get($userKey);
            
            $this->assertEquals(1, $userStats['total_attempts']);
            
            if ($userId % 2 === 0) {
                $this->assertEquals(1, $userStats['successful_attempts']);
                $this->assertEquals('converted', $userStats['conversion_status']);
            } else {
                $this->assertEquals(0, $userStats['successful_attempts']);
                $this->assertEquals('pending', $userStats['conversion_status']);
            }
        }
    }
}