<?php

namespace Tests\Unit;

use App\Models\SseOfflineNotification;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

/**
 * SSE 오프라인 알림 모델 단위 테스트
 */
class SseOfflineNotificationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 테스트용 사용자 생성
        $this->user = User::factory()->create();
    }

    /**
     * 오프라인 알림 생성 테스트
     */
    public function test_can_create_offline_notification(): void
    {
        $notificationData = [
            'title' => '테스트 알림',
            'message' => '테스트 메시지입니다',
            'priority' => 'normal'
        ];

        $notification = SseOfflineNotification::create([
            'user_id' => $this->user->id,
            'type' => 'notification',
            'data' => $notificationData,
            'created_at' => Carbon::now()
        ]);

        $this->assertInstanceOf(SseOfflineNotification::class, $notification);
        $this->assertEquals($this->user->id, $notification->user_id);
        $this->assertEquals('notification', $notification->type);
        $this->assertEquals($notificationData, $notification->data);
        $this->assertNull($notification->delivered_at);
        $this->assertFalse($notification->isDelivered());
    }

    /**
     * 사용자와의 관계 테스트
     */
    public function test_belongs_to_user(): void
    {
        $notification = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id
        ]);

        $this->assertInstanceOf(User::class, $notification->user);
        $this->assertEquals($this->user->id, $notification->user->id);
    }

    /**
     * 미전달 알림 스코프 테스트
     */
    public function test_undelivered_scope(): void
    {
        // 미전달 알림 생성
        $undeliveredNotification = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 전달 완료 알림 생성
        $deliveredNotification = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()
        ]);

        $undeliveredNotifications = SseOfflineNotification::undelivered()->get();

        $this->assertCount(1, $undeliveredNotifications);
        $this->assertEquals($undeliveredNotification->id, $undeliveredNotifications->first()->id);
    }

    /**
     * 사용자별 알림 스코프 테스트
     */
    public function test_for_user_scope(): void
    {
        $otherUser = User::factory()->create();

        // 현재 사용자의 알림
        $userNotification = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id
        ]);

        // 다른 사용자의 알림
        $otherUserNotification = SseOfflineNotification::factory()->create([
            'user_id' => $otherUser->id
        ]);

        $userNotifications = SseOfflineNotification::forUser($this->user->id)->get();

        $this->assertCount(1, $userNotifications);
        $this->assertEquals($userNotification->id, $userNotifications->first()->id);
    }

    /**
     * 알림 전달 완료 표시 테스트
     */
    public function test_mark_as_delivered(): void
    {
        $notification = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        $this->assertFalse($notification->isDelivered());

        $result = $notification->markAsDelivered();

        $this->assertTrue($result);
        $this->assertTrue($notification->isDelivered());
        $this->assertNotNull($notification->delivered_at);
        $this->assertInstanceOf(Carbon::class, $notification->delivered_at);
    }

    /**
     * 데이터 캐스팅 테스트
     */
    public function test_data_casting(): void
    {
        $testData = [
            'title' => '테스트 제목',
            'message' => '테스트 메시지',
            'metadata' => [
                'priority' => 'high',
                'category' => 'system'
            ]
        ];

        $notification = SseOfflineNotification::create([
            'user_id' => $this->user->id,
            'type' => 'notification',
            'data' => $testData,
            'created_at' => Carbon::now()
        ]);

        // 데이터베이스에서 다시 조회
        $retrievedNotification = SseOfflineNotification::find($notification->id);

        $this->assertIsArray($retrievedNotification->data);
        $this->assertEquals($testData, $retrievedNotification->data);
        $this->assertEquals('high', $retrievedNotification->data['metadata']['priority']);
    }

    /**
     * 타임스탬프 처리 테스트
     */
    public function test_timestamps(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $notification = SseOfflineNotification::create([
            'user_id' => $this->user->id,
            'type' => 'notification',
            'data' => ['test' => 'data'],
            'created_at' => $now
        ]);

        $this->assertEquals($now->toDateTimeString(), $notification->created_at->toDateTimeString());
        $this->assertNull($notification->delivered_at);

        // updated_at은 사용하지 않음
        $this->assertNull($notification->updated_at);

        Carbon::setTestNow();
    }

    /**
     * 복합 스코프 테스트 (사용자별 + 미전달)
     */
    public function test_combined_scopes(): void
    {
        $otherUser = User::factory()->create();

        // 현재 사용자의 미전달 알림
        $userUndelivered = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 현재 사용자의 전달 완료 알림
        $userDelivered = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()
        ]);

        // 다른 사용자의 미전달 알림
        $otherUserUndelivered = SseOfflineNotification::factory()->create([
            'user_id' => $otherUser->id,
            'delivered_at' => null
        ]);

        $userUndeliveredNotifications = SseOfflineNotification::forUser($this->user->id)
            ->undelivered()
            ->get();

        $this->assertCount(1, $userUndeliveredNotifications);
        $this->assertEquals($userUndelivered->id, $userUndeliveredNotifications->first()->id);
    }
}