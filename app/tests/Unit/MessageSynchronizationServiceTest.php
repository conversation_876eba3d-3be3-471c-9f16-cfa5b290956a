<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\MessageSynchronizationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Config;

class MessageSynchronizationServiceTest extends TestCase
{
    use RefreshDatabase;

    private MessageSynchronizationService $syncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->syncService = new MessageSynchronizationService();
        
        // 테스트용 설정
        Config::set('sse.migration.hybrid_mode', true);
        Config::set('sse.migration.pusher_fallback_enabled', true);
    }

    /** @test */
    public function 메시지_등록_성공_테스트()
    {
        // Given
        $messageId = 'test_message_001';
        $messageData = ['content' => '테스트 메시지'];
        $channels = ['global', 'user.1'];

        // When
        $result = $this->syncService->registerMessage($messageId, $messageData, $channels);

        // Then
        $this->assertTrue($result);
        
        // 캐시에 메시지가 저장되었는지 확인
        $cacheKey = "message_sync:{$messageId}";
        $cachedMessage = Cache::get($cacheKey);
        
        $this->assertNotNull($cachedMessage);
        $this->assertEquals($messageId, $cachedMessage['id']);
        $this->assertEquals($messageData, $cachedMessage['data']);
        $this->assertEquals($channels, $cachedMessage['channels']);
        $this->assertEquals('pending', $cachedMessage['status']);
    }

    /** @test */
    public function 중복_메시지_등록_방지_테스트()
    {
        // Given
        $messageId = 'duplicate_message';
        $messageData = ['content' => '중복 메시지'];
        $channels = ['global'];

        // When - 첫 번째 등록
        $firstResult = $this->syncService->registerMessage($messageId, $messageData, $channels);
        
        // When - 중복 등록 시도
        $secondResult = $this->syncService->registerMessage($messageId, $messageData, $channels);

        // Then
        $this->assertTrue($firstResult);
        $this->assertFalse($secondResult);
    }

    /** @test */
    public function 메시지_처리_완료_표시_테스트()
    {
        // Given
        $messageId = 'process_test_message';
        $messageData = ['content' => '처리 테스트'];
        $channels = ['global'];
        
        $this->syncService->registerMessage($messageId, $messageData, $channels);

        // When - SSE 처리 완료
        $this->syncService->markMessageProcessed($messageId, 'sse');

        // Then
        $cacheKey = "message_sync:{$messageId}";
        $cachedMessage = Cache::get($cacheKey);
        
        $this->assertContains('sse', $cachedMessage['processed_by']);
        $this->assertEquals('pending', $cachedMessage['status']); // 아직 pusher 처리 대기 중
        
        // When - Pusher 처리 완료
        $this->syncService->markMessageProcessed($messageId, 'pusher');
        
        // Then
        $cachedMessage = Cache::get($cacheKey);
        $this->assertContains('pusher', $cachedMessage['processed_by']);
        $this->assertEquals('completed', $cachedMessage['status']); // 모든 처리 완료
    }

    /** @test */
    public function 동기화_상태_확인_테스트()
    {
        // Given
        $messageId = 'sync_status_test';
        $messageData = ['content' => '상태 확인 테스트'];
        $channels = ['global'];
        
        $this->syncService->registerMessage($messageId, $messageData, $channels);
        $this->syncService->markMessageProcessed($messageId, 'sse');

        // When
        $status = $this->syncService->checkSynchronizationStatus($messageId);

        // Then
        $this->assertTrue($status['exists']);
        $this->assertEquals('pending', $status['status']);
        $this->assertContains('sse', $status['processed_by']);
        $this->assertContains('pusher', $status['pending_processors']);
    }

    /** @test */
    public function 존재하지_않는_메시지_상태_확인_테스트()
    {
        // Given
        $messageId = 'non_existent_message';

        // When
        $status = $this->syncService->checkSynchronizationStatus($messageId);

        // Then
        $this->assertFalse($status['exists']);
        $this->assertEquals('not_found', $status['status']);
    }

    /** @test */
    public function 동기화_락_획득_및_해제_테스트()
    {
        // Given
        $lockKey = 'test_sync_lock';

        // When - 락 획득
        $acquired = $this->syncService->acquireSyncLock($lockKey);

        // Then
        $this->assertTrue($acquired);
        
        // When - 동일한 락 재획득 시도
        $secondAcquire = $this->syncService->acquireSyncLock($lockKey);
        
        // Then
        $this->assertFalse($secondAcquire);
        
        // When - 락 해제
        $this->syncService->releaseSyncLock($lockKey);
        
        // When - 락 해제 후 재획득
        $thirdAcquire = $this->syncService->acquireSyncLock($lockKey);
        
        // Then
        $this->assertTrue($thirdAcquire);
        
        // 정리
        $this->syncService->releaseSyncLock($lockKey);
    }

    /** @test */
    public function 메시지_재시도_처리_테스트()
    {
        // Given
        $messageId = 'retry_test_message';
        $messageData = ['content' => '재시도 테스트'];
        $channels = ['global'];
        $processor = 'sse';
        
        $this->syncService->registerMessage($messageId, $messageData, $channels);

        // When - 첫 번째 재시도
        $firstRetry = $this->syncService->retryMessage($messageId, $processor);
        
        // Then
        $this->assertTrue($firstRetry);
        
        // When - 최대 재시도 횟수까지 시도
        for ($i = 1; $i < 3; $i++) {
            $retry = $this->syncService->retryMessage($messageId, $processor);
            $this->assertTrue($retry);
        }
        
        // When - 최대 재시도 횟수 초과
        $finalRetry = $this->syncService->retryMessage($messageId, $processor);
        
        // Then
        $this->assertFalse($finalRetry);
    }

    /** @test */
    public function 메시지_실패_표시_테스트()
    {
        // Given
        $messageId = 'fail_test_message';
        $messageData = ['content' => '실패 테스트'];
        $channels = ['global'];
        $processor = 'pusher';
        
        $this->syncService->registerMessage($messageId, $messageData, $channels);

        // When
        $this->syncService->markMessageFailed($messageId, $processor);

        // Then
        $cacheKey = "message_sync:{$messageId}";
        $cachedMessage = Cache::get($cacheKey);
        
        $this->assertEquals('failed', $cachedMessage['status']);
        $this->assertContains($processor, $cachedMessage['failed_by']);
    }

    /** @test */
    public function 동기화_통계_업데이트_테스트()
    {
        // Given
        $hour = now()->format('Y-m-d-H');
        $statsKey = "sync_stats:{$hour}";

        // When - 다양한 이벤트 발생
        $this->syncService->updateSyncStats('message_registered');
        $this->syncService->updateSyncStats('message_completed');
        $this->syncService->updateSyncStats('message_failed');
        $this->syncService->updateSyncStats('duplicate_prevented');

        // Then
        $stats = Cache::get($statsKey);
        
        $this->assertEquals(1, $stats['total']);
        $this->assertEquals(1, $stats['completed']);
        $this->assertEquals(1, $stats['failed']);
        $this->assertEquals(1, $stats['duplicates']);
        $this->assertEquals(0, $stats['pending']); // completed와 failed로 인해 0
    }

    /** @test */
    public function 동기화_통계_조회_테스트()
    {
        // Given
        $hour = now()->format('Y-m-d-H');
        $statsKey = "sync_stats:{$hour}";
        
        $testStats = [
            'total' => 100,
            'completed' => 80,
            'failed' => 15,
            'pending' => 5,
            'duplicates' => 10,
            'conflicts' => 2
        ];
        
        Cache::put($statsKey, $testStats, 86400);

        // When
        $stats = $this->syncService->getSynchronizationStats(1);

        // Then
        $this->assertEquals(100, $stats['total_messages']);
        $this->assertEquals(80, $stats['completed_messages']);
        $this->assertEquals(15, $stats['failed_messages']);
        $this->assertEquals(5, $stats['pending_messages']);
        $this->assertEquals(10, $stats['duplicate_prevented']);
        $this->assertEquals(2, $stats['sync_conflicts']);
        $this->assertEquals(80.0, $stats['success_rate']);
        $this->assertEquals(15.0, $stats['failure_rate']);
    }

    /** @test */
    public function 만료된_메시지_정리_테스트()
    {
        // Given
        $expiredMessageId = 'expired_message';
        $activeMessageId = 'active_message';
        
        // 만료된 메시지 (5분 전)
        $expiredMessage = [
            'id' => $expiredMessageId,
            'data' => ['content' => '만료된 메시지'],
            'timestamp' => now()->subMinutes(10)->toISOString(),
            'status' => 'pending'
        ];
        
        // 활성 메시지 (최근)
        $activeMessage = [
            'id' => $activeMessageId,
            'data' => ['content' => '활성 메시지'],
            'timestamp' => now()->toISOString(),
            'status' => 'pending'
        ];
        
        Cache::put("message_sync:{$expiredMessageId}", $expiredMessage, 300);
        Cache::put("message_sync:{$activeMessageId}", $activeMessage, 300);

        // When
        $cleanedCount = $this->syncService->cleanupExpiredMessages();

        // Then
        $this->assertGreaterThanOrEqual(1, $cleanedCount);
        
        // 만료된 메시지는 삭제되고 활성 메시지는 남아있어야 함
        $this->assertNull(Cache::get("message_sync:{$expiredMessageId}"));
        $this->assertNotNull(Cache::get("message_sync:{$activeMessageId}"));
    }
}