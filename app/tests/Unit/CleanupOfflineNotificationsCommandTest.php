<?php

namespace Tests\Unit;

use App\Console\Commands\CleanupOfflineNotifications;
use App\Models\SseOfflineNotification;
use App\Models\User;
use App\Services\NotificationManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;
use Mockery;

/**
 * CleanupOfflineNotifications 명령어 단위 테스트
 */
class CleanupOfflineNotificationsCommandTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 테스트용 사용자 생성
        $this->user = User::factory()->create();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 기본 설정으로 알림 정리 테스트
     */
    public function test_cleans_up_notifications_with_default_settings(): void
    {
        // 전달 완료된 오래된 알림들 (7일 이전)
        SseOfflineNotification::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(8),
            'created_at' => Carbon::now()->subDays(10)
        ]);

        // 전달 완료된 최근 알림들 (삭제되지 않아야 함)
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(5),
            'created_at' => Carbon::now()->subDays(6)
        ]);

        // 미전달 오래된 알림들 (30일 이전)
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(35)
        ]);

        // 미전달 최근 알림들 (삭제되지 않아야 함)
        SseOfflineNotification::factory()->count(1)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(25)
        ]);

        // 명령어 실행
        $this->artisan('sse:cleanup-notifications')
            ->expectsOutput('SSE 오프라인 알림 정리 시작')
            ->expectsOutput('전달 완료 알림 삭제 기준: 7일 이전')
            ->expectsOutput('미전달 알림 삭제 기준: 30일 이전')
            ->expectsOutputToContain('전달 완료된 오래된 알림')
            ->expectsOutputToContain('미전달 상태의 오래된 알림')
            ->expectsOutput('정리 완료')
            ->assertExitCode(0);

        // 남은 알림 수 확인
        $remainingCount = SseOfflineNotification::where('user_id', $this->user->id)->count();
        $this->assertEquals(3, $remainingCount); // 2개 최근 전달 완료 + 1개 최근 미전달
    }

    /**
     * 커스텀 일수 설정으로 알림 정리 테스트
     */
    public function test_cleans_up_notifications_with_custom_days(): void
    {
        // 전달 완료된 알림들 (3일 이전)
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(4)
        ]);

        // 미전달 알림들 (10일 이전)
        SseOfflineNotification::factory()->count(1)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(12)
        ]);

        // 명령어 실행 (커스텀 일수)
        $this->artisan('sse:cleanup-notifications', [
            '--delivered-days' => 3,
            '--undelivered-days' => 10
        ])
            ->expectsOutput('전달 완료 알림 삭제 기준: 3일 이전')
            ->expectsOutput('미전달 알림 삭제 기준: 10일 이전')
            ->expectsOutput('전달 완료된 오래된 알림 2개 발견')
            ->expectsOutput('미전달 상태의 오래된 알림 1개 발견')
            ->assertExitCode(0);

        // 모든 알림이 삭제되었는지 확인
        $remainingCount = SseOfflineNotification::where('user_id', $this->user->id)->count();
        $this->assertEquals(0, $remainingCount);
    }

    /**
     * Dry Run 모드 테스트
     */
    public function test_dry_run_mode_does_not_delete_notifications(): void
    {
        // 삭제 대상 알림들 생성
        SseOfflineNotification::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(10)
        ]);

        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(35)
        ]);

        // Dry Run 모드로 명령어 실행
        $this->artisan('sse:cleanup-notifications', ['--dry-run' => true])
            ->expectsOutput('DRY RUN 모드: 실제 삭제는 수행되지 않습니다')
            ->expectsOutput('전달 완료된 오래된 알림 3개 발견')
            ->expectsOutput('DRY RUN: 실제 삭제는 수행되지 않음')
            ->expectsOutput('미전달 상태의 오래된 알림 2개 발견')
            ->expectsOutput('DRY RUN: 실제 삭제는 수행되지 않음')
            ->assertExitCode(0);

        // 알림이 삭제되지 않았는지 확인
        $remainingCount = SseOfflineNotification::where('user_id', $this->user->id)->count();
        $this->assertEquals(5, $remainingCount);
    }

    /**
     * 삭제할 알림이 없는 경우 테스트
     */
    public function test_handles_no_notifications_to_delete(): void
    {
        // 최근 알림들만 생성 (삭제 대상 아님)
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(3)
        ]);

        SseOfflineNotification::factory()->count(1)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(15)
        ]);

        // 명령어 실행
        $this->artisan('sse:cleanup-notifications')
            ->expectsOutput('삭제할 전달 완료 알림이 없습니다')
            ->expectsOutput('삭제할 미전달 알림이 없습니다')
            ->expectsOutput('정리 완료')
            ->assertExitCode(0);

        // 모든 알림이 유지되었는지 확인
        $remainingCount = SseOfflineNotification::where('user_id', $this->user->id)->count();
        $this->assertEquals(3, $remainingCount);
    }

    /**
     * 다중 사용자 알림 정리 테스트
     */
    public function test_cleans_up_notifications_for_multiple_users(): void
    {
        $user2 = User::factory()->create();

        // 첫 번째 사용자의 오래된 알림들
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(10)
        ]);

        // 두 번째 사용자의 오래된 알림들
        SseOfflineNotification::factory()->count(3)->create([
            'user_id' => $user2->id,
            'delivered_at' => Carbon::now()->subDays(10)
        ]);

        // 명령어 실행
        $this->artisan('sse:cleanup-notifications')
            ->expectsOutput('전달 완료된 오래된 알림 5개 발견')
            ->expectsOutput('전달 완료 알림 5개 삭제 완료')
            ->assertExitCode(0);

        // 모든 사용자의 오래된 알림이 삭제되었는지 확인
        $this->assertEquals(0, SseOfflineNotification::count());
    }

    /**
     * 통계 테이블 출력 테스트
     */
    public function test_displays_cleanup_statistics_table(): void
    {
        // 삭제 대상 알림들 생성
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(10)
        ]);

        SseOfflineNotification::factory()->count(1)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null,
            'created_at' => Carbon::now()->subDays(35)
        ]);

        // 명령어 실행
        $this->artisan('sse:cleanup-notifications')
            ->expectsTable(
                ['구분', '삭제된 알림 수'],
                [
                    ['전달 완료 알림', 2],
                    ['미전달 알림', 1],
                    ['총계', 3]
                ]
            )
            ->assertExitCode(0);
    }

    /**
     * 잘못된 옵션 값 처리 테스트
     */
    public function test_handles_invalid_option_values(): void
    {
        // 음수 값으로 명령어 실행
        $this->artisan('sse:cleanup-notifications', [
            '--delivered-days' => -1,
            '--undelivered-days' => -5
        ])
            ->expectsOutput('전달 완료 알림 삭제 기준: -1일 이전')
            ->expectsOutput('미전달 알림 삭제 기준: -5일 이전')
            ->assertExitCode(0);

        // 음수 값이어도 정상적으로 처리되어야 함 (미래 날짜 기준)
    }

    /**
     * 대량 데이터 정리 성능 테스트
     */
    public function test_handles_large_number_of_notifications(): void
    {
        // 대량의 오래된 알림들 생성
        SseOfflineNotification::factory()->count(100)->create([
            'user_id' => $this->user->id,
            'delivered_at' => Carbon::now()->subDays(10)
        ]);

        // 명령어 실행 시간 측정
        $startTime = microtime(true);
        
        $this->artisan('sse:cleanup-notifications')
            ->expectsOutput('전달 완료된 오래된 알림 100개 발견')
            ->expectsOutput('전달 완료 알림 100개 삭제 완료')
            ->assertExitCode(0);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 실행 시간이 합리적인 범위 내인지 확인 (10초 이내)
        $this->assertLessThan(10, $executionTime, '대량 데이터 정리가 너무 오래 걸립니다');

        // 모든 알림이 삭제되었는지 확인
        $this->assertEquals(0, SseOfflineNotification::count());
    }
}