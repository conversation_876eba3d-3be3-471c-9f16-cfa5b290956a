<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\HybridNotificationService;
use App\Services\NotificationManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Mockery;

class HybridNotificationServiceTest extends TestCase
{
    use RefreshDatabase;

    private HybridNotificationService $hybridService;
    private $mockNotificationManager;

    protected function setUp(): void
    {
        parent::setUp();
        
        // NotificationManager 모킹
        $this->mockNotificationManager = Mockery::mock(NotificationManager::class);
        $this->app->instance(NotificationManager::class, $this->mockNotificationManager);
        
        $this->hybridService = new HybridNotificationService($this->mockNotificationManager);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function 전체_알림_전송_테스트()
    {
        // Given
        Config::set('sse.migration.hybrid_mode', false);
        Config::set('sse.migration.pusher_fallback_enabled', false);
        Config::set('sse.migration.migration_percentage', 100);
        
        $data = ['message' => '테스트 알림'];
        $type = 'notification';
        
        $this->mockNotificationManager
            ->shouldReceive('sendToAll')
            ->once()
            ->with($data, $type);

        // When
        $this->hybridService->sendToAll($data, $type);

        // Then
        // 메서드 호출이 성공적으로 완료되었는지 확인
        $this->assertTrue(true);
    }

    /** @test */
    public function 개별_사용자_알림_전송_테스트()
    {
        // Given
        $userId = 1;
        $data = ['message' => '개별 알림'];
        $type = 'notification';
        
        Config::set('sse.migration.hybrid_mode', false);
        Config::set('sse.migration.pusher_fallback_enabled', false);
        Config::set('sse.migration.migration_percentage', 100);
        
        // 사용자 SSE 지원 설정
        Cache::put("user_sse_support:{$userId}", true, 3600);
        
        $this->mockNotificationManager
            ->shouldReceive('sendToUser')
            ->once()
            ->with($userId, $data, $type);

        // When
        $this->hybridService->sendToUser($userId, $data, $type);

        // Then
        $this->assertTrue(true);
    }

    /** @test */
    public function SSE_지원_여부_확인_테스트()
    {
        // Given
        $userId = 1;
        
        // When - 캐시에 없는 경우
        $supportsSSE = $this->invokePrivateMethod($this->hybridService, 'checkUserSSESupport', [$userId]);
        
        // Then
        $this->assertTrue($supportsSSE); // 기본값은 true
        
        // Given - 캐시에 있는 경우
        Cache::put("user_sse_support:{$userId}", false, 3600);
        
        // When
        $supportsSSE = $this->invokePrivateMethod($this->hybridService, 'checkUserSSESupport', [$userId]);
        
        // Then
        $this->assertFalse($supportsSSE);
    }

    /** @test */
    public function 사용자_SSE_지원_상태_업데이트_테스트()
    {
        // Given
        $userId = 1;
        $supportsSSE = true;

        // When
        $this->hybridService->updateUserSSESupport($userId, $supportsSSE);

        // Then
        $cached = Cache::get("user_sse_support:{$userId}");
        $this->assertTrue($cached);
    }

    /** @test */
    public function 마이그레이션_비율에_따른_SSE_사용_결정_테스트()
    {
        // Given - 50% 마이그레이션
        Config::set('sse.migration.migration_percentage', 50);
        
        // When & Then
        $results = [];
        for ($i = 0; $i < 100; $i++) {
            $shouldUse = $this->invokePrivateMethod($this->hybridService, 'shouldUseSse');
            $results[] = $shouldUse;
        }
        
        // 대략 50% 정도가 true여야 함 (확률적이므로 범위로 확인)
        $trueCount = count(array_filter($results));
        $this->assertGreaterThan(30, $trueCount);
        $this->assertLessThan(70, $trueCount);
    }

    /** @test */
    public function 전송_통계_조회_테스트()
    {
        // Given
        $hour = now()->format('Y-m-d-H');
        Cache::put("delivery_stats:sse:{$hour}", 10, 86400);
        Cache::put("delivery_stats:pusher:{$hour}", 5, 86400);

        // When
        $stats = $this->hybridService->getDeliveryStats(1);

        // Then
        $this->assertEquals(10, $stats['sse']);
        $this->assertEquals(5, $stats['pusher']);
        $this->assertEquals(15, $stats['total']);
    }

    /** @test */
    public function 하이브리드_설정_업데이트_테스트()
    {
        // Given
        $settings = [
            'hybrid_mode' => true,
            'pusher_fallback_enabled' => true,
            'migration_percentage' => 75
        ];

        // When
        $this->hybridService->updateHybridSettings($settings);

        // Then
        $this->assertTrue(Config::get('sse.migration.hybrid_mode'));
        $this->assertTrue(Config::get('sse.migration.pusher_fallback_enabled'));
        $this->assertEquals(75, Config::get('sse.migration.migration_percentage'));
    }

    /** @test */
    public function 마이그레이션_진행률_조회_테스트()
    {
        // Given
        Config::set('sse.migration.migration_percentage', 60);
        Config::set('sse.migration.hybrid_mode', true);
        Config::set('sse.migration.pusher_fallback_enabled', false);
        
        $hour = now()->format('Y-m-d-H');
        Cache::put("delivery_stats:sse:{$hour}", 80, 86400);
        Cache::put("delivery_stats:pusher:{$hour}", 20, 86400);

        // When
        $progress = $this->hybridService->getMigrationProgress();

        // Then
        $this->assertEquals(60, $progress['migration_percentage']);
        $this->assertEquals(80.0, $progress['actual_sse_usage']);
        $this->assertTrue($progress['hybrid_mode']);
        $this->assertFalse($progress['pusher_fallback']);
        $this->assertArrayHasKey('delivery_stats', $progress);
    }

    /** @test */
    public function 메시지_중복_방지_테스트()
    {
        // Given
        $data = ['message' => '중복 테스트'];
        $type = 'notification';
        
        Config::set('sse.migration.hybrid_mode', false);
        Config::set('sse.migration.migration_percentage', 100);
        
        // 첫 번째 호출은 성공해야 함
        $this->mockNotificationManager
            ->shouldReceive('sendToAll')
            ->once()
            ->with($data, $type);

        // When - 첫 번째 전송
        $this->hybridService->sendToAll($data, $type);
        
        // When - 동일한 메시지 재전송 (중복 방지로 인해 실제 전송되지 않음)
        $this->hybridService->sendToAll($data, $type);

        // Then
        // Mockery가 한 번만 호출되었는지 확인 (tearDown에서 검증됨)
        $this->assertTrue(true);
    }

    /** @test */
    public function SSE_실패시_Pusher_폴백_테스트()
    {
        // Given
        Config::set('sse.migration.hybrid_mode', false);
        Config::set('sse.migration.pusher_fallback_enabled', true);
        Config::set('sse.migration.migration_percentage', 100);
        
        $data = ['message' => '폴백 테스트'];
        $type = 'notification';
        
        // SSE 전송 실패 시뮬레이션
        $this->mockNotificationManager
            ->shouldReceive('sendToAll')
            ->once()
            ->with($data, $type)
            ->andThrow(new \Exception('SSE 전송 실패'));

        // When
        $this->hybridService->sendToAll($data, $type);

        // Then
        // 예외가 발생해도 애플리케이션이 중단되지 않아야 함
        $this->assertTrue(true);
    }

    /**
     * private 메서드 호출을 위한 헬퍼 메서드
     */
    private function invokePrivateMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}