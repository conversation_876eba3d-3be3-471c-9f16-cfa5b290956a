<?php

namespace Tests\Unit;

use App\Models\SseOfflineNotification;
use App\Models\User;
use App\Services\ConnectionManager;
use App\Services\MessageFormatterService;
use App\Services\NotificationManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Redis;
use Tests\TestCase;
use Mockery;
use Carbon\Carbon;

/**
 * NotificationManager 오프라인 알림 기능 단위 테스트
 */
class NotificationManagerOfflineTest extends TestCase
{
    use RefreshDatabase;

    private NotificationManager $notificationManager;
    private ConnectionManager $connectionManager;
    private MessageFormatterService $messageFormatter;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock 서비스 생성
        $this->connectionManager = Mockery::mock(ConnectionManager::class);
        $this->messageFormatter = Mockery::mock(MessageFormatterService::class);
        
        // NotificationManager 인스턴스 생성
        $this->notificationManager = new NotificationManager(
            $this->connectionManager,
            $this->messageFormatter
        );

        // 테스트용 사용자 생성
        $this->user = User::factory()->create();

        // Redis 모킹
        Redis::shouldReceive('publish')->andReturn(1)->byDefault();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 오프라인 사용자에게 알림 전송 시 데이터베이스 저장 테스트
     */
    public function test_stores_notification_for_offline_user(): void
    {
        // 사용자가 오프라인 상태로 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($this->user->id)
            ->andReturn([]);

        $notificationData = [
            'title' => '테스트 알림',
            'message' => '오프라인 사용자를 위한 알림입니다',
            'priority' => 'normal'
        ];

        // 알림 전송
        $result = $this->notificationManager->sendToUser(
            $this->user->id,
            $notificationData,
            'notification'
        );

        // 결과 검증
        $this->assertFalse($result['delivered_online']);
        $this->assertTrue($result['stored_offline']);
        $this->assertEquals(0, $result['successful_sends']);

        // 데이터베이스에 저장되었는지 확인
        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $this->user->id,
            'type' => 'notification',
            'delivered_at' => null
        ]);

        $notification = SseOfflineNotification::where('user_id', $this->user->id)->first();
        $this->assertEquals($notificationData, $notification->data);
    }

    /**
     * 온라인 사용자에게 알림 전송 시 실시간 전송 테스트
     */
    public function test_sends_notification_to_online_user(): void
    {
        $connectionIds = ['conn_1', 'conn_2'];
        
        // 사용자가 온라인 상태로 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($this->user->id)
            ->andReturn($connectionIds);

        // 메시지 포맷팅 모킹
        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->andReturn(['formatted' => 'message']);
        
        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->andReturn('serialized_message');

        $notificationData = [
            'title' => '실시간 알림',
            'message' => '온라인 사용자를 위한 알림입니다'
        ];

        // 알림 전송
        $result = $this->notificationManager->sendToUser(
            $this->user->id,
            $notificationData,
            'notification'
        );

        // 결과 검증
        $this->assertTrue($result['delivered_online']);
        $this->assertEquals(2, $result['successful_sends']);
        $this->assertEquals(0, $result['failed_sends']);

        // 데이터베이스에 저장되지 않았는지 확인
        $this->assertDatabaseMissing('sse_offline_notifications', [
            'user_id' => $this->user->id
        ]);
    }

    /**
     * 오프라인 알림 조회 테스트
     */
    public function test_gets_offline_notifications(): void
    {
        // 테스트 알림들 생성
        $notification1 = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'notification',
            'data' => ['title' => '첫 번째 알림'],
            'created_at' => Carbon::now()->subMinutes(10)
        ]);

        $notification2 = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'data_update',
            'data' => ['model' => 'categories'],
            'created_at' => Carbon::now()->subMinutes(5)
        ]);

        // 전달 완료된 알림 (조회되지 않아야 함)
        SseOfflineNotification::factory()->delivered()->create([
            'user_id' => $this->user->id
        ]);

        // 다른 사용자의 알림 (조회되지 않아야 함)
        SseOfflineNotification::factory()->create([
            'user_id' => User::factory()->create()->id
        ]);

        // 오프라인 알림 조회
        $notifications = $this->notificationManager->getOfflineNotifications($this->user->id);

        // 결과 검증
        $this->assertCount(2, $notifications);
        $this->assertEquals($notification1->id, $notifications[0]['id']);
        $this->assertEquals($notification2->id, $notifications[1]['id']);
        $this->assertEquals('notification', $notifications[0]['type']);
        $this->assertEquals('data_update', $notifications[1]['type']);
    }

    /**
     * 로그인 시 오프라인 알림 전송 테스트
     */
    public function test_delivers_offline_notifications_on_login(): void
    {
        // 미전달 알림들 생성
        $notification1 = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        $notification2 = SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        $connectionIds = ['conn_1'];
        
        // 사용자가 온라인 상태로 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($this->user->id)
            ->andReturn($connectionIds);

        // 메시지 포맷팅 모킹
        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->andReturn(['formatted' => 'message']);
        
        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->andReturn('serialized_message');

        // 오프라인 알림 전송
        $result = $this->notificationManager->deliverOfflineNotifications($this->user->id);

        // 결과 검증
        $this->assertEquals(2, $result['total_notifications']);
        $this->assertEquals(2, $result['delivered_count']);
        $this->assertEquals(0, $result['failed_count']);

        // 알림들이 전달 완료로 표시되었는지 확인
        $notification1->refresh();
        $notification2->refresh();
        
        $this->assertNotNull($notification1->delivered_at);
        $this->assertNotNull($notification2->delivered_at);
        $this->assertTrue($notification1->isDelivered());
        $this->assertTrue($notification2->isDelivered());
    }

    /**
     * 사용자가 오프라인일 때 오프라인 알림 전송 시도 테스트
     */
    public function test_handles_offline_user_during_notification_delivery(): void
    {
        // 미전달 알림 생성
        SseOfflineNotification::factory()->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 사용자가 오프라인 상태로 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($this->user->id)
            ->andReturn([]);

        // 오프라인 알림 전송 시도
        $result = $this->notificationManager->deliverOfflineNotifications($this->user->id);

        // 결과 검증
        $this->assertEquals(1, $result['total_notifications']);
        $this->assertEquals(0, $result['delivered_count']);
        $this->assertTrue($result['user_offline']);
    }

    /**
     * 오프라인 알림 수 제한 테스트
     */
    public function test_limits_offline_notifications_per_user(): void
    {
        // 최대 허용 수만큼 알림 생성 (1000개)
        SseOfflineNotification::factory()->count(1000)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 사용자가 오프라인 상태로 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($this->user->id)
            ->andReturn([]);

        $notificationData = [
            'title' => '새로운 알림',
            'message' => '제한 테스트용 알림'
        ];

        // 새 알림 전송 (제한 초과)
        $result = $this->notificationManager->sendToUser(
            $this->user->id,
            $notificationData,
            'notification'
        );

        // 결과 검증
        $this->assertTrue($result['stored_offline']);
        
        // 여전히 1000개만 유지되는지 확인
        $count = SseOfflineNotification::forUser($this->user->id)->undelivered()->count();
        $this->assertEquals(1000, $count);
    }

    /**
     * 전달 완료된 알림 정리 테스트
     */
    public function test_cleans_up_delivered_notifications(): void
    {
        $daysOld = 7;
        $cutoffDate = Carbon::now()->subDays($daysOld);

        // 정리 대상 알림들 (7일 이전에 전달 완료)
        SseOfflineNotification::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'delivered_at' => $cutoffDate->copy()->subDay()
        ]);

        // 정리 대상이 아닌 알림들 (최근에 전달 완료)
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => $cutoffDate->copy()->addDay()
        ]);

        // 미전달 알림들 (정리되지 않아야 함)
        SseOfflineNotification::factory()->count(2)->create([
            'user_id' => $this->user->id,
            'delivered_at' => null
        ]);

        // 정리 실행
        $deletedCount = $this->notificationManager->cleanupDeliveredNotifications($daysOld);

        // 결과 검증
        $this->assertEquals(3, $deletedCount);
        
        // 남은 알림 수 확인
        $remainingCount = SseOfflineNotification::forUser($this->user->id)->count();
        $this->assertEquals(4, $remainingCount); // 2개 최근 전달 완료 + 2개 미전달
    }

    /**
     * 빈 데이터로 오프라인 알림 조회 테스트
     */
    public function test_returns_empty_array_when_no_offline_notifications(): void
    {
        // 오프라인 알림 조회
        $notifications = $this->notificationManager->getOfflineNotifications($this->user->id);

        // 결과 검증
        $this->assertIsArray($notifications);
        $this->assertEmpty($notifications);
    }

    /**
     * 다중 사용자 알림 전송에서 오프라인 사용자 처리 테스트
     */
    public function test_handles_offline_users_in_batch_sending(): void
    {
        $onlineUser = User::factory()->create();
        $offlineUser = User::factory()->create();

        // 온라인/오프라인 사용자 설정
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($onlineUser->id)
            ->andReturn(['conn_1']);
            
        $this->connectionManager
            ->shouldReceive('getUserConnections')
            ->with($offlineUser->id)
            ->andReturn([]);

        // 메시지 포맷팅 모킹
        $this->messageFormatter
            ->shouldReceive('formatNotificationMessage')
            ->andReturn(['formatted' => 'message']);
        
        $this->messageFormatter
            ->shouldReceive('serializeMessage')
            ->andReturn('serialized_message');

        $notificationData = [
            'title' => '배치 알림',
            'message' => '다중 사용자 알림 테스트'
        ];

        // 다중 사용자 알림 전송
        $result = $this->notificationManager->sendToUsers(
            [$onlineUser->id, $offlineUser->id],
            $notificationData,
            'notification'
        );

        // 결과 검증
        $this->assertEquals(2, $result['total_users']);
        $this->assertEquals(1, $result['online_users']);
        $this->assertEquals(1, $result['offline_users']);
        $this->assertEquals(1, $result['stored_offline']);

        // 오프라인 사용자의 알림이 저장되었는지 확인
        $this->assertDatabaseHas('sse_offline_notifications', [
            'user_id' => $offlineUser->id,
            'delivered_at' => null
        ]);

        // 온라인 사용자의 알림은 저장되지 않았는지 확인
        $this->assertDatabaseMissing('sse_offline_notifications', [
            'user_id' => $onlineUser->id
        ]);
    }
}