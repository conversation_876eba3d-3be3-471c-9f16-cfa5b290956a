<?php

namespace Tests\Unit;

use App\Http\Middleware\SseSecurityMiddleware;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;
use Mockery;

/**
 * SSE 보안 미들웨어 단위 테스트
 * 
 * SseSecurityMiddleware의 개별 기능을 테스트합니다.
 */
class SseSecurityMiddlewareTest extends TestCase
{
    private SseSecurityMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->middleware = new SseSecurityMiddleware();
        Cache::flush();
    }

    protected function tearDown(): void
    {
        Cache::flush();
        Mockery::close();
        
        parent::tearDown();
    }

    /**
     * 정상적인 요청이 통과하는지 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 정상적인_요청이_미들웨어를_통과한다(): void
    {
        // Given: 정상적인 요청
        $request = Request::create('/sse/stream', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'HTTP_ACCEPT' => 'text/event-stream',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: 정상 통과
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('OK', $response->getContent());
    }

    /**
     * 의심스러운 User-Agent 차단 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 의심스러운_User_Agent가_차단된다(): void
    {
        // Given: 봇 User-Agent
        $request = Request::create('/sse/stream', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'GoogleBot/2.1',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: 차단됨
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('유효하지 않은 연결 요청입니다.', $responseData['message']);
    }

    /**
     * 빈 User-Agent 차단 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 빈_User_Agent가_차단된다(): void
    {
        // Given: 빈 User-Agent
        $request = Request::create('/sse/stream', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => '',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: 차단됨
        $this->assertEquals(400, $response->getStatusCode());
    }

    /**
     * IP 연결 제한 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function IP별_연결_제한이_작동한다(): void
    {
        // Given: IP 연결 수를 최대치로 설정
        $ip = '127.0.0.1';
        Cache::put("sse_ip_connections:{$ip}", 10, 3600); // 최대 10개

        $request = Request::create('/sse/stream', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'REMOTE_ADDR' => $ip
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: 연결 제한으로 차단
        $this->assertEquals(429, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('동일 IP에서 너무 많은 연결이 시도되었습니다.', $responseData['message']);
    }

    /**
     * 사용자별 연결 제한 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 사용자별_연결_제한이_작동한다(): void
    {
        // Given: 인증된 사용자와 최대 연결 수 설정
        $user = User::factory()->make(['id' => 1]);
        Auth::shouldReceive('check')->andReturn(true);
        Auth::shouldReceive('user')->andReturn($user);

        Cache::put("sse_user_connections:{$user->id}", 5, 3600); // 최대 5개

        $request = Request::create('/sse/stream', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: 사용자 연결 제한으로 차단
        $this->assertEquals(429, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('사용자당 최대 연결 수를 초과했습니다.', $responseData['message']);
    }

    /**
     * CSRF 토큰 검증 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function POST_요청시_CSRF_토큰이_검증된다(): void
    {
        // Given: CSRF 토큰이 없는 POST 요청
        $request = Request::create('/sse/connect', 'POST', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: CSRF 검증 실패로 차단
        $this->assertEquals(403, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('CSRF 토큰이 유효하지 않습니다.', $responseData['message']);
    }

    /**
     * 데이터 sanitization 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 요청_데이터가_sanitize된다(): void
    {
        // Given: 악성 스크립트가 포함된 요청
        $maliciousData = [
            'message' => '<script>alert("XSS")</script>Hello',
            'title' => 'Test & Title',
            'nested' => [
                'content' => '<img src="x" onerror="alert(1)">'
            ]
        ];

        $request = Request::create('/sse/connect', 'GET', $maliciousData, [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            // Then: 데이터가 sanitize되었는지 확인
            $this->assertEquals('Hello', $req->input('message')); // 스크립트 태그 제거
            $this->assertEquals('Test &amp; Title', $req->input('title')); // HTML 엔티티 인코딩
            $this->assertEquals('', $req->input('nested.content')); // 악성 태그 제거
            
            return new Response('OK', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * 연결 카운터 증가/감소 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 연결_카운터가_정상적으로_작동한다(): void
    {
        // Given: 초기 상태
        $ip = '127.0.0.1';
        $userId = 1;

        // When: 연결 카운터 증가
        $this->middleware->incrementIpConnections($ip);
        $this->middleware->incrementUserConnections($userId);

        // Then: 카운터 증가 확인
        $this->assertEquals(1, Cache::get("sse_ip_connections:{$ip}"));
        $this->assertEquals(1, Cache::get("sse_user_connections:{$userId}"));

        // When: 추가 증가
        $this->middleware->incrementIpConnections($ip);
        $this->middleware->incrementUserConnections($userId);

        // Then: 카운터 추가 증가 확인
        $this->assertEquals(2, Cache::get("sse_ip_connections:{$ip}"));
        $this->assertEquals(2, Cache::get("sse_user_connections:{$userId}"));

        // When: 연결 카운터 감소
        $this->middleware->decrementIpConnections($ip);
        $this->middleware->decrementUserConnections($userId);

        // Then: 카운터 감소 확인
        $this->assertEquals(1, Cache::get("sse_ip_connections:{$ip}"));
        $this->assertEquals(1, Cache::get("sse_user_connections:{$userId}"));

        // When: 0 이하로 감소 시도
        $this->middleware->decrementIpConnections($ip);
        $this->middleware->decrementUserConnections($userId);
        $this->middleware->decrementIpConnections($ip); // 추가 감소 시도

        // Then: 0 이하로 내려가지 않음
        $this->assertEquals(0, Cache::get("sse_ip_connections:{$ip}"));
        $this->assertEquals(0, Cache::get("sse_user_connections:{$userId}"));
    }

    /**
     * Accept 헤더 검증 테스트 (SSE 스트림)
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function SSE_스트림_요청시_Accept_헤더가_검증된다(): void
    {
        // Given: 잘못된 Accept 헤더
        $request = Request::create('/sse/stream', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'HTTP_ACCEPT' => 'application/json', // SSE가 아닌 Accept 헤더
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        });

        // Then: Accept 헤더 검증 실패로 차단
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('유효하지 않은 연결 요청입니다.', $responseData['message']);
    }

    /**
     * 올바른 Accept 헤더 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 올바른_Accept_헤더는_통과한다(): void
    {
        // Given: 올바른 Accept 헤더들
        $validAcceptHeaders = [
            'text/event-stream',
            'text/event-stream, application/json',
            '*/*',
            'text/*, */*'
        ];

        foreach ($validAcceptHeaders as $acceptHeader) {
            $request = Request::create('/sse/stream', 'GET', [], [], [], [
                'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'HTTP_ACCEPT' => $acceptHeader,
                'REMOTE_ADDR' => '127.0.0.1'
            ]);

            Session::start();
            $request->setLaravelSession(session());

            // When: 미들웨어 실행
            $response = $this->middleware->handle($request, function ($req) {
                return new Response('OK', 200);
            });

            // Then: 정상 통과
            $this->assertEquals(200, $response->getStatusCode(), 
                "Accept 헤더 '{$acceptHeader}'가 거부되었습니다.");
        }
    }

    /**
     * 긴 문자열 길이 제한 테스트
     * 
     * @test
     * @group sse
     * @group middleware
     */
    public function 긴_문자열이_제한된다(): void
    {
        // Given: 1000자를 초과하는 긴 문자열
        $longString = str_repeat('A', 1500);
        
        $request = Request::create('/sse/connect', 'GET', [
            'long_field' => $longString
        ], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'REMOTE_ADDR' => '127.0.0.1'
        ]);

        Session::start();
        $request->setLaravelSession(session());

        // When: 미들웨어 실행
        $response = $this->middleware->handle($request, function ($req) {
            // Then: 문자열이 1000자로 제한됨
            $this->assertEquals(1000, strlen($req->input('long_field')));
            $this->assertEquals(str_repeat('A', 1000), $req->input('long_field'));
            
            return new Response('OK', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }
}