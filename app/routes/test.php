<?php

use Illuminate\Support\Facades\Route;
use Tests\Feature\SseTestController;

/*
|--------------------------------------------------------------------------
| SSE E2E 테스트 라우트
|--------------------------------------------------------------------------
|
| 이 라우트들은 SSE E2E 테스트를 위한 테스트용 엔드포인트입니다.
| 테스트 및 로컬 환경에서만 사용되며, 프로덕션에서는 비활성화됩니다.
|
*/

// 테스트 환경에서만 라우트 등록
if (app()->environment(['testing', 'local'])) {
    
    Route::prefix('api/test')->group(function () {
        
        // 알림 전송 테스트
        Route::post('/send-notification', [SseTestController::class, 'sendNotification'])
            ->name('test.send-notification');
        
        // 대용량 알림 전송 테스트
        Route::post('/send-large-notification', [SseTestController::class, 'sendLargeNotification'])
            ->name('test.send-large-notification');
        
        // 카테고리 생성 테스트
        Route::post('/create-category', [SseTestController::class, 'createCategory'])
            ->name('test.create-category');
        
        // 개별 사용자 알림 전송 테스트
        Route::post('/send-user-notification', [SseTestController::class, 'sendUserNotification'])
            ->name('test.send-user-notification');
        
        // 연결 상태 확인
        Route::get('/connection-status', [SseTestController::class, 'getConnectionStatus'])
            ->name('test.connection-status');
        
        // 하트비트 전송 테스트
        Route::post('/send-heartbeat', [SseTestController::class, 'sendHeartbeat'])
            ->name('test.send-heartbeat');
        
        // 배치 메시지 전송 테스트
        Route::post('/send-batch-messages', [SseTestController::class, 'sendBatchMessages'])
            ->name('test.send-batch-messages');
        
        // 오류 시뮬레이션
        Route::post('/simulate-error', [SseTestController::class, 'simulateError'])
            ->name('test.simulate-error');
        
        // 성능 메트릭 수집
        Route::get('/performance-metrics', [SseTestController::class, 'getPerformanceMetrics'])
            ->name('test.performance-metrics');
        
        // 테스트 환경 초기화
        Route::post('/reset-environment', [SseTestController::class, 'resetTestEnvironment'])
            ->name('test.reset-environment');
    });
    
    // 테스트용 SSE 스트림 (디버깅용)
    Route::get('/api/test/sse-debug', function () {
        return response()->stream(function () {
            echo "data: " . json_encode([
                'type' => 'debug',
                'message' => 'SSE 디버그 스트림이 시작되었습니다.',
                'timestamp' => now()->toISOString()
            ]) . "\n\n";
            
            // 10초간 1초마다 디버그 메시지 전송
            for ($i = 1; $i <= 10; $i++) {
                sleep(1);
                echo "data: " . json_encode([
                    'type' => 'debug',
                    'message' => "디버그 메시지 {$i}",
                    'sequence' => $i,
                    'timestamp' => now()->toISOString()
                ]) . "\n\n";
                
                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }
            
            echo "data: " . json_encode([
                'type' => 'debug',
                'message' => 'SSE 디버그 스트림이 종료되었습니다.',
                'timestamp' => now()->toISOString()
            ]) . "\n\n";
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no', // Nginx 버퍼링 비활성화
        ]);
    })->name('test.sse-debug');
}