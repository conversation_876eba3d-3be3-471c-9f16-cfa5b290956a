<?php

use App\Http\Controllers\Api\SSE\HybridNotificationController;
use App\Http\Controllers\Api\SSE\MigrationDashboardController;
use App\Http\Controllers\Api\SSE\SseController;
use App\Http\Controllers\Api\SSE\SseMonitoringController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// 공개 상태 확인 엔드포인트 (인증 불필요)
Route::get('/health', [SseMonitoringController::class, 'healthCheck'])
    ->name('health.check');

/**
 * SSE (Server-Sent Events) 라우트
 * 실시간 알림 및 데이터 전송을 위한 SSE 연결 관리
 */
Route::name('sse.')->prefix('sse')->middleware('sse.security')->group(function () {
    // SSE 스트림 연결 (GET 요청, 인증 선택적)
    Route::get('/stream', [SseController::class, 'stream'])->name('stream');

    // SSE 연결 관리 (POST 요청, 보안 검증 필요)
    Route::post('/connect', [SseController::class, 'connect'])->name('connect');
    Route::post('/disconnect', [SseController::class, 'disconnect'])->name('disconnect');
});

// SSE 모니터링 API 라우트
Route::prefix('sse')->middleware('auth:sanctum')->group(function () {
    // 실시간 모니터링 대시보드
    Route::get('/monitoring/dashboard', [SseMonitoringController::class, 'dashboard']);

    // 기간별 통계
    Route::get('/monitoring/statistics', [SseMonitoringController::class, 'statistics']);

    // 시스템 알림
    Route::get('/monitoring/alerts', [SseMonitoringController::class, 'alerts']);

    // 시스템 상태
    Route::get('/monitoring/health', [SseMonitoringController::class, 'health']);

    // 실시간 연결 상태
    Route::get('/monitoring/connections', [SseMonitoringController::class, 'connections']);

    // 메시지 통계
    Route::get('/monitoring/messages', [SseMonitoringController::class, 'messages']);

    // 관리 기능 (관리자만 접근 가능)
    Route::middleware('can:admin')->group(function () {
        // 연결 통계 수동 기록
        Route::post('/monitoring/record-connection', [SseMonitoringController::class, 'recordConnection']);

        // 메시지 통계 수동 기록
        Route::post('/monitoring/record-message', [SseMonitoringController::class, 'recordMessage']);

        // 오래된 데이터 정리
        Route::delete('/monitoring/cleanup', [SseMonitoringController::class, 'cleanup']);
    });
});

// 하이브리드 알림 시스템 API 라우트
Route::prefix('hybrid-notifications')->group(function () {
    // 공개 라우트 (인증 불필요)
    Route::post('/sse-support', [HybridNotificationController::class, 'updateSSESupport'])
        ->middleware(['sse.capability.detection']);

    Route::get('/system-status', [HybridNotificationController::class, 'getSystemStatus']);

    // 인증 필요 라우트
    Route::middleware('auth:sanctum')->group(function () {
        // 알림 전송
        Route::post('/send', [HybridNotificationController::class, 'sendNotification']);

        // 마이그레이션 진행 상황
        Route::get('/migration/progress', [HybridNotificationController::class, 'getMigrationProgress']);

        // 메시지 동기화 상태 확인
        Route::get('/sync/status', [HybridNotificationController::class, 'checkMessageSync']);

        // 동기화 통계
        Route::get('/sync/stats', [HybridNotificationController::class, 'getSyncStats']);

        // 관리자 전용 기능
        Route::middleware('can:admin')->group(function () {
            // 하이브리드 설정 업데이트
            Route::put('/settings', [HybridNotificationController::class, 'updateSettings']);
        });
    });
});

// 마이그레이션 대시보드 API 라우트
Route::prefix('migration-dashboard')->middleware('auth:sanctum')->group(function () {
    // 대시보드 메인 데이터
    Route::get('/dashboard', [MigrationDashboardController::class, 'dashboard']);

    // 상세 리포트
    Route::get('/detailed-report', [MigrationDashboardController::class, 'detailedReport']);

    // 실시간 통계
    Route::get('/realtime-stats', [MigrationDashboardController::class, 'realtimeStats']);

    // SSE 전환율 기록
    Route::post('/record-conversion', [MigrationDashboardController::class, 'recordConversion']);

    // 관리자 전용 기능
    Route::middleware('can:admin')->group(function () {
        // Pusher 제거 체크리스트
        Route::get('/removal-checklist', [MigrationDashboardController::class, 'removalChecklist']);

        // 알림 설정 업데이트
        Route::put('/alert-settings', [MigrationDashboardController::class, 'updateAlertSettings']);
    });
});

/*
|--------------------------------------------------------------------------
| SSE 모니터링 라우트
|--------------------------------------------------------------------------
|
| SSE 시스템의 실시간 모니터링 및 성능 메트릭을 위한 라우트입니다.
| 관리자 권한이 필요한 엔드포인트들입니다.
|
*/

Route::prefix('monitoring')->middleware('auth:sanctum')->group(function () {

    // 실시간 모니터링 대시보드
    Route::get('/dashboard', [SseMonitoringController::class, 'dashboard'])
        ->name('monitoring.dashboard');

    // 연결 통계
    Route::get('/connections', [SseMonitoringController::class, 'connectionStats'])
        ->name('monitoring.connections');

    // 메시지 통계
    Route::get('/messages', [SseMonitoringController::class, 'messageStats'])
        ->name('monitoring.messages');

    // 성능 메트릭
    Route::get('/performance', [SseMonitoringController::class, 'performanceMetrics'])
        ->name('monitoring.performance');

    // 시스템 상태 확인
    Route::get('/health', [SseMonitoringController::class, 'healthCheck'])
        ->name('monitoring.health');

    // 알림 및 경고
    Route::get('/alerts', [SseMonitoringController::class, 'alerts'])
        ->name('monitoring.alerts');

    // 실시간 로그 스트림
    Route::get('/logs/stream', [SseMonitoringController::class, 'logStream'])
        ->name('monitoring.logs.stream');
});
