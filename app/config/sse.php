<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SSE (Server-Sent Events) 설정
    |--------------------------------------------------------------------------
    |
    | SSE 실시간 알림 시스템의 기본 설정값들을 정의합니다.
    | Redis 키 네임스페이스, 연결 제한, 하트비트 간격 등을 설정할 수 있습니다.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Redis 키 네임스페이스
    |--------------------------------------------------------------------------
    |
    | SSE 관련 Redis 키들의 네임스페이스를 정의합니다.
    | 다른 시스템과의 키 충돌을 방지하기 위해 고유한 접두사를 사용합니다.
    |
    */
    'redis' => [
        'prefix' => env('SSE_REDIS_PREFIX', 'sse:'),
        'connection' => env('SSE_REDIS_CONNECTION', 'default'),
        'key_ttl' => env('SSE_REDIS_KEY_TTL', 3600), // 1시간 (초 단위)
    ],

    /*
    |--------------------------------------------------------------------------
    | 연결 관리 설정
    |--------------------------------------------------------------------------
    |
    | SSE 연결의 최대 개수, 사용자별 연결 제한, 연결 타임아웃 등을 설정합니다.
    |
    */
    'connections' => [
        'max_total' => env('SSE_MAX_TOTAL_CONNECTIONS', 1000),
        'max_per_user' => env('SSE_MAX_CONNECTIONS_PER_USER', 5),
        'max_per_ip' => env('SSE_MAX_CONNECTIONS_PER_IP', 10),
        'timeout' => env('SSE_CONNECTION_TIMEOUT', 300), // 5분 (초 단위)
        'heartbeat_interval' => env('SSE_HEARTBEAT_INTERVAL', 30), // 30초
    ],

    /*
    |--------------------------------------------------------------------------
    | 메시지 전송 설정
    |--------------------------------------------------------------------------
    |
    | 메시지 배치 처리, 재시도 정책, 압축 설정 등을 정의합니다.
    |
    */
    'messages' => [
        'batch_size' => env('SSE_MESSAGE_BATCH_SIZE', 100),
        'retry_attempts' => env('SSE_MESSAGE_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('SSE_MESSAGE_RETRY_DELAY', 1000), // 1초 (밀리초 단위)
        'max_message_size' => env('SSE_MAX_MESSAGE_SIZE', 65536), // 64KB
        'compression_enabled' => env('SSE_COMPRESSION_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | 오프라인 알림 설정
    |--------------------------------------------------------------------------
    |
    | 오프라인 사용자를 위한 알림 저장 및 정리 정책을 설정합니다.
    |
    */
    'offline_notifications' => [
        'enabled' => env('SSE_OFFLINE_NOTIFICATIONS_ENABLED', true),
        'max_per_user' => env('SSE_MAX_OFFLINE_NOTIFICATIONS_PER_USER', 100),
        'retention_days' => env('SSE_OFFLINE_NOTIFICATIONS_RETENTION_DAYS', 7),
        'cleanup_interval' => env('SSE_OFFLINE_NOTIFICATIONS_CLEANUP_INTERVAL', 3600), // 1시간
    ],

    /*
    |--------------------------------------------------------------------------
    | 보안 설정
    |--------------------------------------------------------------------------
    |
    | Rate limiting, CSRF 보호, IP 화이트리스트 등 보안 관련 설정입니다.
    |
    */
    'security' => [
        'rate_limit_per_minute' => env('SSE_RATE_LIMIT_PER_MINUTE', 60),
        'csrf_protection' => env('SSE_CSRF_PROTECTION', true),
        'ip_whitelist' => env('SSE_IP_WHITELIST', ''),
        'require_authentication' => env('SSE_REQUIRE_AUTHENTICATION', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | 모니터링 및 로깅 설정
    |--------------------------------------------------------------------------
    |
    | 통계 수집, 로그 레벨, 성능 모니터링 등의 설정입니다.
    |
    */
    'monitoring' => [
        'statistics_enabled' => env('SSE_STATISTICS_ENABLED', true),
        'log_level' => env('SSE_LOG_LEVEL', 'info'),
        'performance_tracking' => env('SSE_PERFORMANCE_TRACKING', true),
        'error_reporting' => env('SSE_ERROR_REPORTING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Pusher 마이그레이션 설정
    |--------------------------------------------------------------------------
    |
    | 기존 Pusher 시스템과의 병행 운영 및 점진적 마이그레이션을 위한 설정입니다.
    |
    */
    'migration' => [
        'pusher_fallback_enabled' => env('SSE_PUSHER_FALLBACK_ENABLED', false),
        'hybrid_mode' => env('SSE_HYBRID_MODE', false),
        'migration_percentage' => env('SSE_MIGRATION_PERCENTAGE', 0), // 0-100%
    ],
];