upstream laravel_backend {
    # docker-compose의 서비스 이름 사용 (PHP-FPM은 9000 포트)
    server cnsprowms-api:9000;
    keepalive 32;
}

upstream sse_backend {
    # SSE용 업스트림 - 세션 유지를 위해 ip_hash 사용
    ip_hash;
    server cnsprowms-api:9000;
    keepalive 64;
    keepalive_requests 1000;
    keepalive_timeout 60s;
}

server {
    listen 80;
    server_name cnsprowms-api.test;

    root /var/www/html/public;
    index index.php index.html;
    charset UTF-8;

    # 연결 제한
    limit_conn conn_limit_per_ip 20;

    # SSE 엔드포인트 설정
    location = /api/sse/stream {
        # Rate limiting (SSE 연결용)
        limit_req zone=sse burst=10 nodelay;

        # index.php로 직접 전달 (파일 존재 검사로 404 내지 않음)
        fastcgi_pass sse_backend;
        fastcgi_index index.php;

        # 반드시 index.php를 실행 대상으로 지정
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        fastcgi_param QUERY_STRING     $query_string;
        fastcgi_param REQUEST_URI      $request_uri;
        fastcgi_param PATH_INFO        "";
        fastcgi_param DOCUMENT_URI     /index.php;

        # 프록시 전달 헤더 유지
        fastcgi_param HTTP_HOST $host;
        fastcgi_param HTTP_X_REAL_IP $remote_addr;
        fastcgi_param HTTP_X_FORWARDED_FOR $proxy_add_x_forwarded_for;
        fastcgi_param HTTP_X_FORWARDED_PROTO $scheme;

        # 캐시 비활성화
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";

        # CORS (필요 시)
        add_header Access-Control-Allow-Origin $cors_origin always;
        add_header Access-Control-Allow-Credentials "true" always;

        # FastCGI 버퍼링 비활성화 (실시간 스트리밍)
        fastcgi_buffering off;
        fastcgi_request_buffering off;

        # 타임아웃 (장시간 연결 유지)
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 3600s;
        fastcgi_read_timeout 3600s;

        # SSE 전용 로그
        access_log /var/log/nginx/sse-access.log sse_log;
        error_log  /var/log/nginx/sse-error.log;
    }

    # 클라이언트 설정
    client_max_body_size     10m;
    client_body_buffer_size  4m;
    client_body_temp_path    /tmp;

    # 정적 파일 처리
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;

        # 압축 적용
        gzip_static on;
        brotli_static on;
    }

    # 일반 API 엔드포인트 (SSE 제외)
    location /api/ {
        limit_req zone=api burst=20 nodelay;

        include /etc/nginx/snippets/cors_headers.conf;
        include /etc/nginx/snippets/cors_preflight.conf;

        try_files $uri $uri/ /index.php?$query_string;
    }

    # WMS 경로 처리 (동적 CORS 포함)
    location /wms/ {
        include /etc/nginx/snippets/cors_headers.conf;
        include /etc/nginx/snippets/cors_preflight.conf;

        try_files $uri $uri/ /index.php?$query_string;
    }


    # 메인 라우팅 (Laravel)
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP 파일 처리 (통합)
    location ~ \.php$ {
        try_files $uri =404;

        fastcgi_pass laravel_backend;
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;

        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        # 프록시 전달 헤더 유지
        fastcgi_param HTTP_HOST $host;
        fastcgi_param HTTP_X_REAL_IP $remote_addr;
        fastcgi_param HTTP_X_FORWARDED_FOR $proxy_add_x_forwarded_for;
        fastcgi_param HTTP_X_FORWARDED_PROTO $scheme;

        # 타임아웃/버퍼
        fastcgi_connect_timeout 30s;
        fastcgi_send_timeout 30s;
        fastcgi_read_timeout 300s;
        fastcgi_buffers 64 16k;

        # 여기가 중요: 최종 응답 위치에서도 CORS 헤더를 설정
        # (Origin이 허용 목록에 있을 때만 반영됨)
        include /etc/nginx/snippets/cors_headers.conf;
    }


    # error_page 403 /403.html;
    # error_page 404 /404.html;
    # error_page 405 /405.html;

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    location ~ /\. {
        deny  all;
    }

    location ~* /(?:uploads|files|data|tmp)/.*\.php$ {
        deny all;
    }

    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }

    location ~* ^.+\.(ogg|ogv|svg|svgz|eot|otf|woff|mp4|ttf|rss|atom|jpg|jpeg|gif|png|ico|zip|tgz|gz|rar|bz2|doc|xls|xlsx|exe|ppt|tar|mid|midi|wav|bmp|rtf)$ {
        access_log off;
        log_not_found off;
        expires max;
    }
}