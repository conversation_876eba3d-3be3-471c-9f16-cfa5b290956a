name: csp-api

services:
  mariadb:
    image: mariadb:11.4
    container_name: mariadb
    restart: unless-stopped
    ports:
      - "30001:3306"
    volumes:
      - ./conf/mysql/my-11.4-8G.cnf:/etc/mysql/conf.d/my-11.4-8G.cnf
      - ./logs/mariadb:/var/log/mysql
      - /Data/api_mariadb:/var/lib/mysql
    environment:
      - TZ=${TIME_ZONE}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
    networks:
      - default

  redis:
    image: redis:alpine
    container_name: redis
    restart: unless-stopped
    volumes:
      - /Data/api_redis:/data
    environment:
      - TZ=${TIME_ZONE}
    command: redis-server
    networks:
      - default

  memcached:
    image: memcached:alpine
    container_name: memcached
    restart: unless-stopped
    networks:
      - default

  meilisearch:
    image: getmeili/meilisearch:v1.15
    container_name: meilisearch
    restart: unless-stopped
    ports:
      - "30002:7700"
    volumes:
      - /Data/api_meilisearch:/meili_data
    environment:
      - TZ=${TIME_ZONE}
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY}
    networks:
      - default

  # PHP 이미지
  laravel:
    container_name: cnsprowms-api
    build:
      args:
        user: ec2-user
        uid: 1000
      context: .
      dockerfile: Dockerfile
    working_dir: /var/www/html
    volumes:
      - ./app:/var/www/html
      - ./conf/php/php.ini:/usr/local/etc/php/php.ini
      - ./conf/php/www.conf:/usr/local/etc/php-fpm.d/www.conf
      - ./conf/php/opcache.ini:/usr/local/etc/php/conf.d/opcache-recommended.ini
      - ./conf/php/memcached.ini:/usr/local/etc/php/conf.d/memcached-recommended.ini
      - ./logs/supervisor/:/var/log/supervisor
    restart: unless-stopped
    environment:
      - TZ=${TIME_ZONE}
      - MEILISEARCH_HOST=http://meilisearch:7700
      - MEILISEARCH_KEY=${MEILI_MASTER_KEY}
    depends_on:
      - mariadb
      - redis
      - memcached
      - meilisearch
    networks:
      - default

  # PHP API 웹서버
  webserver:
    container_name: cnsprowms-web
    image: nginx:stable-alpine
    environment:
      - TZ=Asia/Seoul
    volumes:
      - ./app:/var/www/html
      - ./conf/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./conf/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./conf/nginx/snippets/cors_headers.conf:/etc/nginx/snippets/cors_headers.conf
      - ./conf/nginx/snippets/cors_preflight.conf:/etc/nginx/snippets/cors_preflight.conf
      - ./logs/nginx:/var/log/nginx
    restart: unless-stopped
    depends_on:
      - laravel
    networks:
      - default
      - npm-network

networks:
  npm-network:
    external: true
